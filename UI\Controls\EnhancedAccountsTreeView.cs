using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.UI.Controls
{
    /// <summary>
    /// عنصر تحكم شجرة الحسابات المحسن والتفاعلي
    /// Enhanced and Interactive Accounts TreeView Control
    /// </summary>
    public partial class EnhancedAccountsTreeView : UserControl
    {
        #region الأحداث - Events
        
        /// <summary>
        /// حدث تحديد حساب
        /// Account Selected Event
        /// </summary>
        public event EventHandler<AccountSelectedEventArgs> AccountSelected;

        /// <summary>
        /// حدث النقر المزدوج على حساب
        /// Account Double Clicked Event
        /// </summary>
        public event EventHandler<AccountSelectedEventArgs> AccountDoubleClicked;

        /// <summary>
        /// حدث النقر بالزر الأيمن على حساب
        /// Account Right Clicked Event
        /// </summary>
        public event EventHandler<AccountSelectedEventArgs> AccountRightClicked;

        /// <summary>
        /// حدث تغيير البحث
        /// Search Changed Event
        /// </summary>
        public event EventHandler<string> SearchChanged;

        #endregion

        #region الحقول الخاصة - Private Fields
        
        private TreeView treeViewAccounts;
        private TextBox textBoxSearch;
        private ComboBox comboBoxFilter;
        private Button buttonExpandAll;
        private Button buttonCollapseAll;
        private Button buttonRefresh;
        private Label labelAccountsCount;
        private ContextMenuStrip contextMenuAccount;
        
        private List<ChartOfAccount> _allAccounts;
        private List<ChartOfAccount> _filteredAccounts;
        private ChartOfAccount _selectedAccount;
        private Timer _searchTimer;
        
        // ألوان التصميم
        private readonly Color _primaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color _secondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color _successColor = Color.FromArgb(39, 174, 96);
        private readonly Color _warningColor = Color.FromArgb(241, 196, 15);
        private readonly Color _dangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color _lightGray = Color.FromArgb(236, 240, 241);

        #endregion

        #region الخصائص - Properties
        
        /// <summary>
        /// الحساب المحدد حالياً
        /// Currently Selected Account
        /// </summary>
        public ChartOfAccount SelectedAccount
        {
            get => _selectedAccount;
            private set
            {
                _selectedAccount = value;
                OnAccountSelected(new AccountSelectedEventArgs(value));
            }
        }

        /// <summary>
        /// قائمة جميع الحسابات
        /// All Accounts List
        /// </summary>
        public List<ChartOfAccount> AllAccounts
        {
            get => _allAccounts;
            set
            {
                _allAccounts = value ?? new List<ChartOfAccount>();
                RefreshTreeView();
            }
        }

        /// <summary>
        /// نص البحث الحالي
        /// Current Search Text
        /// </summary>
        public string SearchText => textBoxSearch?.Text ?? "";

        /// <summary>
        /// نوع الفلتر المحدد
        /// Selected Filter Type
        /// </summary>
        public string SelectedFilter => comboBoxFilter?.SelectedItem?.ToString() ?? "الكل";

        /// <summary>
        /// عدد الحسابات المعروضة
        /// Displayed Accounts Count
        /// </summary>
        public int DisplayedAccountsCount => _filteredAccounts?.Count ?? 0;

        #endregion

        #region المنشئ - Constructor
        
        /// <summary>
        /// منشئ عنصر التحكم
        /// Control Constructor
        /// </summary>
        public EnhancedAccountsTreeView()
        {
            InitializeComponent();
            InitializeControls();
            SetupEventHandlers();
            ApplyModernStyling();
            
            _allAccounts = new List<ChartOfAccount>();
            _filteredAccounts = new List<ChartOfAccount>();
            
            // إعداد مؤقت البحث
            _searchTimer = new Timer { Interval = 300 };
            _searchTimer.Tick += SearchTimer_Tick;
        }

        #endregion

        #region تهيئة العناصر - Initialize Controls
        
        /// <summary>
        /// تهيئة عناصر التحكم
        /// Initialize Controls
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد الحاوي الرئيسي
            this.Size = new Size(400, 600);
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            
            this.ResumeLayout(false);
        }

        /// <summary>
        /// تهيئة العناصر الفرعية
        /// Initialize Sub Controls
        /// </summary>
        private void InitializeControls()
        {
            // شريط البحث والفلترة
            CreateSearchAndFilterPanel();
            
            // شجرة الحسابات
            CreateTreeView();
            
            // شريط الحالة
            CreateStatusPanel();
            
            // القائمة السياقية
            CreateContextMenu();
        }

        /// <summary>
        /// إنشاء شريط البحث والفلترة
        /// Create Search and Filter Panel
        /// </summary>
        private void CreateSearchAndFilterPanel()
        {
            var panel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = _lightGray,
                Padding = new Padding(10)
            };

            // مربع البحث
            textBoxSearch = new TextBox
            {
                Location = new Point(10, 10),
                Width = 200,
                Height = 25,
                PlaceholderText = "البحث في الحسابات...",
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            // قائمة الفلترة
            comboBoxFilter = new ComboBox
            {
                Location = new Point(220, 10),
                Width = 120,
                Height = 25,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };
            comboBoxFilter.Items.AddRange(new[] { "الكل", "نشط", "غير نشط", "حسابات أب", "حسابات فرعية" });
            comboBoxFilter.SelectedIndex = 0;

            // أزرار التحكم
            buttonExpandAll = new Button
            {
                Location = new Point(10, 45),
                Size = new Size(80, 25),
                Text = "توسيع الكل",
                Font = new Font("Segoe UI", 8F),
                BackColor = _primaryColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonCollapseAll = new Button
            {
                Location = new Point(100, 45),
                Size = new Size(80, 25),
                Text = "طي الكل",
                Font = new Font("Segoe UI", 8F),
                BackColor = _secondaryColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonRefresh = new Button
            {
                Location = new Point(190, 45),
                Size = new Size(60, 25),
                Text = "تحديث",
                Font = new Font("Segoe UI", 8F),
                BackColor = _successColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            panel.Controls.AddRange(new Control[] 
            { 
                textBoxSearch, comboBoxFilter, 
                buttonExpandAll, buttonCollapseAll, buttonRefresh 
            });
            
            this.Controls.Add(panel);
        }

        /// <summary>
        /// إنشاء شجرة الحسابات
        /// Create TreeView
        /// </summary>
        private void CreateTreeView()
        {
            treeViewAccounts = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                HideSelection = false,
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                FullRowSelect = true,
                HotTracking = true,
                ItemHeight = 22,
                BackColor = Color.White,
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            this.Controls.Add(treeViewAccounts);
        }

        /// <summary>
        /// إنشاء شريط الحالة
        /// Create Status Panel
        /// </summary>
        private void CreateStatusPanel()
        {
            var panel = new Panel
            {
                Height = 30,
                Dock = DockStyle.Bottom,
                BackColor = _lightGray
            };

            labelAccountsCount = new Label
            {
                Location = new Point(10, 5),
                AutoSize = true,
                Text = "عدد الحسابات: 0",
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.FromArgb(127, 140, 141)
            };

            panel.Controls.Add(labelAccountsCount);
            this.Controls.Add(panel);
        }

        /// <summary>
        /// إنشاء القائمة السياقية
        /// Create Context Menu
        /// </summary>
        private void CreateContextMenu()
        {
            contextMenuAccount = new ContextMenuStrip();
            contextMenuAccount.RightToLeft = RightToLeft.Yes;

            var menuItemView = new ToolStripMenuItem("عرض التفاصيل", null, ContextMenu_ViewDetails);
            var menuItemEdit = new ToolStripMenuItem("تعديل", null, ContextMenu_Edit);
            var menuItemAddChild = new ToolStripMenuItem("إضافة حساب فرعي", null, ContextMenu_AddChild);
            var menuItemSeparator = new ToolStripSeparator();
            var menuItemDelete = new ToolStripMenuItem("حذف", null, ContextMenu_Delete);

            contextMenuAccount.Items.AddRange(new ToolStripItem[]
            {
                menuItemView, menuItemEdit, menuItemAddChild, menuItemSeparator, menuItemDelete
            });

            treeViewAccounts.ContextMenuStrip = contextMenuAccount;
        }

        #endregion

        #region معالجة الأحداث - Event Handlers
        
        /// <summary>
        /// إعداد معالجات الأحداث
        /// Setup Event Handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث البحث والفلترة
            textBoxSearch.TextChanged += TextBoxSearch_TextChanged;
            comboBoxFilter.SelectedIndexChanged += ComboBoxFilter_SelectedIndexChanged;

            // أحداث الأزرار
            buttonExpandAll.Click += ButtonExpandAll_Click;
            buttonCollapseAll.Click += ButtonCollapseAll_Click;
            buttonRefresh.Click += ButtonRefresh_Click;

            // أحداث شجرة الحسابات
            treeViewAccounts.AfterSelect += TreeViewAccounts_AfterSelect;
            treeViewAccounts.NodeMouseDoubleClick += TreeViewAccounts_NodeMouseDoubleClick;
            treeViewAccounts.NodeMouseClick += TreeViewAccounts_NodeMouseClick;
        }

        private void TextBoxSearch_TextChanged(object sender, EventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private void SearchTimer_Tick(object sender, EventArgs e)
        {
            _searchTimer.Stop();
            ApplyFilters();
            OnSearchChanged(textBoxSearch.Text);
        }

        private void ComboBoxFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ButtonExpandAll_Click(object sender, EventArgs e)
        {
            treeViewAccounts.ExpandAll();
        }

        private void ButtonCollapseAll_Click(object sender, EventArgs e)
        {
            treeViewAccounts.CollapseAll();
        }

        private void ButtonRefresh_Click(object sender, EventArgs e)
        {
            RefreshTreeView();
        }

        private void TreeViewAccounts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is ChartOfAccount account)
            {
                SelectedAccount = account;
            }
        }

        private void TreeViewAccounts_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node?.Tag is ChartOfAccount account)
            {
                OnAccountDoubleClicked(new AccountSelectedEventArgs(account));
            }
        }

        private void TreeViewAccounts_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Button == MouseButtons.Right && e.Node?.Tag is ChartOfAccount account)
            {
                treeViewAccounts.SelectedNode = e.Node;
                OnAccountRightClicked(new AccountSelectedEventArgs(account));
            }
        }

        #endregion

        #region معالجة القائمة السياقية - Context Menu Handlers

        private void ContextMenu_ViewDetails(object sender, EventArgs e)
        {
            if (SelectedAccount != null)
            {
                OnAccountDoubleClicked(new AccountSelectedEventArgs(SelectedAccount));
            }
        }

        private void ContextMenu_Edit(object sender, EventArgs e)
        {
            if (SelectedAccount != null)
            {
                // سيتم تنفيذ هذا في النموذج الرئيسي
                OnAccountDoubleClicked(new AccountSelectedEventArgs(SelectedAccount));
            }
        }

        private void ContextMenu_AddChild(object sender, EventArgs e)
        {
            if (SelectedAccount != null)
            {
                // إنشاء حساب فرعي جديد
                var newAccount = new ChartOfAccount
                {
                    ParentAccountId = SelectedAccount.AccountId,
                    AccountTypeId = SelectedAccount.AccountTypeId,
                    AccountGroupId = SelectedAccount.AccountGroupId
                };
                OnAccountSelected(new AccountSelectedEventArgs(newAccount));
            }
        }

        private void ContextMenu_Delete(object sender, EventArgs e)
        {
            if (SelectedAccount != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب '{SelectedAccount.AccountNameAr}'؟",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button2,
                    MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    // سيتم تنفيذ الحذف في النموذج الرئيسي
                    OnAccountRightClicked(new AccountSelectedEventArgs(SelectedAccount));
                }
            }
        }

        #endregion

        #region الطرق الأساسية - Core Methods

        /// <summary>
        /// تحديث شجرة الحسابات
        /// Refresh TreeView
        /// </summary>
        public void RefreshTreeView()
        {
            try
            {
                treeViewAccounts.BeginUpdate();
                treeViewAccounts.Nodes.Clear();

                ApplyFilters();
                BuildTreeStructure();
                UpdateAccountsCount();

                treeViewAccounts.EndUpdate();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث شجرة الحسابات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// تطبيق الفلاتر
        /// Apply Filters
        /// </summary>
        private void ApplyFilters()
        {
            if (_allAccounts == null)
            {
                _filteredAccounts = new List<ChartOfAccount>();
                return;
            }

            var searchText = textBoxSearch.Text.Trim().ToLower();
            var filterType = comboBoxFilter.SelectedItem?.ToString() ?? "الكل";

            _filteredAccounts = _allAccounts.Where(account =>
            {
                // فلتر البحث
                var matchesSearch = string.IsNullOrEmpty(searchText) ||
                    account.AccountNameAr.ToLower().Contains(searchText) ||
                    account.AccountNameEn?.ToLower().Contains(searchText) == true ||
                    account.AccountCode.ToLower().Contains(searchText) ||
                    account.Description?.ToLower().Contains(searchText) == true;

                // فلتر النوع
                var matchesFilter = filterType switch
                {
                    "نشط" => account.IsActive,
                    "غير نشط" => !account.IsActive,
                    "حسابات أب" => account.IsParent,
                    "حسابات فرعية" => !account.IsParent,
                    _ => true
                };

                return matchesSearch && matchesFilter;
            }).ToList();
        }

        /// <summary>
        /// بناء هيكل الشجرة
        /// Build Tree Structure
        /// </summary>
        private void BuildTreeStructure()
        {
            if (_filteredAccounts == null || !_filteredAccounts.Any())
                return;

            // إنشاء قاموس للعقد للوصول السريع
            var nodeDict = new Dictionary<int, TreeNode>();

            // ترتيب الحسابات حسب الهيكل الهرمي
            var sortedAccounts = _filteredAccounts
                .OrderBy(a => a.AccountLevel)
                .ThenBy(a => a.AccountPath)
                .ThenBy(a => a.SortOrder)
                .ThenBy(a => a.AccountCode)
                .ToList();

            foreach (var account in sortedAccounts)
            {
                var node = CreateAccountNode(account);
                nodeDict[account.AccountId] = node;

                if (account.ParentAccountId.HasValue &&
                    nodeDict.ContainsKey(account.ParentAccountId.Value))
                {
                    // إضافة كعقدة فرعية
                    nodeDict[account.ParentAccountId.Value].Nodes.Add(node);
                }
                else
                {
                    // إضافة كعقدة جذر
                    treeViewAccounts.Nodes.Add(node);
                }
            }

            // توسيع المستوى الأول
            foreach (TreeNode rootNode in treeViewAccounts.Nodes)
            {
                rootNode.Expand();
            }
        }

        /// <summary>
        /// إنشاء عقدة حساب
        /// Create Account Node
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>عقدة الشجرة</returns>
        private TreeNode CreateAccountNode(ChartOfAccount account)
        {
            var nodeText = $"{account.AccountCode} - {account.AccountNameAr}";

            // إضافة معلومات إضافية
            if (account.CurrentBalance != 0)
            {
                nodeText += $" ({account.CurrentBalance:N2} {account.CurrencyCode})";
            }

            var node = new TreeNode(nodeText)
            {
                Tag = account,
                ToolTipText = GetAccountTooltip(account)
            };

            // تحديد الأيقونة واللون
            SetNodeAppearance(node, account);

            return node;
        }

        /// <summary>
        /// تحديد مظهر العقدة
        /// Set Node Appearance
        /// </summary>
        /// <param name="node">العقدة</param>
        /// <param name="account">بيانات الحساب</param>
        private void SetNodeAppearance(TreeNode node, ChartOfAccount account)
        {
            // تحديد اللون حسب حالة الحساب
            if (!account.IsActive)
            {
                node.ForeColor = Color.Gray;
                node.Text = "🚫 " + node.Text;
            }
            else if (account.IsParent)
            {
                node.ForeColor = _primaryColor;
                node.Text = "📁 " + node.Text;
                node.NodeFont = new Font(treeViewAccounts.Font, FontStyle.Bold);
            }
            else
            {
                node.ForeColor = Color.FromArgb(44, 62, 80);
                node.Text = "📄 " + node.Text;
            }

            // تحديد لون الخلفية حسب طبيعة الحساب
            if (account.Nature == "مدين")
            {
                node.BackColor = Color.FromArgb(250, 255, 250); // أخضر فاتح جداً
            }
            else
            {
                node.BackColor = Color.FromArgb(250, 250, 255); // أزرق فاتح جداً
            }
        }

        /// <summary>
        /// الحصول على نص التلميح للحساب
        /// Get Account Tooltip
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نص التلميح</returns>
        private string GetAccountTooltip(ChartOfAccount account)
        {
            var tooltip = $"رمز الحساب: {account.AccountCode}\n";
            tooltip += $"اسم الحساب: {account.AccountNameAr}\n";
            tooltip += $"نوع الحساب: {account.AccountTypeName}\n";
            tooltip += $"مجموعة الحساب: {account.AccountGroupName}\n";
            tooltip += $"المستوى: {account.AccountLevel}\n";
            tooltip += $"الطبيعة: {account.Nature}\n";
            tooltip += $"الرصيد الحالي: {account.CurrentBalance:N2} {account.CurrencyCode}\n";
            tooltip += $"الحالة: {(account.IsActive ? "نشط" : "غير نشط")}\n";

            if (!string.IsNullOrEmpty(account.Description))
            {
                tooltip += $"الوصف: {account.Description}";
            }

            return tooltip;
        }

        /// <summary>
        /// تحديث عداد الحسابات
        /// Update Accounts Count
        /// </summary>
        private void UpdateAccountsCount()
        {
            var totalCount = _allAccounts?.Count ?? 0;
            var displayedCount = _filteredAccounts?.Count ?? 0;

            labelAccountsCount.Text = $"عدد الحسابات: {displayedCount} من {totalCount}";
        }

        #endregion

        #region الطرق العامة - Public Methods

        /// <summary>
        /// البحث عن حساب وتحديده
        /// Find and Select Account
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>هل تم العثور على الحساب</returns>
        public bool FindAndSelectAccount(int accountId)
        {
            return FindAndSelectAccountRecursive(treeViewAccounts.Nodes, accountId);
        }

        /// <summary>
        /// البحث المتكرر عن الحساب
        /// Recursive Account Search
        /// </summary>
        /// <param name="nodes">مجموعة العقد</param>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>هل تم العثور على الحساب</returns>
        private bool FindAndSelectAccountRecursive(TreeNodeCollection nodes, int accountId)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Tag is ChartOfAccount account && account.AccountId == accountId)
                {
                    treeViewAccounts.SelectedNode = node;
                    node.EnsureVisible();
                    return true;
                }

                if (FindAndSelectAccountRecursive(node.Nodes, accountId))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// تطبيق التصميم العصري
        /// Apply Modern Styling
        /// </summary>
        private void ApplyModernStyling()
        {
            // تطبيق الألوان والخطوط العصرية
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
        }

        #endregion

        #region إثارة الأحداث - Raise Events

        protected virtual void OnAccountSelected(AccountSelectedEventArgs e)
        {
            AccountSelected?.Invoke(this, e);
        }

        protected virtual void OnAccountDoubleClicked(AccountSelectedEventArgs e)
        {
            AccountDoubleClicked?.Invoke(this, e);
        }

        protected virtual void OnAccountRightClicked(AccountSelectedEventArgs e)
        {
            AccountRightClicked?.Invoke(this, e);
        }

        protected virtual void OnSearchChanged(string searchText)
        {
            SearchChanged?.Invoke(this, searchText);
        }

        #endregion
    }

    /// <summary>
    /// معاملات حدث تحديد الحساب
    /// Account Selected Event Arguments
    /// </summary>
    public class AccountSelectedEventArgs : EventArgs
    {
        public ChartOfAccount Account { get; }

        public AccountSelectedEventArgs(ChartOfAccount account)
        {
            Account = account;
        }
    }
}
