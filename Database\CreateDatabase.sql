-- ========================================
-- 🗄️ سكريبت إنشاء قاعدة بيانات نظام إدارة الأوقاف
-- 🗄️ Awqaf Management System Database Creation Script
-- ========================================

-- إنشاء قاعدة البيانات
-- Create Database
USE master;
GO

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AwqafManagement')
BEGIN
    CREATE DATABASE AwqafManagement
    ON (
        NAME = 'AwqafManagement',
        FILENAME = 'C:\Database\AwqafManagement.mdf',
        SIZE = 100MB,
        MAXSIZE = 1GB,
        FILEGROWTH = 10MB
    )
    LOG ON (
        NAME = 'AwqafManagement_Log',
        FILENAME = 'C:\Database\AwqafManagement_Log.ldf',
        SIZE = 10MB,
        MAXSIZE = 100MB,
        FILEGROWTH = 5MB
    );
    
    PRINT '✅ تم إنشاء قاعدة البيانات بنجاح - Database created successfully';
END
ELSE
BEGIN
    PRINT '⚠️ قاعدة البيانات موجودة مسبقاً - Database already exists';
END
GO

-- استخدام قاعدة البيانات
-- Use Database
USE AwqafManagement;
GO

-- ========================================
-- 📋 إنشاء الجداول الأساسية
-- 📋 Create Basic Tables
-- ========================================

-- جدول أنواع الحسابات
-- Account Types Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountTypes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountTypes] (
        [AccountTypeId] INT IDENTITY(1,1) PRIMARY KEY,
        [TypeNameAr] NVARCHAR(100) NOT NULL,
        [TypeNameEn] NVARCHAR(100) NULL,
        [TypeCode] NVARCHAR(10) NOT NULL UNIQUE,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] NVARCHAR(50) NULL
    );
    
    PRINT '✅ تم إنشاء جدول أنواع الحسابات - AccountTypes table created';
END
GO

-- جدول مجموعات الحسابات
-- Account Groups Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountGroups]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountGroups] (
        [AccountGroupId] INT IDENTITY(1,1) PRIMARY KEY,
        [GroupNameAr] NVARCHAR(100) NOT NULL,
        [GroupNameEn] NVARCHAR(100) NULL,
        [GroupCode] NVARCHAR(10) NOT NULL UNIQUE,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] NVARCHAR(50) NULL
    );
    
    PRINT '✅ تم إنشاء جدول مجموعات الحسابات - AccountGroups table created';
END
GO

-- جدول العملات
-- Currencies Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Currencies] (
        [CurrencyId] INT IDENTITY(1,1) PRIMARY KEY,
        [CurrencyCode] NVARCHAR(3) NOT NULL UNIQUE,
        [CurrencyNameAr] NVARCHAR(50) NOT NULL,
        [CurrencyNameEn] NVARCHAR(50) NULL,
        [Symbol] NVARCHAR(5) NOT NULL,
        [ExchangeRate] DECIMAL(18,6) NOT NULL DEFAULT 1.0,
        [IsBaseCurrency] BIT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] NVARCHAR(50) NULL
    );
    
    PRINT '✅ تم إنشاء جدول العملات - Currencies table created';
END
GO

-- جدول المعلومات الشخصية
-- Personal Information Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PersonalInformation]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PersonalInformation] (
        [PersonalInfoId] INT IDENTITY(1,1) PRIMARY KEY,
        [FullNameAr] NVARCHAR(200) NOT NULL,
        [FullNameEn] NVARCHAR(200) NULL,
        [FirstNameAr] NVARCHAR(50) NULL,
        [LastNameAr] NVARCHAR(50) NULL,
        [FatherNameAr] NVARCHAR(50) NULL,
        [GrandFatherNameAr] NVARCHAR(50) NULL,
        [IdNumber] NVARCHAR(20) NULL,
        [NationalId] NVARCHAR(20) NULL,
        [PassportNumber] NVARCHAR(20) NULL,
        [IdType] NVARCHAR(50) NULL,
        [IdExpiryDate] DATE NULL,
        [PhoneNumber] NVARCHAR(20) NULL,
        [Phone] NVARCHAR(20) NULL,
        [Mobile] NVARCHAR(20) NULL,
        [SecondaryPhone] NVARCHAR(20) NULL,
        [Email] NVARCHAR(100) NULL,
        [AddressAr] NVARCHAR(500) NULL,
        [AddressEn] NVARCHAR(500) NULL,
        [Address] NVARCHAR(500) NULL,
        [City] NVARCHAR(100) NULL,
        [State] NVARCHAR(100) NULL,
        [PostalCode] NVARCHAR(20) NULL,
        [Country] NVARCHAR(100) NULL,
        [DateOfBirth] DATE NULL,
        [Gender] NVARCHAR(10) NULL,
        [Nationality] NVARCHAR(50) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] NVARCHAR(50) NULL
    );
    
    PRINT '✅ تم إنشاء جدول المعلومات الشخصية - PersonalInformation table created';
END
GO

-- جدول دليل الحسابات
-- Chart of Accounts Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ChartOfAccounts] (
        [AccountId] INT IDENTITY(1,1) PRIMARY KEY,
        [AccountCode] NVARCHAR(20) NOT NULL UNIQUE,
        [AccountNameAr] NVARCHAR(200) NOT NULL,
        [AccountNameEn] NVARCHAR(200) NULL,
        [AccountTypeId] INT NOT NULL,
        [AccountGroupId] INT NOT NULL,
        [ParentAccountId] INT NULL,
        [PersonalInfoId] INT NULL,
        [CurrencyId] INT NULL,
        [AccountLevel] INT NOT NULL DEFAULT 1,
        [AccountPath] NVARCHAR(500) NULL,
        [IsParent] BIT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [AllowPosting] BIT NOT NULL DEFAULT 1,
        [Description] NVARCHAR(1000) NULL,
        [OpeningBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [OpeningBalanceDate] DATE NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] NVARCHAR(50) NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_ChartOfAccounts_AccountTypes] 
            FOREIGN KEY ([AccountTypeId]) REFERENCES [AccountTypes]([AccountTypeId]),
        CONSTRAINT [FK_ChartOfAccounts_AccountGroups] 
            FOREIGN KEY ([AccountGroupId]) REFERENCES [AccountGroups]([AccountGroupId]),
        CONSTRAINT [FK_ChartOfAccounts_ParentAccount] 
            FOREIGN KEY ([ParentAccountId]) REFERENCES [ChartOfAccounts]([AccountId]),
        CONSTRAINT [FK_ChartOfAccounts_PersonalInformation] 
            FOREIGN KEY ([PersonalInfoId]) REFERENCES [PersonalInformation]([PersonalInfoId]),
        CONSTRAINT [FK_ChartOfAccounts_Currencies] 
            FOREIGN KEY ([CurrencyId]) REFERENCES [Currencies]([CurrencyId])
    );
    
    PRINT '✅ تم إنشاء جدول دليل الحسابات - ChartOfAccounts table created';
END
GO

-- ========================================
-- 📊 إدراج البيانات الأساسية
-- 📊 Insert Basic Data
-- ========================================

-- إدراج أنواع الحسابات الأساسية
-- Insert Basic Account Types
IF NOT EXISTS (SELECT * FROM AccountTypes WHERE TypeCode = 'ASSET')
BEGIN
    INSERT INTO AccountTypes (TypeNameAr, TypeNameEn, TypeCode, Description)
    VALUES 
        (N'الأصول', N'Assets', 'ASSET', N'حسابات الأصول والممتلكات'),
        (N'الخصوم', N'Liabilities', 'LIAB', N'حسابات الخصوم والالتزامات'),
        (N'حقوق الملكية', N'Equity', 'EQUITY', N'حسابات رؤوس الأموال وحقوق الملكية'),
        (N'الإيرادات', N'Revenue', 'REV', N'حسابات الإيرادات والدخل'),
        (N'المصروفات', N'Expenses', 'EXP', N'حسابات المصروفات والتكاليف');
    
    PRINT '✅ تم إدراج أنواع الحسابات الأساسية - Basic account types inserted';
END
GO

-- إدراج مجموعات الحسابات الأساسية
-- Insert Basic Account Groups
IF NOT EXISTS (SELECT * FROM AccountGroups WHERE GroupCode = 'CASH')
BEGIN
    INSERT INTO AccountGroups (GroupNameAr, GroupNameEn, GroupCode, Description)
    VALUES 
        (N'النقدية', N'Cash & Bank', 'CASH', N'حسابات النقدية والبنوك'),
        (N'المدينون', N'Receivables', 'REC', N'حسابات العملاء والمدينين'),
        (N'الدائنون', N'Payables', 'PAY', N'حسابات الموردين والدائنين'),
        (N'المخزون', N'Inventory', 'INV', N'حسابات المخزون والبضائع'),
        (N'الأصول الثابتة', N'Fixed Assets', 'FA', N'حسابات الأصول الثابتة');
    
    PRINT '✅ تم إدراج مجموعات الحسابات الأساسية - Basic account groups inserted';
END
GO

-- إدراج العملات الأساسية
-- Insert Basic Currencies
IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'SAR')
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate, IsBaseCurrency)
    VALUES 
        ('SAR', N'ريال سعودي', N'Saudi Riyal', N'ر.س', 1.0, 1),
        ('USD', N'دولار أمريكي', N'US Dollar', N'$', 3.75, 0),
        ('EUR', N'يورو', N'Euro', N'€', 4.10, 0);
    
    PRINT '✅ تم إدراج العملات الأساسية - Basic currencies inserted';
END
GO

PRINT '🎉 تم إنشاء قاعدة البيانات وإعدادها بنجاح!';
PRINT '🎉 Database created and configured successfully!';
GO
