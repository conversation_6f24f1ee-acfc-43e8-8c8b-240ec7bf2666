-- =============================================
-- حذف الجداول القديمة مع القيود الخارجية
-- Drop Old Tables with Foreign Key Constraints
-- =============================================

USE AwqafManagement;
GO

-- حذف القيود الخارجية أولاً
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_JournalEntryDetails_Account')
    ALTER TABLE JournalEntryDetails DROP CONSTRAINT FK_JournalEntryDetails_Account;

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ChartOfAccounts_Parent')
    ALTER TABLE ChartOfAccounts DROP CONSTRAINT FK_ChartOfAccounts_Parent;

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ChartOfAccounts_AccountType')
    ALTER TABLE ChartOfAccounts DROP CONSTRAINT FK_ChartOfAccounts_AccountType;

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ChartOfAccounts_AccountGroup')
    ALTER TABLE ChartOfAccounts DROP CONSTRAINT FK_ChartOfAccounts_AccountGroup;

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AccountGroups_AccountType')
    ALTER TABLE AccountGroups DROP CONSTRAINT FK_AccountGroups_AccountType;

-- حذف الجداول
IF OBJECT_ID('ChartOfAccounts', 'U') IS NOT NULL
    DROP TABLE ChartOfAccounts;

IF OBJECT_ID('AccountGroups', 'U') IS NOT NULL
    DROP TABLE AccountGroups;

IF OBJECT_ID('AccountTypes', 'U') IS NOT NULL
    DROP TABLE AccountTypes;

-- حذف الإجراءات المخزنة
IF OBJECT_ID('[dbo].[sp_GetAccountsHierarchy]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[sp_GetAccountsHierarchy];

IF OBJECT_ID('[dbo].[sp_ValidateAccountCode]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[sp_ValidateAccountCode];

PRINT N'تم حذف الجداول والإجراءات القديمة بنجاح';
