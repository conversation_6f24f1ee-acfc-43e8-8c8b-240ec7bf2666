using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// كلاس إدارة الاتصال بقاعدة البيانات
    /// Database Connection Management Class
    /// </summary>
    public class DatabaseConnection : IDisposable
    {
        private SqlConnection _connection;
        private SqlTransaction _transaction;
        private bool _disposed = false;

        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// Database Connection String
        /// </summary>
        public static string ConnectionString { get; private set; }

        /// <summary>
        /// الاتصال الحالي
        /// Current Connection
        /// </summary>
        public SqlConnection Connection => _connection;

        /// <summary>
        /// المعاملة الحالية
        /// Current Transaction
        /// </summary>
        public SqlTransaction Transaction => _transaction;

        /// <summary>
        /// هل يوجد معاملة نشطة
        /// Is Transaction Active
        /// </summary>
        public bool HasActiveTransaction => _transaction != null;

        static DatabaseConnection()
        {
            InitializeConnectionString();
        }

        /// <summary>
        /// منشئ الكلاس
        /// Class Constructor
        /// </summary>
        public DatabaseConnection()
        {
            _connection = new SqlConnection(ConnectionString);
        }

        /// <summary>
        /// تهيئة سلسلة الاتصال
        /// Initialize Connection String
        /// </summary>
        private static void InitializeConnectionString()
        {
            try
            {
                // استخدام DatabaseConfig للحصول على سلسلة اتصال صالحة
                // Use DatabaseConfig to get valid connection string
                ConnectionString = DatabaseConfig.GetValidConnectionString();

                // إذا لم توجد سلسلة اتصال صالحة، استخدم القيمة الافتراضية
                // If no valid connection string found, use default value
                if (string.IsNullOrEmpty(ConnectionString))
                {
                    ConnectionString = DatabaseConfig.TestConnectionString;
                }
            }
            catch
            {
                // في حالة الخطأ، استخدم القيمة الافتراضية
                // In case of error, use default value
                ConnectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;TrustServerCertificate=true;Connect Timeout=30;MultipleActiveResultSets=true;Application Name=AwqafManagement;";
            }
        }

        /// <summary>
        /// فتح الاتصال
        /// Open Connection
        /// </summary>
        public void Open()
        {
            try
            {
                if (_connection.State == ConnectionState.Closed)
                {
                    _connection.Open();
                }
            }
            catch (SqlException ex)
            {
                throw new Exception($"خطأ في فتح الاتصال بقاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إغلاق الاتصال
        /// Close Connection
        /// </summary>
        public void Close()
        {
            try
            {
                if (_transaction != null)
                {
                    _transaction.Dispose();
                    _transaction = null;
                }

                if (_connection?.State == ConnectionState.Open)
                {
                    _connection.Close();
                }
            }
            catch (SqlException ex)
            {
                throw new Exception($"خطأ في إغلاق الاتصال بقاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// بدء معاملة جديدة
        /// Begin New Transaction
        /// </summary>
        /// <param name="isolationLevel">مستوى العزل</param>
        /// <returns>المعاملة الجديدة</returns>
        public SqlTransaction BeginTransaction(IsolationLevel isolationLevel = IsolationLevel.ReadCommitted)
        {
            try
            {
                if (_transaction != null)
                {
                    throw new InvalidOperationException("يوجد معاملة نشطة بالفعل");
                }

                Open();
                _transaction = _connection.BeginTransaction(isolationLevel);
                return _transaction;
            }
            catch (SqlException ex)
            {
                throw new Exception($"خطأ في بدء المعاملة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تأكيد المعاملة
        /// Commit Transaction
        /// </summary>
        public void CommitTransaction()
        {
            try
            {
                if (_transaction == null)
                {
                    throw new InvalidOperationException("لا توجد معاملة نشطة للتأكيد");
                }

                _transaction.Commit();
                _transaction.Dispose();
                _transaction = null;
            }
            catch (SqlException ex)
            {
                throw new Exception($"خطأ في تأكيد المعاملة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إلغاء المعاملة
        /// Rollback Transaction
        /// </summary>
        public void RollbackTransaction()
        {
            try
            {
                if (_transaction == null)
                {
                    throw new InvalidOperationException("لا توجد معاملة نشطة للإلغاء");
                }

                _transaction.Rollback();
                _transaction.Dispose();
                _transaction = null;
            }
            catch (SqlException ex)
            {
                throw new Exception($"خطأ في إلغاء المعاملة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء أمر SQL جديد
        /// Create New SQL Command
        /// </summary>
        /// <param name="commandText">نص الأمر</param>
        /// <param name="commandType">نوع الأمر</param>
        /// <returns>أمر SQL جديد</returns>
        public SqlCommand CreateCommand(string commandText, CommandType commandType = CommandType.Text)
        {
            Open();
            var command = new SqlCommand(commandText, _connection);
            command.CommandType = commandType;
            
            if (_transaction != null)
            {
                command.Transaction = _transaction;
            }

            return command;
        }

        /// <summary>
        /// تنفيذ أمر SQL وإرجاع عدد الصفوف المتأثرة
        /// Execute SQL Command and Return Affected Rows Count
        /// </summary>
        /// <param name="commandText">نص الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public int ExecuteNonQuery(string commandText, params SqlParameter[] parameters)
        {
            using (var command = CreateCommand(commandText))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                return command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// تنفيذ أمر SQL وإرجاع قيمة واحدة
        /// Execute SQL Command and Return Single Value
        /// </summary>
        /// <param name="commandText">نص الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public object ExecuteScalar(string commandText, params SqlParameter[] parameters)
        {
            using (var command = CreateCommand(commandText))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                return command.ExecuteScalar();
            }
        }

        /// <summary>
        /// تنفيذ أمر SQL وإرجاع قارئ البيانات
        /// Execute SQL Command and Return Data Reader
        /// </summary>
        /// <param name="commandText">نص الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قارئ البيانات</returns>
        public SqlDataReader ExecuteReader(string commandText, params SqlParameter[] parameters)
        {
            var command = CreateCommand(commandText);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }
            return command.ExecuteReader();
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// Test Database Connection
        /// </summary>
        /// <returns>هل الاتصال ناجح</returns>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// Get Database Information
        /// </summary>
        /// <returns>معلومات قاعدة البيانات</returns>
        public static string GetDatabaseInfo()
        {
            try
            {
                using (var db = new DatabaseConnection())
                {
                    var serverVersion = db.ExecuteScalar("SELECT @@VERSION").ToString();
                    var databaseName = db.ExecuteScalar("SELECT DB_NAME()").ToString();
                    return $"Database: {databaseName}\nServer: {serverVersion}";
                }
            }
            catch (Exception ex)
            {
                return $"خطأ في الحصول على معلومات قاعدة البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// تحرير الموارد
        /// Dispose Resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تحرير الموارد المحمي
        /// Protected Dispose Resources
        /// </summary>
        /// <param name="disposing">هل يتم التحرير</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Close();
                _connection?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// المدمر
        /// Destructor
        /// </summary>
        ~DatabaseConnection()
        {
            Dispose(false);
        }
    }
}
