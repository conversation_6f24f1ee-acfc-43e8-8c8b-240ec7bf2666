using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج دليل الحسابات
    /// Chart of Accounts Model
    /// </summary>
    public class ChartOfAccount
    {
        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رمز الحساب
        /// Account Code
        /// </summary>
        [Required(ErrorMessage = "رمز الحساب مطلوب")]
        [StringLength(20, ErrorMessage = "رمز الحساب يجب ألا يزيد عن 20 حرف")]
        public string AccountCode { get; set; }

        /// <summary>
        /// اسم الحساب بالعربية
        /// Account Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "اسم الحساب بالعربية مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب يجب ألا يزيد عن 200 حرف")]
        public string AccountName { get; set; }

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// Account Name in English
        /// </summary>
        [StringLength(200, ErrorMessage = "اسم الحساب يجب ألا يزيد عن 200 حرف")]
        public string AccountNameEn { get; set; }

        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public int AccountTypeId { get; set; }

        /// <summary>
        /// معرف مجموعة الحساب
        /// Account Group ID
        /// </summary>
        public int? AccountGroupId { get; set; }

        /// <summary>
        /// معرف الحساب الأب
        /// Parent Account ID
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// مستوى الحساب في التسلسل الهرمي
        /// Account Level in Hierarchy
        /// </summary>
        public int AccountLevel { get; set; } = 1;

        /// <summary>
        /// المسار الهرمي للحساب
        /// Hierarchical Path of Account
        /// </summary>
        [StringLength(500)]
        public string AccountPath { get; set; }

        /// <summary>
        /// هل الحساب أب (له حسابات فرعية)
        /// Is Parent Account (has sub-accounts)
        /// </summary>
        public bool IsParent { get; set; } = false;

        /// <summary>
        /// هل الحساب نشط
        /// Is Account Active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// السماح بالترحيل
        /// Allow Posting
        /// </summary>
        public bool AllowPosting { get; set; } = true;

        /// <summary>
        /// وصف الحساب بالعربية
        /// Account Description in Arabic
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف الحساب يجب ألا يزيد عن 1000 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// وصف الحساب بالإنجليزية
        /// Account Description in English
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف الحساب يجب ألا يزيد عن 1000 حرف")]
        public string DescriptionEn { get; set; }

        /// <summary>
        /// معرف العملة
        /// Currency ID
        /// </summary>
        public int? CurrencyId { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// Opening Balance
        /// </summary>
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// تاريخ الرصيد الافتتاحي
        /// Opening Balance Date
        /// </summary>
        public DateTime? OpeningBalanceDate { get; set; }

        /// <summary>
        /// الرصيد المدين
        /// Debit Balance
        /// </summary>
        public decimal DebitBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الدائن
        /// Credit Balance
        /// </summary>
        public decimal CreditBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// Current Balance
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// معرف المعلومات الشخصية المرتبطة
        /// Related Personal Information ID
        /// </summary>
        public int? PersonalInfoId { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// Sort Order
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// منشئ السجل
        /// Created By
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل السجل
        /// Modified By
        /// </summary>
        [StringLength(100)]
        public string ModifiedBy { get; set; }

        // Navigation Properties
        /// <summary>
        /// نوع الحساب المرتبط
        /// Related Account Type
        /// </summary>
        public AccountType AccountType { get; set; }

        /// <summary>
        /// مجموعة الحساب المرتبطة
        /// Related Account Group
        /// </summary>
        public AccountGroup AccountGroup { get; set; }

        /// <summary>
        /// الحساب الأب
        /// Parent Account
        /// </summary>
        public ChartOfAccount ParentAccount { get; set; }

        /// <summary>
        /// العملة المرتبطة
        /// Related Currency
        /// </summary>
        public Currency Currency { get; set; }

        /// <summary>
        /// المعلومات الشخصية المرتبطة
        /// Related Personal Information
        /// </summary>
        public PersonalInformation PersonalInformation { get; set; }

        /// <summary>
        /// الحصول على اسم الحساب حسب اللغة
        /// Get account name based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>اسم الحساب</returns>
        public string GetDisplayName(bool isArabic = true)
        {
            if (isArabic)
                return !string.IsNullOrWhiteSpace(AccountName) ? AccountName : AccountNameEn;
            else
                return !string.IsNullOrWhiteSpace(AccountNameEn) ? AccountNameEn : AccountName;
        }

        /// <summary>
        /// الحصول على وصف الحساب حسب اللغة
        /// Get account description based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>وصف الحساب</returns>
        public string GetDisplayDescription(bool isArabic = true)
        {
            if (isArabic)
                return !string.IsNullOrWhiteSpace(Description) ? Description : DescriptionEn;
            else
                return !string.IsNullOrWhiteSpace(DescriptionEn) ? DescriptionEn : Description;
        }

        /// <summary>
        /// الحصول على النص الكامل للحساب
        /// Get full account text
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>النص الكامل للحساب</returns>
        public string GetFullDisplayText(bool isArabic = true)
        {
            var name = GetDisplayName(isArabic);
            return $"{AccountCode} - {name}";
        }

        /// <summary>
        /// الحصول على المسار الكامل للحساب
        /// Get full path of the account
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>المسار الكامل</returns>
        public string GetFullPath(bool isArabic = true)
        {
            if (!string.IsNullOrWhiteSpace(AccountPath))
            {
                // تحويل المسار من أرقام إلى أسماء
                // Convert path from numbers to names
                return AccountPath; // يمكن تطوير هذا لاحقاً
            }
            
            var currentName = GetDisplayName(isArabic);
            
            if (ParentAccount != null)
            {
                return $"{ParentAccount.GetFullPath(isArabic)} > {currentName}";
            }
            
            return currentName;
        }

        /// <summary>
        /// حساب الرصيد الصافي
        /// Calculate net balance
        /// </summary>
        /// <returns>الرصيد الصافي</returns>
        public decimal GetNetBalance()
        {
            return DebitBalance - CreditBalance;
        }

        /// <summary>
        /// تحديث الرصيد الحالي
        /// Update current balance
        /// </summary>
        public void UpdateCurrentBalance()
        {
            CurrentBalance = OpeningBalance + GetNetBalance();
        }

        /// <summary>
        /// التحقق من إمكانية الترحيل
        /// Check if posting is allowed
        /// </summary>
        /// <returns>هل يمكن الترحيل</returns>
        public bool CanPost()
        {
            return IsActive && AllowPosting && !IsParent;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (string.IsNullOrWhiteSpace(AccountCode))
                return "رمز الحساب مطلوب";

            if (string.IsNullOrWhiteSpace(AccountName))
                return "اسم الحساب بالعربية مطلوب";

            if (AccountTypeId <= 0)
                return "نوع الحساب مطلوب";

            // التحقق من عدم وجود مرجع دائري
            if (ParentAccountId.HasValue && ParentAccountId.Value == AccountId)
                return "لا يمكن أن يكون الحساب أب لنفسه";

            if (AccountLevel < 1)
                return "مستوى الحساب يجب أن يكون أكبر من صفر";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// نسخ البيانات من نموذج آخر
        /// Copy data from another model
        /// </summary>
        /// <param name="source">النموذج المصدر</param>
        public void CopyFrom(ChartOfAccount source)
        {
            if (source == null) return;

            AccountCode = source.AccountCode;
            AccountName = source.AccountName;
            AccountNameEn = source.AccountNameEn;
            AccountTypeId = source.AccountTypeId;
            AccountGroupId = source.AccountGroupId;
            ParentAccountId = source.ParentAccountId;
            AccountLevel = source.AccountLevel;
            AccountPath = source.AccountPath;
            IsParent = source.IsParent;
            IsActive = source.IsActive;
            AllowPosting = source.AllowPosting;
            Description = source.Description;
            DescriptionEn = source.DescriptionEn;
            CurrencyId = source.CurrencyId;
            OpeningBalance = source.OpeningBalance;
            OpeningBalanceDate = source.OpeningBalanceDate;
            PersonalInfoId = source.PersonalInfoId;
            SortOrder = source.SortOrder;
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return GetFullDisplayText(true);
        }
    }
}
