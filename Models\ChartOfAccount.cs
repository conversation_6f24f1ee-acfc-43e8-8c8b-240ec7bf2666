using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج دليل الحسابات المحسن والشامل
    /// Enhanced and Comprehensive Chart of Accounts Model
    /// </summary>
    public class ChartOfAccount
    {
        #region المعرفات الأساسية - Basic Identifiers

        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رمز الحساب (فريد)
        /// Account Code (Unique)
        /// </summary>
        [Required(ErrorMessage = "رمز الحساب مطلوب")]
        [StringLength(20, ErrorMessage = "رمز الحساب يجب ألا يزيد عن 20 حرف")]
        public string AccountCode { get; set; }

        #endregion

        #region أسماء الحساب - Account Names

        /// <summary>
        /// اسم الحساب بالعربية
        /// Account Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "اسم الحساب بالعربية مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب يجب ألا يزيد عن 200 حرف")]
        public string AccountNameAr { get; set; }

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// Account Name in English
        /// </summary>
        [StringLength(200, ErrorMessage = "اسم الحساب بالإنجليزية يجب ألا يزيد عن 200 حرف")]
        public string AccountNameEn { get; set; }

        /// <summary>
        /// اسم الحساب (للتوافق مع الكود القديم)
        /// Account Name (for backward compatibility)
        /// </summary>
        public string AccountName
        {
            get => AccountNameAr;
            set => AccountNameAr = value;
        }

        #endregion

        #region التصنيفات والهيكل - Classifications and Structure

        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public int AccountTypeId { get; set; }

        /// <summary>
        /// اسم نوع الحساب
        /// Account Type Name
        /// </summary>
        public string AccountTypeName { get; set; }

        /// <summary>
        /// معرف مجموعة الحساب
        /// Account Group ID
        /// </summary>
        [Required(ErrorMessage = "مجموعة الحساب مطلوبة")]
        public int AccountGroupId { get; set; }

        /// <summary>
        /// اسم مجموعة الحساب
        /// Account Group Name
        /// </summary>
        public string AccountGroupName { get; set; }

        /// <summary>
        /// معرف الحساب الأب
        /// Parent Account ID
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// اسم الحساب الأب
        /// Parent Account Name
        /// </summary>
        public string ParentAccountName { get; set; }

        /// <summary>
        /// معرف البيانات الشخصية المرتبطة
        /// Personal Information ID
        /// </summary>
        public int? PersonalInfoId { get; set; }

        /// <summary>
        /// اسم الشخص المرتبط بالحساب
        /// Personal Name Associated with Account
        /// </summary>
        public string PersonalName { get; set; }

        #endregion

        #region الهيكل الهرمي - Hierarchical Structure

        /// <summary>
        /// مستوى الحساب في الهيكل الهرمي
        /// Account Level in Hierarchy
        /// </summary>
        [Range(1, 10, ErrorMessage = "مستوى الحساب يجب أن يكون بين 1 و 10")]
        public int AccountLevel { get; set; } = 1;

        /// <summary>
        /// المسار الهرمي للحساب (مثل: 1.01.001)
        /// Hierarchical Path of Account
        /// </summary>
        [StringLength(500, ErrorMessage = "المسار الهرمي يجب ألا يزيد عن 500 حرف")]
        public string AccountPath { get; set; }

        /// <summary>
        /// هل الحساب أب (له حسابات فرعية)
        /// Is Parent Account (has sub-accounts)
        /// </summary>
        public bool IsParent { get; set; } = false;

        /// <summary>
        /// عدد الحسابات الفرعية
        /// Number of Sub-accounts
        /// </summary>
        public int SubAccountsCount { get; set; } = 0;

        #endregion

        #region حالة الحساب والإعدادات - Account Status and Settings

        /// <summary>
        /// هل الحساب نشط
        /// Is Account Active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// السماح بالترحيل
        /// Allow Posting
        /// </summary>
        public bool AllowPosting { get; set; } = true;

        /// <summary>
        /// السماح بالإدخال المباشر
        /// Allow Direct Entry
        /// </summary>
        public bool AllowDirectEntry { get; set; } = true;

        /// <summary>
        /// يتطلب مركز تكلفة
        /// Requires Cost Center
        /// </summary>
        public bool RequiresCostCenter { get; set; } = false;

        /// <summary>
        /// يتطلب مشروع
        /// Requires Project
        /// </summary>
        public bool RequiresProject { get; set; } = false;

        /// <summary>
        /// توليد الرمز تلقائياً
        /// Auto Generate Code
        /// </summary>
        public bool AutoGenerateCode { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// Sort Order
        /// </summary>
        public int SortOrder { get; set; } = 0;

        #endregion

        #region الوصف والملاحظات - Description and Notes

        /// <summary>
        /// وصف الحساب
        /// Account Description
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف الحساب يجب ألا يزيد عن 1000 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// Additional Notes
        /// </summary>
        [StringLength(2000, ErrorMessage = "الملاحظات يجب ألا تزيد عن 2000 حرف")]
        public string Notes { get; set; }

        #endregion

        #region العملة والأرصدة - Currency and Balances

        /// <summary>
        /// معرف العملة
        /// Currency ID
        /// </summary>
        public int? CurrencyId { get; set; } = 1;

        /// <summary>
        /// رمز العملة
        /// Currency Code
        /// </summary>
        [StringLength(3, ErrorMessage = "رمز العملة يجب ألا يزيد عن 3 أحرف")]
        public string CurrencyCode { get; set; } = "SAR";

        /// <summary>
        /// اسم العملة
        /// Currency Name
        /// </summary>
        public string CurrencyName { get; set; }

        /// <summary>
        /// رمز العملة للعرض
        /// Currency Symbol
        /// </summary>
        public string CurrencySymbol { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// Opening Balance
        /// </summary>
        [Range(-999999999999.9999, 999999999999.9999, ErrorMessage = "الرصيد الافتتاحي خارج النطاق المسموح")]
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// Current Balance
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد المدين
        /// Debit Balance
        /// </summary>
        public decimal DebitBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الدائن
        /// Credit Balance
        /// </summary>
        public decimal CreditBalance { get; set; } = 0;

        #endregion

        #region طبيعة الحساب - Account Nature

        /// <summary>
        /// طبيعة الحساب (مدين أو دائن)
        /// Account Nature (Debit or Credit)
        /// </summary>
        [StringLength(10, ErrorMessage = "طبيعة الحساب يجب ألا تزيد عن 10 أحرف")]
        public string Nature { get; set; } = "مدين";

        /// <summary>
        /// نوع الرصيد (مدين أو دائن)
        /// Balance Type (Debit or Credit)
        /// </summary>
        [StringLength(10, ErrorMessage = "نوع الرصيد يجب ألا يزيد عن 10 أحرف")]
        public string BalanceType { get; set; } = "مدين";

        #endregion

        #region المعلومات المالية الإضافية - Additional Financial Information

        /// <summary>
        /// الرقم الضريبي للحساب
        /// Tax Number
        /// </summary>
        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب ألا يزيد عن 50 حرف")]
        public string TaxNumber { get; set; }

        /// <summary>
        /// رقم السجل التجاري
        /// Commercial Register Number
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم السجل التجاري يجب ألا يزيد عن 50 حرف")]
        public string CommercialRegister { get; set; }

        /// <summary>
        /// رقم الحساب البنكي
        /// Bank Account Number
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم الحساب البنكي يجب ألا يزيد عن 50 حرف")]
        public string BankAccount { get; set; }

        /// <summary>
        /// رقم الآيبان
        /// IBAN Number
        /// </summary>
        [StringLength(34, ErrorMessage = "رقم الآيبان يجب ألا يزيد عن 34 حرف")]
        public string IBAN { get; set; }

        #endregion

        #region معلومات التدقيق - Audit Information

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// معرف منشئ الحساب
        /// Created By User ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// اسم منشئ الحساب
        /// Created By User Name
        /// </summary>
        public string CreatedByName { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف معدل الحساب
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }

        /// <summary>
        /// اسم معدل الحساب
        /// Modified By User Name
        /// </summary>
        public string ModifiedByName { get; set; }

        /// <summary>
        /// تاريخ آخر حركة مالية
        /// Last Transaction Date
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }

        #endregion

        #region خصائص محسوبة - Calculated Properties

        /// <summary>
        /// النص الكامل للحساب (الرمز + الاسم)
        /// Full Account Text (Code + Name)
        /// </summary>
        public string FullAccountText => $"{AccountCode} - {AccountNameAr}";

        /// <summary>
        /// مستوى العمق في الشجرة
        /// Tree Depth Level
        /// </summary>
        public string IndentText => new string(' ', (AccountLevel - 1) * 4);

        /// <summary>
        /// نص الرصيد مع العملة
        /// Balance Text with Currency
        /// </summary>
        public string BalanceText => $"{CurrentBalance:N2} {CurrencySymbol ?? CurrencyCode}";

        /// <summary>
        /// حالة الحساب كنص
        /// Account Status as Text
        /// </summary>
        public string StatusText => IsActive ? "نشط" : "غير نشط";

        /// <summary>
        /// نوع الحساب (أب أم فرعي)
        /// Account Type (Parent or Child)
        /// </summary>
        public string AccountTypeText => IsParent ? "حساب أب" : "حساب فرعي";

        #endregion

        #region التحقق من صحة البيانات - Data Validation

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        /// <returns>قائمة بالأخطاء إن وجدت</returns>
        public List<string> ValidateData()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(AccountCode))
                errors.Add("رمز الحساب مطلوب");

            if (string.IsNullOrWhiteSpace(AccountNameAr))
                errors.Add("اسم الحساب بالعربية مطلوب");

            if (AccountTypeId <= 0)
                errors.Add("نوع الحساب مطلوب");

            if (AccountGroupId <= 0)
                errors.Add("مجموعة الحساب مطلوبة");

            if (AccountLevel < 1 || AccountLevel > 10)
                errors.Add("مستوى الحساب يجب أن يكون بين 1 و 10");

            if (!string.IsNullOrEmpty(Nature) && Nature != "مدين" && Nature != "دائن")
                errors.Add("طبيعة الحساب يجب أن تكون 'مدين' أو 'دائن'");

            if (!string.IsNullOrEmpty(BalanceType) && BalanceType != "مدين" && BalanceType != "دائن")
                errors.Add("نوع الرصيد يجب أن يكون 'مدين' أو 'دائن'");

            return errors;
        }

        /// <summary>
        /// هل البيانات صحيحة
        /// Is Data Valid
        /// </summary>
        public bool IsValid => ValidateData().Count == 0;

        #endregion

        #region خصائص للتوافق مع الكود القديم - Backward Compatibility Properties

        /// <summary>
        /// للتوافق مع الكود القديم - IsParentAccount
        /// </summary>
        public bool IsParentAccount
        {
            get => IsParent;
            set => IsParent = value;
        }

        #endregion
    }
}

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// Account Name in English
        /// </summary>
        [StringLength(200, ErrorMessage = "اسم الحساب يجب ألا يزيد عن 200 حرف")]
        public string AccountNameEn { get; set; }

        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public int AccountTypeId { get; set; }

        /// <summary>
        /// معرف مجموعة الحساب
        /// Account Group ID
        /// </summary>
        public int? AccountGroupId { get; set; }

        /// <summary>
        /// معرف الحساب الأب
        /// Parent Account ID
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// مستوى الحساب في التسلسل الهرمي
        /// Account Level in Hierarchy
        /// </summary>
        public int AccountLevel { get; set; } = 1;

        /// <summary>
        /// المسار الهرمي للحساب
        /// Hierarchical Path of Account
        /// </summary>
        [StringLength(500)]
        public string AccountPath { get; set; }

        /// <summary>
        /// هل الحساب أب (له حسابات فرعية)
        /// Is Parent Account (has sub-accounts)
        /// </summary>
        public bool IsParent { get; set; } = false;

        /// <summary>
        /// هل الحساب نشط
        /// Is Account Active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// السماح بالترحيل
        /// Allow Posting
        /// </summary>
        public bool AllowPosting { get; set; } = true;

        /// <summary>
        /// وصف الحساب بالعربية
        /// Account Description in Arabic
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف الحساب يجب ألا يزيد عن 1000 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// وصف الحساب بالإنجليزية
        /// Account Description in English
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف الحساب يجب ألا يزيد عن 1000 حرف")]
        public string DescriptionEn { get; set; }

        /// <summary>
        /// معرف العملة
        /// Currency ID
        /// </summary>
        public int? CurrencyId { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// Opening Balance
        /// </summary>
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// تاريخ الرصيد الافتتاحي
        /// Opening Balance Date
        /// </summary>
        public DateTime? OpeningBalanceDate { get; set; }

        /// <summary>
        /// الرصيد المدين
        /// Debit Balance
        /// </summary>
        public decimal DebitBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الدائن
        /// Credit Balance
        /// </summary>
        public decimal CreditBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// Current Balance
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// معرف المعلومات الشخصية المرتبطة
        /// Related Personal Information ID
        /// </summary>
        public int? PersonalInfoId { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// Sort Order
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// منشئ السجل
        /// Created By
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل السجل
        /// Modified By
        /// </summary>
        [StringLength(100)]
        public string ModifiedBy { get; set; }

        // Navigation Properties
        /// <summary>
        /// نوع الحساب المرتبط
        /// Related Account Type
        /// </summary>
        public AccountType AccountType { get; set; }

        /// <summary>
        /// مجموعة الحساب المرتبطة
        /// Related Account Group
        /// </summary>
        public AccountGroup AccountGroup { get; set; }

        /// <summary>
        /// الحساب الأب
        /// Parent Account
        /// </summary>
        public ChartOfAccount ParentAccount { get; set; }

        /// <summary>
        /// العملة المرتبطة
        /// Related Currency
        /// </summary>
        public Currency Currency { get; set; }

        /// <summary>
        /// المعلومات الشخصية المرتبطة
        /// Related Personal Information
        /// </summary>
        public PersonalInformation PersonalInformation { get; set; }

        /// <summary>
        /// الحصول على اسم الحساب حسب اللغة
        /// Get account name based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>اسم الحساب</returns>
        public string GetDisplayName(bool isArabic = true)
        {
            if (isArabic)
                return !string.IsNullOrWhiteSpace(AccountName) ? AccountName : AccountNameEn;
            else
                return !string.IsNullOrWhiteSpace(AccountNameEn) ? AccountNameEn : AccountName;
        }

        /// <summary>
        /// الحصول على وصف الحساب حسب اللغة
        /// Get account description based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>وصف الحساب</returns>
        public string GetDisplayDescription(bool isArabic = true)
        {
            if (isArabic)
                return !string.IsNullOrWhiteSpace(Description) ? Description : DescriptionEn;
            else
                return !string.IsNullOrWhiteSpace(DescriptionEn) ? DescriptionEn : Description;
        }

        /// <summary>
        /// الحصول على النص الكامل للحساب
        /// Get full account text
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>النص الكامل للحساب</returns>
        public string GetFullDisplayText(bool isArabic = true)
        {
            var name = GetDisplayName(isArabic);
            return $"{AccountCode} - {name}";
        }

        /// <summary>
        /// الحصول على المسار الكامل للحساب
        /// Get full path of the account
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>المسار الكامل</returns>
        public string GetFullPath(bool isArabic = true)
        {
            if (!string.IsNullOrWhiteSpace(AccountPath))
            {
                // تحويل المسار من أرقام إلى أسماء
                // Convert path from numbers to names
                return AccountPath; // يمكن تطوير هذا لاحقاً
            }
            
            var currentName = GetDisplayName(isArabic);
            
            if (ParentAccount != null)
            {
                return $"{ParentAccount.GetFullPath(isArabic)} > {currentName}";
            }
            
            return currentName;
        }

        /// <summary>
        /// حساب الرصيد الصافي
        /// Calculate net balance
        /// </summary>
        /// <returns>الرصيد الصافي</returns>
        public decimal GetNetBalance()
        {
            return DebitBalance - CreditBalance;
        }

        /// <summary>
        /// تحديث الرصيد الحالي
        /// Update current balance
        /// </summary>
        public void UpdateCurrentBalance()
        {
            CurrentBalance = OpeningBalance + GetNetBalance();
        }

        /// <summary>
        /// التحقق من إمكانية الترحيل
        /// Check if posting is allowed
        /// </summary>
        /// <returns>هل يمكن الترحيل</returns>
        public bool CanPost()
        {
            return IsActive && AllowPosting && !IsParent;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (string.IsNullOrWhiteSpace(AccountCode))
                return "رمز الحساب مطلوب";

            if (string.IsNullOrWhiteSpace(AccountName))
                return "اسم الحساب بالعربية مطلوب";

            if (AccountTypeId <= 0)
                return "نوع الحساب مطلوب";

            // التحقق من عدم وجود مرجع دائري
            if (ParentAccountId.HasValue && ParentAccountId.Value == AccountId)
                return "لا يمكن أن يكون الحساب أب لنفسه";

            if (AccountLevel < 1)
                return "مستوى الحساب يجب أن يكون أكبر من صفر";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// نسخ البيانات من نموذج آخر
        /// Copy data from another model
        /// </summary>
        /// <param name="source">النموذج المصدر</param>
        public void CopyFrom(ChartOfAccount source)
        {
            if (source == null) return;

            AccountCode = source.AccountCode;
            AccountName = source.AccountName;
            AccountNameEn = source.AccountNameEn;
            AccountTypeId = source.AccountTypeId;
            AccountGroupId = source.AccountGroupId;
            ParentAccountId = source.ParentAccountId;
            AccountLevel = source.AccountLevel;
            AccountPath = source.AccountPath;
            IsParent = source.IsParent;
            IsActive = source.IsActive;
            AllowPosting = source.AllowPosting;
            Description = source.Description;
            DescriptionEn = source.DescriptionEn;
            CurrencyId = source.CurrencyId;
            OpeningBalance = source.OpeningBalance;
            OpeningBalanceDate = source.OpeningBalanceDate;
            PersonalInfoId = source.PersonalInfoId;
            SortOrder = source.SortOrder;
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return GetFullDisplayText(true);
        }
    }
}
