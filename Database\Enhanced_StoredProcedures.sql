-- =============================================
-- الإجراءات المخزنة المحسنة لنظام إدارة الدليل المحاسبي
-- Enhanced Stored Procedures for Chart of Accounts
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. إجراء البحث المتقدم في الحسابات
-- Advanced Account Search Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_SearchAccounts')
    DROP PROCEDURE SP_SearchAccounts;
GO

CREATE PROCEDURE SP_SearchAccounts
    @SearchTerm NVARCHAR(200) = NULL,
    @AccountTypeId INT = NULL,
    @AccountGroupId INT = NULL,
    @IsActive BIT = NULL,
    @IncludePersonalInfo BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ca.AccountId,
        ca.AccountCode,
        ca.AccountNameAr,
        ca.AccountNameEn,
        ca.AccountTypeId,
        at.TypeNameAr AS AccountTypeName,
        ca.AccountGroupId,
        ag.GroupNameAr AS AccountGroupName,
        ca.ParentAccountId,
        pa.AccountNameAr AS ParentAccountName,
        ca.PersonalInfoId,
        CASE WHEN @IncludePersonalInfo = 1 THEN pi.FullNameAr ELSE NULL END AS PersonalName,
        ca.AccountLevel,
        ca.IsParent,
        ca.IsActive,
        ca.AllowPosting,
        ca.Description,
        ca.CurrencyCode,
        ca.OpeningBalance,
        ca.CurrentBalance,
        ca.DebitBalance,
        ca.CreditBalance,
        ca.LastTransactionDate,
        ca.CreatedDate,
        ca.ModifiedDate
    FROM ChartOfAccounts ca
    INNER JOIN AccountTypes at ON ca.AccountTypeId = at.AccountTypeId
    INNER JOIN AccountGroups ag ON ca.AccountGroupId = ag.AccountGroupId
    LEFT JOIN ChartOfAccounts pa ON ca.ParentAccountId = pa.AccountId
    LEFT JOIN PersonalInformation pi ON ca.PersonalInfoId = pi.PersonalInfoId
    WHERE 
        (@SearchTerm IS NULL OR 
         ca.AccountCode LIKE '%' + @SearchTerm + '%' OR
         ca.AccountNameAr LIKE '%' + @SearchTerm + '%' OR
         ca.AccountNameEn LIKE '%' + @SearchTerm + '%' OR
         ca.Description LIKE '%' + @SearchTerm + '%')
    AND (@AccountTypeId IS NULL OR ca.AccountTypeId = @AccountTypeId)
    AND (@AccountGroupId IS NULL OR ca.AccountGroupId = @AccountGroupId)
    AND (@IsActive IS NULL OR ca.IsActive = @IsActive)
    ORDER BY ca.AccountCode;
END
GO

-- =============================================
-- 2. إجراء الحصول على الحسابات الهرمية
-- Get Hierarchical Accounts Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_GetAccountsHierarchy')
    DROP PROCEDURE SP_GetAccountsHierarchy;
GO

CREATE PROCEDURE SP_GetAccountsHierarchy
    @ParentAccountId INT = NULL,
    @AccountTypeId INT = NULL,
    @MaxLevel INT = 5
AS
BEGIN
    SET NOCOUNT ON;
    
    WITH AccountHierarchy AS (
        -- المستوى الأول: الحسابات الرئيسية
        SELECT 
            ca.AccountId,
            ca.AccountCode,
            ca.AccountNameAr,
            ca.AccountNameEn,
            ca.AccountTypeId,
            ca.AccountGroupId,
            ca.ParentAccountId,
            ca.AccountLevel,
            ca.IsParent,
            ca.IsActive,
            ca.AllowPosting,
            ca.CurrentBalance,
            CAST(ca.AccountCode AS NVARCHAR(500)) AS HierarchyPath,
            0 AS Level
        FROM ChartOfAccounts ca
        WHERE ca.ParentAccountId IS NULL
        AND (@AccountTypeId IS NULL OR ca.AccountTypeId = @AccountTypeId)
        AND ca.IsActive = 1
        
        UNION ALL
        
        -- المستويات التالية: الحسابات الفرعية
        SELECT 
            ca.AccountId,
            ca.AccountCode,
            ca.AccountNameAr,
            ca.AccountNameEn,
            ca.AccountTypeId,
            ca.AccountGroupId,
            ca.ParentAccountId,
            ca.AccountLevel,
            ca.IsParent,
            ca.IsActive,
            ca.AllowPosting,
            ca.CurrentBalance,
            CAST(ah.HierarchyPath + ' > ' + ca.AccountCode AS NVARCHAR(500)),
            ah.Level + 1
        FROM ChartOfAccounts ca
        INNER JOIN AccountHierarchy ah ON ca.ParentAccountId = ah.AccountId
        WHERE ah.Level < @MaxLevel
        AND ca.IsActive = 1
    )
    SELECT 
        ah.*,
        at.TypeNameAr AS AccountTypeName,
        ag.GroupNameAr AS AccountGroupName,
        REPLICATE('    ', ah.Level) + ah.AccountNameAr AS IndentedName
    FROM AccountHierarchy ah
    INNER JOIN AccountTypes at ON ah.AccountTypeId = at.AccountTypeId
    INNER JOIN AccountGroups ag ON ah.AccountGroupId = ag.AccountGroupId
    WHERE (@ParentAccountId IS NULL OR ah.ParentAccountId = @ParentAccountId)
    ORDER BY ah.HierarchyPath;
END
GO

-- =============================================
-- 3. إجراء توليد كود الحساب التلقائي
-- Auto Generate Account Code Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_GenerateAccountCode')
    DROP PROCEDURE SP_GenerateAccountCode;
GO

CREATE PROCEDURE SP_GenerateAccountCode
    @AccountTypeId INT,
    @AccountGroupId INT = NULL,
    @ParentAccountId INT = NULL,
    @GeneratedCode NVARCHAR(20) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TypeCode INT = @AccountTypeId;
    DECLARE @GroupCode INT;
    DECLARE @AccountNumber INT;
    
    -- الحصول على كود المجموعة
    IF @AccountGroupId IS NOT NULL
    BEGIN
        SELECT @GroupCode = CAST(GroupCode AS INT)
        FROM AccountGroups 
        WHERE AccountGroupId = @AccountGroupId;
    END
    ELSE
    BEGIN
        SET @GroupCode = 1;
    END
    
    -- إذا كان هناك حساب أب، استخدم نمطه
    IF @ParentAccountId IS NOT NULL
    BEGIN
        DECLARE @ParentCode NVARCHAR(20);
        SELECT @ParentCode = AccountCode 
        FROM ChartOfAccounts 
        WHERE AccountId = @ParentAccountId;
        
        -- تحليل كود الحساب الأب
        DECLARE @ParentParts TABLE (PartIndex INT, PartValue NVARCHAR(10));
        INSERT INTO @ParentParts
        SELECT 
            ROW_NUMBER() OVER (ORDER BY (SELECT NULL)),
            value
        FROM STRING_SPLIT(@ParentCode, '.');
        
        SELECT @TypeCode = CAST(PartValue AS INT) FROM @ParentParts WHERE PartIndex = 1;
        SELECT @GroupCode = CAST(PartValue AS INT) FROM @ParentParts WHERE PartIndex = 2;
        
        -- العثور على أعلى رقم حساب فرعي
        SELECT @AccountNumber = ISNULL(MAX(CAST(RIGHT(AccountCode, 3) AS INT)), 0) + 1
        FROM ChartOfAccounts
        WHERE ParentAccountId = @ParentAccountId;
    END
    ELSE
    BEGIN
        -- العثور على أعلى رقم حساب في المجموعة
        SELECT @AccountNumber = ISNULL(MAX(CAST(RIGHT(AccountCode, 3) AS INT)), 0) + 1
        FROM ChartOfAccounts
        WHERE AccountTypeId = @AccountTypeId 
        AND AccountGroupId = @AccountGroupId;
    END
    
    -- توليد الكود الجديد
    SET @GeneratedCode = FORMAT(@TypeCode, '0') + '.' + 
                        FORMAT(@GroupCode, '00') + '.' + 
                        FORMAT(@AccountNumber, '000');
END
GO

-- =============================================
-- 4. إجراء حفظ الحساب مع التحقق
-- Save Account with Validation Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_SaveAccount')
    DROP PROCEDURE SP_SaveAccount;
GO

CREATE PROCEDURE SP_SaveAccount
    @AccountId INT = NULL,
    @AccountCode NVARCHAR(20),
    @AccountNameAr NVARCHAR(200),
    @AccountNameEn NVARCHAR(200) = NULL,
    @AccountTypeId INT,
    @AccountGroupId INT,
    @ParentAccountId INT = NULL,
    @PersonalInfoId INT = NULL,
    @Description NVARCHAR(1000) = NULL,
    @CurrencyCode NVARCHAR(3) = 'SAR',
    @OpeningBalance DECIMAL(18,4) = 0,
    @AllowPosting BIT = 1,
    @IsActive BIT = 1,
    @UserId NVARCHAR(100),
    @Result INT OUTPUT,
    @Message NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من عدم تكرار الكود
        IF EXISTS (SELECT 1 FROM ChartOfAccounts 
                  WHERE AccountCode = @AccountCode 
                  AND (@AccountId IS NULL OR AccountId != @AccountId))
        BEGIN
            SET @Result = -1;
            SET @Message = N'كود الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من صحة الحساب الأب
        IF @ParentAccountId IS NOT NULL
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @ParentAccountId)
            BEGIN
                SET @Result = -2;
                SET @Message = N'الحساب الأب غير موجود';
                ROLLBACK TRANSACTION;
                RETURN;
            END
        END
        
        -- حساب المستوى
        DECLARE @AccountLevel INT = 1;
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT @AccountLevel = AccountLevel + 1
            FROM ChartOfAccounts
            WHERE AccountId = @ParentAccountId;
        END
        
        -- تحديد ما إذا كان الحساب أب
        DECLARE @IsParent BIT = 0;
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE ParentAccountId = @AccountId)
            SET @IsParent = 1;
        
        IF @AccountId IS NULL
        BEGIN
            -- إدراج حساب جديد
            INSERT INTO ChartOfAccounts (
                AccountCode, AccountNameAr, AccountNameEn, AccountTypeId,
                AccountGroupId, ParentAccountId, PersonalInfoId, AccountLevel,
                IsParent, IsActive, AllowPosting, Description, CurrencyCode,
                OpeningBalance, CurrentBalance, CreatedBy
            )
            VALUES (
                @AccountCode, @AccountNameAr, @AccountNameEn, @AccountTypeId,
                @AccountGroupId, @ParentAccountId, @PersonalInfoId, @AccountLevel,
                @IsParent, @IsActive, @AllowPosting, @Description, @CurrencyCode,
                @OpeningBalance, @OpeningBalance, @UserId
            );
            
            SET @AccountId = SCOPE_IDENTITY();
            SET @Message = N'تم إضافة الحساب بنجاح';
        END
        ELSE
        BEGIN
            -- تحديث حساب موجود
            UPDATE ChartOfAccounts
            SET AccountCode = @AccountCode,
                AccountNameAr = @AccountNameAr,
                AccountNameEn = @AccountNameEn,
                AccountTypeId = @AccountTypeId,
                AccountGroupId = @AccountGroupId,
                ParentAccountId = @ParentAccountId,
                PersonalInfoId = @PersonalInfoId,
                AccountLevel = @AccountLevel,
                IsParent = @IsParent,
                IsActive = @IsActive,
                AllowPosting = @AllowPosting,
                Description = @Description,
                CurrencyCode = @CurrencyCode,
                OpeningBalance = @OpeningBalance,
                ModifiedDate = GETDATE(),
                ModifiedBy = @UserId
            WHERE AccountId = @AccountId;
            
            SET @Message = N'تم تحديث الحساب بنجاح';
        END
        
        -- تحديث حالة الحساب الأب إذا لزم الأمر
        IF @ParentAccountId IS NOT NULL
        BEGIN
            UPDATE ChartOfAccounts
            SET IsParent = 1
            WHERE AccountId = @ParentAccountId;
        END
        
        COMMIT TRANSACTION;
        SET @Result = @AccountId;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @Result = -999;
        SET @Message = N'خطأ في حفظ الحساب: ' + ERROR_MESSAGE();
    END CATCH
END
GO

PRINT '✅ تم إنشاء الإجراءات المخزنة المحسنة بنجاح!';
