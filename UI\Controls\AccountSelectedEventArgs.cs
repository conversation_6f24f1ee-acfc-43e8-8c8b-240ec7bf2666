using System;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.UI.Controls
{
    /// <summary>
    /// معاملات حدث تحديد الحساب
    /// Account Selected Event Arguments
    /// </summary>
    public class AccountSelectedEventArgs : EventArgs
    {
        #region الخصائص - Properties
        
        /// <summary>
        /// الحساب المحدد
        /// Selected Account
        /// </summary>
        public ChartOfAccount Account { get; }

        /// <summary>
        /// نوع الحدث
        /// Event Type
        /// </summary>
        public AccountSelectionType SelectionType { get; }

        /// <summary>
        /// معلومات إضافية
        /// Additional Information
        /// </summary>
        public object Tag { get; set; }

        #endregion

        #region المنشئات - Constructors
        
        /// <summary>
        /// منشئ أساسي
        /// Basic Constructor
        /// </summary>
        /// <param name="account">الحساب المحدد</param>
        public AccountSelectedEventArgs(ChartOfAccount account) 
            : this(account, AccountSelectionType.Selected)
        {
        }

        /// <summary>
        /// منشئ مع نوع التحديد
        /// Constructor with Selection Type
        /// </summary>
        /// <param name="account">الحساب المحدد</param>
        /// <param name="selectionType">نوع التحديد</param>
        public AccountSelectedEventArgs(ChartOfAccount account, AccountSelectionType selectionType)
        {
            Account = account;
            SelectionType = selectionType;
        }

        /// <summary>
        /// منشئ كامل
        /// Full Constructor
        /// </summary>
        /// <param name="account">الحساب المحدد</param>
        /// <param name="selectionType">نوع التحديد</param>
        /// <param name="tag">معلومات إضافية</param>
        public AccountSelectedEventArgs(ChartOfAccount account, AccountSelectionType selectionType, object tag)
            : this(account, selectionType)
        {
            Tag = tag;
        }

        #endregion

        #region الطرق - Methods
        
        /// <summary>
        /// التحقق من وجود حساب محدد
        /// Check if Account is Selected
        /// </summary>
        /// <returns>هل يوجد حساب محدد</returns>
        public bool HasAccount()
        {
            return Account != null;
        }

        /// <summary>
        /// التحقق من نوع التحديد
        /// Check Selection Type
        /// </summary>
        /// <param name="type">النوع المراد التحقق منه</param>
        /// <returns>هل النوع مطابق</returns>
        public bool IsSelectionType(AccountSelectionType type)
        {
            return SelectionType == type;
        }

        /// <summary>
        /// الحصول على معرف الحساب
        /// Get Account ID
        /// </summary>
        /// <returns>معرف الحساب أو null</returns>
        public int? GetAccountId()
        {
            return Account?.AccountId;
        }

        /// <summary>
        /// الحصول على رمز الحساب
        /// Get Account Code
        /// </summary>
        /// <returns>رمز الحساب أو نص فارغ</returns>
        public string GetAccountCode()
        {
            return Account?.AccountCode ?? string.Empty;
        }

        /// <summary>
        /// الحصول على اسم الحساب
        /// Get Account Name
        /// </summary>
        /// <returns>اسم الحساب أو نص فارغ</returns>
        public string GetAccountName()
        {
            return Account?.AccountNameAr ?? string.Empty;
        }

        /// <summary>
        /// التمثيل النصي
        /// String Representation
        /// </summary>
        /// <returns>النص الممثل للحدث</returns>
        public override string ToString()
        {
            if (Account == null)
                return $"AccountSelectedEventArgs: No Account, Type: {SelectionType}";
            
            return $"AccountSelectedEventArgs: {Account.AccountCode} - {Account.AccountNameAr}, Type: {SelectionType}";
        }

        #endregion
    }

    /// <summary>
    /// أنواع تحديد الحساب
    /// Account Selection Types
    /// </summary>
    public enum AccountSelectionType
    {
        /// <summary>
        /// تحديد عادي
        /// Normal Selection
        /// </summary>
        Selected,

        /// <summary>
        /// نقر مزدوج
        /// Double Click
        /// </summary>
        DoubleClicked,

        /// <summary>
        /// نقر بالزر الأيمن
        /// Right Click
        /// </summary>
        RightClicked,

        /// <summary>
        /// تحديد بالبرمجة
        /// Programmatic Selection
        /// </summary>
        Programmatic,

        /// <summary>
        /// تحديد من البحث
        /// Search Selection
        /// </summary>
        SearchResult,

        /// <summary>
        /// تحديد من الفلترة
        /// Filter Selection
        /// </summary>
        FilterResult
    }
}
