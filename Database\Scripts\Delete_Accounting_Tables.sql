-- ========================================
-- سكريبت حذف جداول النظام المحاسبي
-- Delete Accounting System Tables Script
-- ========================================
-- قاعدة البيانات: AwqafManagement
-- الخادم: NAJEEB
-- ========================================

USE AwqafManagement;
GO

PRINT '========================================';
PRINT 'بدء حذف جداول النظام المحاسبي';
PRINT 'Starting deletion of accounting tables';
PRINT '========================================';

-- إيقاف فحص المفاتيح الخارجية مؤقتاً
EXEC sp_MSforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all";

-- ========================================
-- 1. حذف الجداول الفرعية أولاً (التي تحتوي على مفاتيح خارجية)
-- ========================================

-- حذف جدول تفاصيل القيود اليومية
IF OBJECT_ID('dbo.JournalEntryDetails', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول تفاصيل القيود اليومية...';
    PRINT 'Deleting JournalEntryDetails table...';
    DROP TABLE dbo.JournalEntryDetails;
    PRINT '✅ تم حذف جدول JournalEntryDetails بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول JournalEntryDetails غير موجود';
END

-- حذف جدول القيود اليومية
IF OBJECT_ID('dbo.JournalEntries', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول القيود اليومية...';
    PRINT 'Deleting JournalEntries table...';
    DROP TABLE dbo.JournalEntries;
    PRINT '✅ تم حذف جدول JournalEntries بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول JournalEntries غير موجود';
END

-- حذف جدول سجل مراجعة الحسابات
IF OBJECT_ID('dbo.AccountAuditLog', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول سجل مراجعة الحسابات...';
    PRINT 'Deleting AccountAuditLog table...';
    DROP TABLE dbo.AccountAuditLog;
    PRINT '✅ تم حذف جدول AccountAuditLog بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول AccountAuditLog غير موجود';
END

-- حذف جدول المعلومات الشخصية
IF OBJECT_ID('dbo.PersonalInformation', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول المعلومات الشخصية...';
    PRINT 'Deleting PersonalInformation table...';
    DROP TABLE dbo.PersonalInformation;
    PRINT '✅ تم حذف جدول PersonalInformation بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول PersonalInformation غير موجود';
END

-- حذف جدول دليل الحسابات
IF OBJECT_ID('dbo.ChartOfAccounts', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول دليل الحسابات...';
    PRINT 'Deleting ChartOfAccounts table...';
    DROP TABLE dbo.ChartOfAccounts;
    PRINT '✅ تم حذف جدول ChartOfAccounts بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول ChartOfAccounts غير موجود';
END

-- ========================================
-- 2. حذف الجداول الأساسية (المرجعية)
-- ========================================

-- حذف جدول العملات
IF OBJECT_ID('dbo.Currencies', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول العملات...';
    PRINT 'Deleting Currencies table...';
    DROP TABLE dbo.Currencies;
    PRINT '✅ تم حذف جدول Currencies بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول Currencies غير موجود';
END

-- حذف جدول مجموعات الحسابات
IF OBJECT_ID('dbo.AccountGroups', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول مجموعات الحسابات...';
    PRINT 'Deleting AccountGroups table...';
    DROP TABLE dbo.AccountGroups;
    PRINT '✅ تم حذف جدول AccountGroups بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول AccountGroups غير موجود';
END

-- حذف جدول أنواع الحسابات
IF OBJECT_ID('dbo.AccountTypes', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول أنواع الحسابات...';
    PRINT 'Deleting AccountTypes table...';
    DROP TABLE dbo.AccountTypes;
    PRINT '✅ تم حذف جدول AccountTypes بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول AccountTypes غير موجود';
END

-- حذف جدول أنواع القيود
IF OBJECT_ID('dbo.JournalTypes', 'U') IS NOT NULL
BEGIN
    PRINT 'حذف جدول أنواع القيود...';
    PRINT 'Deleting JournalTypes table...';
    DROP TABLE dbo.JournalTypes;
    PRINT '✅ تم حذف جدول JournalTypes بنجاح';
END
ELSE
BEGIN
    PRINT '⚠️ جدول JournalTypes غير موجود';
END

-- ========================================
-- 3. حذف الإجراءات المخزنة (Stored Procedures)
-- ========================================

-- حذف إجراءات دليل الحسابات
IF OBJECT_ID('dbo.SP_SearchAccounts', 'P') IS NOT NULL
BEGIN
    PRINT 'حذف إجراء البحث في الحسابات...';
    DROP PROCEDURE dbo.SP_SearchAccounts;
    PRINT '✅ تم حذف SP_SearchAccounts بنجاح';
END

IF OBJECT_ID('dbo.SP_GetAccountsHierarchy', 'P') IS NOT NULL
BEGIN
    PRINT 'حذف إجراء التسلسل الهرمي للحسابات...';
    DROP PROCEDURE dbo.SP_GetAccountsHierarchy;
    PRINT '✅ تم حذف SP_GetAccountsHierarchy بنجاح';
END

IF OBJECT_ID('dbo.SP_GenerateAccountCode', 'P') IS NOT NULL
BEGIN
    PRINT 'حذف إجراء توليد رمز الحساب...';
    DROP PROCEDURE dbo.SP_GenerateAccountCode;
    PRINT '✅ تم حذف SP_GenerateAccountCode بنجاح';
END

IF OBJECT_ID('dbo.SP_SaveAccount', 'P') IS NOT NULL
BEGIN
    PRINT 'حذف إجراء حفظ الحساب...';
    DROP PROCEDURE dbo.SP_SaveAccount;
    PRINT '✅ تم حذف SP_SaveAccount بنجاح';
END

-- حذف إجراءات القيود اليومية
IF OBJECT_ID('dbo.SP_SaveJournalEntry', 'P') IS NOT NULL
BEGIN
    PRINT 'حذف إجراء حفظ القيد اليومي...';
    DROP PROCEDURE dbo.SP_SaveJournalEntry;
    PRINT '✅ تم حذف SP_SaveJournalEntry بنجاح';
END

-- ========================================
-- 4. حذف المشغلات (Triggers)
-- ========================================

-- حذف مشغل مراجعة الحسابات
IF OBJECT_ID('dbo.TR_ChartOfAccounts_Audit', 'TR') IS NOT NULL
BEGIN
    PRINT 'حذف مشغل مراجعة الحسابات...';
    DROP TRIGGER dbo.TR_ChartOfAccounts_Audit;
    PRINT '✅ تم حذف TR_ChartOfAccounts_Audit بنجاح';
END

-- إعادة تفعيل فحص المفاتيح الخارجية
EXEC sp_MSforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all";

PRINT '========================================';
PRINT '✅ تم حذف جميع جداول النظام المحاسبي بنجاح';
PRINT '✅ All accounting system tables deleted successfully';
PRINT '========================================';

-- عرض الجداول المتبقية في قاعدة البيانات
PRINT 'الجداول المتبقية في قاعدة البيانات:';
PRINT 'Remaining tables in database:';
SELECT TABLE_NAME as 'اسم الجدول / Table Name'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

GO
