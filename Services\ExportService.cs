using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using Awqaf_Managment.DataAccess;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.Services
{
    /// <summary>
    /// خدمة التصدير والتقارير
    /// Export and Reporting Service
    /// </summary>
    public class ExportService
    {
        #region Private Fields

        private readonly ChartOfAccountsDataAccess _chartOfAccountsDataAccess;
        private readonly AccountTypeDataAccess _accountTypeDataAccess;
        private readonly AccountGroupDataAccess _accountGroupDataAccess;
        private readonly CurrencyDataAccess _currencyDataAccess;
        private readonly AuditLogDataAccess _auditLogDataAccess;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ خدمة التصدير
        /// Export Service Constructor
        /// </summary>
        public ExportService()
        {
            _chartOfAccountsDataAccess = new ChartOfAccountsDataAccess();
            _accountTypeDataAccess = new AccountTypeDataAccess();
            _accountGroupDataAccess = new AccountGroupDataAccess();
            _currencyDataAccess = new CurrencyDataAccess();
            _auditLogDataAccess = new AuditLogDataAccess();
        }

        #endregion

        #region Chart of Accounts Export

        /// <summary>
        /// تصدير دليل الحسابات إلى CSV
        /// Export Chart of Accounts to CSV
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="includeInactive">تضمين الحسابات غير النشطة</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<string> ExportChartOfAccountsToCSV(string filePath, bool includeInactive = false)
        {
            try
            {
                // الحصول على جميع الحسابات
                var searchModel = new AccountSearchModel
                {
                    IncludeInactive = includeInactive,
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };

                var searchResult = _chartOfAccountsDataAccess.SearchAccounts(searchModel);
                var accounts = searchResult.Accounts;

                // إنشاء محتوى CSV
                var csvContent = new StringBuilder();
                
                // إضافة العناوين
                csvContent.AppendLine("كود الحساب,اسم الحساب (عربي),اسم الحساب (إنجليزي),نوع الحساب,مجموعة الحساب,الحساب الأب,مستوى الحساب,حساب أب,نشط,يسمح بالترحيل,العملة,الرصيد الافتتاحي,الرصيد الحالي,الوصف,تاريخ الإنشاء,المنشئ");

                // إضافة البيانات
                foreach (var account in accounts)
                {
                    var line = $"\"{account.AccountCode}\"," +
                              $"\"{EscapeCsvValue(account.AccountNameAr)}\"," +
                              $"\"{EscapeCsvValue(account.AccountName)}\"," +
                              $"\"{EscapeCsvValue(account.AccountTypeName)}\"," +
                              $"\"{EscapeCsvValue(account.AccountGroupName)}\"," +
                              $"\"{EscapeCsvValue(account.ParentAccountName ?? "")}\"," +
                              $"{account.AccountLevel}," +
                              $"\"{(account.IsParent ? "نعم" : "لا")}\"," +
                              $"\"{(account.IsActive ? "نشط" : "غير نشط")}\"," +
                              $"\"{(account.AllowPosting ? "نعم" : "لا")}\"," +
                              $"\"{account.CurrencyCode}\"," +
                              $"{account.OpeningBalance:N2}," +
                              $"{account.CurrentBalance:N2}," +
                              $"\"{EscapeCsvValue(account.Description ?? "")}\"," +
                              $"\"{account.CreatedDate:yyyy-MM-dd HH:mm:ss}\"," +
                              $"\"{EscapeCsvValue(account.CreatedBy)}\"";

                    csvContent.AppendLine(line);
                }

                // كتابة الملف
                File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);

                return ServiceResult<string>.Success(filePath, $"تم تصدير {accounts.Count} حساب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في تصدير دليل الحسابات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير دليل الحسابات إلى Excel (XML format)
        /// Export Chart of Accounts to Excel (XML format)
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="includeInactive">تضمين الحسابات غير النشطة</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<string> ExportChartOfAccountsToExcel(string filePath, bool includeInactive = false)
        {
            try
            {
                // الحصول على جميع الحسابات
                var searchModel = new AccountSearchModel
                {
                    IncludeInactive = includeInactive,
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };

                var searchResult = _chartOfAccountsDataAccess.SearchAccounts(searchModel);
                var accounts = searchResult.Accounts;

                // إنشاء DataTable
                var dataTable = new DataTable("ChartOfAccounts");
                
                // إضافة الأعمدة
                dataTable.Columns.Add("كود الحساب", typeof(string));
                dataTable.Columns.Add("اسم الحساب (عربي)", typeof(string));
                dataTable.Columns.Add("اسم الحساب (إنجليزي)", typeof(string));
                dataTable.Columns.Add("نوع الحساب", typeof(string));
                dataTable.Columns.Add("مجموعة الحساب", typeof(string));
                dataTable.Columns.Add("الحساب الأب", typeof(string));
                dataTable.Columns.Add("مستوى الحساب", typeof(int));
                dataTable.Columns.Add("حساب أب", typeof(string));
                dataTable.Columns.Add("الحالة", typeof(string));
                dataTable.Columns.Add("يسمح بالترحيل", typeof(string));
                dataTable.Columns.Add("العملة", typeof(string));
                dataTable.Columns.Add("الرصيد الافتتاحي", typeof(decimal));
                dataTable.Columns.Add("الرصيد الحالي", typeof(decimal));
                dataTable.Columns.Add("الوصف", typeof(string));
                dataTable.Columns.Add("تاريخ الإنشاء", typeof(DateTime));
                dataTable.Columns.Add("المنشئ", typeof(string));

                // إضافة البيانات
                foreach (var account in accounts)
                {
                    var row = dataTable.NewRow();
                    row["كود الحساب"] = account.AccountCode;
                    row["اسم الحساب (عربي)"] = account.AccountNameAr;
                    row["اسم الحساب (إنجليزي)"] = account.AccountName;
                    row["نوع الحساب"] = account.AccountTypeName;
                    row["مجموعة الحساب"] = account.AccountGroupName;
                    row["الحساب الأب"] = account.ParentAccountName ?? "";
                    row["مستوى الحساب"] = account.AccountLevel;
                    row["حساب أب"] = account.IsParent ? "نعم" : "لا";
                    row["الحالة"] = account.IsActive ? "نشط" : "غير نشط";
                    row["يسمح بالترحيل"] = account.AllowPosting ? "نعم" : "لا";
                    row["العملة"] = account.CurrencyCode;
                    row["الرصيد الافتتاحي"] = account.OpeningBalance;
                    row["الرصيد الحالي"] = account.CurrentBalance;
                    row["الوصف"] = account.Description ?? "";
                    row["تاريخ الإنشاء"] = account.CreatedDate;
                    row["المنشئ"] = account.CreatedBy;
                    
                    dataTable.Rows.Add(row);
                }

                // تصدير إلى Excel XML
                ExportDataTableToExcelXml(dataTable, filePath, "دليل الحسابات");

                return ServiceResult<string>.Success(filePath, $"تم تصدير {accounts.Count} حساب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في تصدير دليل الحسابات إلى Excel: {ex.Message}");
            }
        }

        #endregion

        #region Account Types Export

        /// <summary>
        /// تصدير أنواع الحسابات
        /// Export Account Types
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="format">تنسيق التصدير</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<string> ExportAccountTypes(string filePath, ExportFormat format = ExportFormat.CSV)
        {
            try
            {
                var accountTypes = _accountTypeDataAccess.GetAllAccountTypes();

                if (format == ExportFormat.CSV)
                {
                    return ExportAccountTypesToCSV(accountTypes, filePath);
                }
                else
                {
                    return ExportAccountTypesToExcel(accountTypes, filePath);
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في تصدير أنواع الحسابات: {ex.Message}");
            }
        }

        #endregion

        #region Audit Logs Export

        /// <summary>
        /// تصدير سجلات التدقيق
        /// Export Audit Logs
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="format">تنسيق التصدير</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<string> ExportAuditLogs(string filePath, DateTime? fromDate = null, DateTime? toDate = null, 
            string userId = null, ExportFormat format = ExportFormat.CSV)
        {
            try
            {
                var auditLogs = _auditLogDataAccess.SearchAuditLogs(null, null, fromDate, toDate, userId, 1, int.MaxValue);

                if (format == ExportFormat.CSV)
                {
                    return ExportAuditLogsToCSV(auditLogs, filePath);
                }
                else
                {
                    return ExportAuditLogsToExcel(auditLogs, filePath);
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في تصدير سجلات التدقيق: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تنظيف قيمة CSV
        /// Escape CSV Value
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <returns>القيمة المنظفة</returns>
        private string EscapeCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            // استبدال علامات الاقتباس المزدوجة
            return value.Replace("\"", "\"\"");
        }

        /// <summary>
        /// تصدير أنواع الحسابات إلى CSV
        /// Export Account Types to CSV
        /// </summary>
        /// <param name="accountTypes">أنواع الحسابات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نتيجة العملية</returns>
        private ServiceResult<string> ExportAccountTypesToCSV(List<AccountType> accountTypes, string filePath)
        {
            var csvContent = new StringBuilder();
            csvContent.AppendLine("كود النوع,اسم النوع (عربي),اسم النوع (إنجليزي),الوصف,الحالة,تاريخ الإنشاء");

            foreach (var type in accountTypes)
            {
                var line = $"\"{type.TypeCode}\"," +
                          $"\"{EscapeCsvValue(type.TypeNameAr)}\"," +
                          $"\"{EscapeCsvValue(type.TypeName)}\"," +
                          $"\"{EscapeCsvValue(type.Description ?? "")}\"," +
                          $"\"{(type.IsActive ? "نشط" : "غير نشط")}\"," +
                          $"\"{type.CreatedDate:yyyy-MM-dd HH:mm:ss}\"";

                csvContent.AppendLine(line);
            }

            File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);
            return ServiceResult<string>.Success(filePath, $"تم تصدير {accountTypes.Count} نوع حساب");
        }

        /// <summary>
        /// تصدير أنواع الحسابات إلى Excel
        /// Export Account Types to Excel
        /// </summary>
        /// <param name="accountTypes">أنواع الحسابات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نتيجة العملية</returns>
        private ServiceResult<string> ExportAccountTypesToExcel(List<AccountType> accountTypes, string filePath)
        {
            var dataTable = new DataTable("AccountTypes");
            dataTable.Columns.Add("كود النوع", typeof(string));
            dataTable.Columns.Add("اسم النوع (عربي)", typeof(string));
            dataTable.Columns.Add("اسم النوع (إنجليزي)", typeof(string));
            dataTable.Columns.Add("الوصف", typeof(string));
            dataTable.Columns.Add("الحالة", typeof(string));
            dataTable.Columns.Add("تاريخ الإنشاء", typeof(DateTime));

            foreach (var type in accountTypes)
            {
                var row = dataTable.NewRow();
                row["كود النوع"] = type.TypeCode;
                row["اسم النوع (عربي)"] = type.TypeNameAr;
                row["اسم النوع (إنجليزي)"] = type.TypeName;
                row["الوصف"] = type.Description ?? "";
                row["الحالة"] = type.IsActive ? "نشط" : "غير نشط";
                row["تاريخ الإنشاء"] = type.CreatedDate;
                dataTable.Rows.Add(row);
            }

            ExportDataTableToExcelXml(dataTable, filePath, "أنواع الحسابات");
            return ServiceResult<string>.Success(filePath, $"تم تصدير {accountTypes.Count} نوع حساب");
        }

        /// <summary>
        /// تصدير سجلات التدقيق إلى CSV
        /// Export Audit Logs to CSV
        /// </summary>
        /// <param name="auditLogs">سجلات التدقيق</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نتيجة العملية</returns>
        private ServiceResult<string> ExportAuditLogsToCSV(List<AccountAuditLog> auditLogs, string filePath)
        {
            var csvContent = new StringBuilder();
            csvContent.AppendLine("معرف الحساب,العملية,اسم الحقل,القيمة القديمة,القيمة الجديدة,سبب التغيير,تاريخ التغيير,المستخدم,عنوان IP");

            foreach (var log in auditLogs)
            {
                var line = $"{log.AccountId}," +
                          $"\"{log.Operation}\"," +
                          $"\"{EscapeCsvValue(log.FieldName ?? "")}\"," +
                          $"\"{EscapeCsvValue(log.OldValue ?? "")}\"," +
                          $"\"{EscapeCsvValue(log.NewValue ?? "")}\"," +
                          $"\"{EscapeCsvValue(log.ChangeReason ?? "")}\"," +
                          $"\"{log.ChangeDate:yyyy-MM-dd HH:mm:ss}\"," +
                          $"\"{EscapeCsvValue(log.UserName)}\"," +
                          $"\"{log.IPAddress ?? ""}\"";

                csvContent.AppendLine(line);
            }

            File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);
            return ServiceResult<string>.Success(filePath, $"تم تصدير {auditLogs.Count} سجل تدقيق");
        }

        /// <summary>
        /// تصدير سجلات التدقيق إلى Excel
        /// Export Audit Logs to Excel
        /// </summary>
        /// <param name="auditLogs">سجلات التدقيق</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نتيجة العملية</returns>
        private ServiceResult<string> ExportAuditLogsToExcel(List<AccountAuditLog> auditLogs, string filePath)
        {
            var dataTable = new DataTable("AuditLogs");
            dataTable.Columns.Add("معرف الحساب", typeof(int));
            dataTable.Columns.Add("العملية", typeof(string));
            dataTable.Columns.Add("اسم الحقل", typeof(string));
            dataTable.Columns.Add("القيمة القديمة", typeof(string));
            dataTable.Columns.Add("القيمة الجديدة", typeof(string));
            dataTable.Columns.Add("سبب التغيير", typeof(string));
            dataTable.Columns.Add("تاريخ التغيير", typeof(DateTime));
            dataTable.Columns.Add("المستخدم", typeof(string));
            dataTable.Columns.Add("عنوان IP", typeof(string));

            foreach (var log in auditLogs)
            {
                var row = dataTable.NewRow();
                row["معرف الحساب"] = log.AccountId;
                row["العملية"] = log.Operation;
                row["اسم الحقل"] = log.FieldName ?? "";
                row["القيمة القديمة"] = log.OldValue ?? "";
                row["القيمة الجديدة"] = log.NewValue ?? "";
                row["سبب التغيير"] = log.ChangeReason ?? "";
                row["تاريخ التغيير"] = log.ChangeDate;
                row["المستخدم"] = log.UserName;
                row["عنوان IP"] = log.IPAddress ?? "";
                dataTable.Rows.Add(row);
            }

            ExportDataTableToExcelXml(dataTable, filePath, "سجلات التدقيق");
            return ServiceResult<string>.Success(filePath, $"تم تصدير {auditLogs.Count} سجل تدقيق");
        }

        /// <summary>
        /// تصدير DataTable إلى Excel XML
        /// Export DataTable to Excel XML
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="worksheetName">اسم ورقة العمل</param>
        private void ExportDataTableToExcelXml(DataTable dataTable, string filePath, string worksheetName)
        {
            var xmlContent = new StringBuilder();
            
            // XML Header
            xmlContent.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            xmlContent.AppendLine("<?mso-application progid=\"Excel.Sheet\"?>");
            xmlContent.AppendLine("<Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\"");
            xmlContent.AppendLine(" xmlns:o=\"urn:schemas-microsoft-com:office:office\"");
            xmlContent.AppendLine(" xmlns:x=\"urn:schemas-microsoft-com:office:excel\"");
            xmlContent.AppendLine(" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"");
            xmlContent.AppendLine(" xmlns:html=\"http://www.w3.org/TR/REC-html40\">");

            // Styles
            xmlContent.AppendLine("<Styles>");
            xmlContent.AppendLine("<Style ss:ID=\"Header\">");
            xmlContent.AppendLine("<Font ss:Bold=\"1\"/>");
            xmlContent.AppendLine("<Interior ss:Color=\"#CCCCCC\" ss:Pattern=\"Solid\"/>");
            xmlContent.AppendLine("</Style>");
            xmlContent.AppendLine("</Styles>");

            // Worksheet
            xmlContent.AppendLine($"<Worksheet ss:Name=\"{worksheetName}\">");
            xmlContent.AppendLine("<Table>");

            // Header Row
            xmlContent.AppendLine("<Row>");
            foreach (DataColumn column in dataTable.Columns)
            {
                xmlContent.AppendLine($"<Cell ss:StyleID=\"Header\"><Data ss:Type=\"String\">{System.Security.SecurityElement.Escape(column.ColumnName)}</Data></Cell>");
            }
            xmlContent.AppendLine("</Row>");

            // Data Rows
            foreach (DataRow row in dataTable.Rows)
            {
                xmlContent.AppendLine("<Row>");
                foreach (var item in row.ItemArray)
                {
                    var value = item?.ToString() ?? "";
                    var dataType = GetExcelDataType(item);
                    xmlContent.AppendLine($"<Cell><Data ss:Type=\"{dataType}\">{System.Security.SecurityElement.Escape(value)}</Data></Cell>");
                }
                xmlContent.AppendLine("</Row>");
            }

            xmlContent.AppendLine("</Table>");
            xmlContent.AppendLine("</Worksheet>");
            xmlContent.AppendLine("</Workbook>");

            File.WriteAllText(filePath, xmlContent.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// الحصول على نوع البيانات في Excel
        /// Get Excel Data Type
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <returns>نوع البيانات</returns>
        private string GetExcelDataType(object value)
        {
            if (value == null || value == DBNull.Value)
                return "String";

            var type = value.GetType();
            
            if (type == typeof(int) || type == typeof(long) || type == typeof(short))
                return "Number";
            
            if (type == typeof(decimal) || type == typeof(double) || type == typeof(float))
                return "Number";
            
            if (type == typeof(DateTime))
                return "DateTime";
            
            if (type == typeof(bool))
                return "Boolean";
            
            return "String";
        }

        #endregion
    }

    /// <summary>
    /// تنسيقات التصدير
    /// Export Formats
    /// </summary>
    public enum ExportFormat
    {
        CSV,
        Excel
    }
}
