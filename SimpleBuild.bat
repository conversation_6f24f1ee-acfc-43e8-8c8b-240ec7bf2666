@echo off
echo Building Awqaf Management System...
echo.

REM Create output directory
if not exist "bin\Debug" mkdir "bin\Debug"

REM Compile the system
csc /target:winexe /reference:System.dll /reference:System.Windows.Forms.dll /reference:System.Drawing.dll /reference:System.Data.dll /reference:System.ComponentModel.DataAnnotations.dll /reference:System.Data.SqlClient.dll /reference:System.Configuration.dll /out:"bin\Debug\AwqafManagement.exe" /debug+ /define:DEBUG "TestSimpleRun.cs" "DatabaseConfig.cs" "UI\Forms\TestMainForm.cs" "UI\Forms\Accounting\ChartOfAccountsManagementForm.cs" "UI\Forms\Accounting\ChartOfAccountsManagementForm.Designer.cs" "UI\Controls\EnhancedAccountsTreeView.cs" "UI\Controls\EnhancedAccountsTreeView.Designer.cs" "UI\Controls\AccountSelectedEventArgs.cs" "Models\ChartOfAccount.cs" "Models\AccountType.cs" "Models\AccountGroup.cs" "Models\Currency.cs" "Models\PersonalInformation.cs" "DataAccess\Base\BaseDataAccess.cs" "DataAccess\DatabaseConnection.cs" "DataAccess\ChartOfAccountsDataAccess.cs" "DataAccess\AccountTypeDataAccess.cs" "DataAccess\AccountGroupDataAccess.cs" "DataAccess\CurrencyDataAccess.cs" "DataAccess\PersonalInformationDataAccess.cs" "Services\ServiceResult.cs" "Services\ValidationService.cs" "Services\BusinessRulesService.cs"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Compilation successful!
    echo Running system...
    start "" "bin\Debug\AwqafManagement.exe"
) else (
    echo.
    echo Compilation failed!
    echo Check errors above
)

echo.
pause
