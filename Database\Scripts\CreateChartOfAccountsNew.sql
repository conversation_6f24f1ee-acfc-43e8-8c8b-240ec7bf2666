-- =============================================
-- إنشاء جداول دليل الحسابات المحسن
-- Create Enhanced Chart of Accounts Tables
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. إنشاء جدول أنواع الحسابات
-- Create Account Types Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountTypes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountTypes](
        [AccountTypeId] [int] IDENTITY(1,1) NOT NULL,
        [TypeCode] [nvarchar](10) NOT NULL,
        [TypeNameAr] [nvarchar](100) NOT NULL,
        [TypeNameEn] [nvarchar](100) NULL,
        [Nature] [nvarchar](20) NOT NULL, -- Debit, Credit
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [CreatedDate] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        
        CONSTRAINT [PK_AccountTypes] PRIMARY KEY CLUSTERED ([AccountTypeId] ASC),
        CONSTRAINT [UK_AccountTypes_TypeCode] UNIQUE ([TypeCode])
    );
    
    PRINT N'تم إنشاء جدول أنواع الحسابات';
END
GO

-- =============================================
-- 2. إنشاء جدول مجموعات الحسابات
-- Create Account Groups Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountGroups]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountGroups](
        [AccountGroupId] [int] IDENTITY(1,1) NOT NULL,
        [AccountTypeId] [int] NOT NULL,
        [GroupCode] [nvarchar](10) NOT NULL,
        [GroupNameAr] [nvarchar](100) NOT NULL,
        [GroupNameEn] [nvarchar](100) NULL,
        [Description] [nvarchar](500) NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [CreatedDate] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        
        CONSTRAINT [PK_AccountGroups] PRIMARY KEY CLUSTERED ([AccountGroupId] ASC),
        CONSTRAINT [UK_AccountGroups_GroupCode] UNIQUE ([GroupCode]),
        CONSTRAINT [FK_AccountGroups_AccountTypes] FOREIGN KEY([AccountTypeId]) 
            REFERENCES [dbo].[AccountTypes] ([AccountTypeId])
    );
    
    PRINT N'تم إنشاء جدول مجموعات الحسابات';
END
GO

-- =============================================
-- 3. إنشاء جدول دليل الحسابات الرئيسي
-- Create Main Chart of Accounts Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ChartOfAccounts](
        [AccountId] [int] IDENTITY(1,1) NOT NULL,
        [AccountCode] [nvarchar](20) NOT NULL,
        [AccountNameAr] [nvarchar](200) NOT NULL,
        [AccountNameEn] [nvarchar](200) NULL,
        [AccountTypeId] [int] NOT NULL,
        [AccountGroupId] [int] NOT NULL,
        [ParentAccountId] [int] NULL,
        [AccountLevel] [int] NOT NULL DEFAULT(1),
        [IsParent] [bit] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [AllowPosting] [bit] NOT NULL DEFAULT(1),
        [Description] [nvarchar](500) NULL,
        [CurrencyCode] [nvarchar](3) NOT NULL DEFAULT('SAR'),
        [OpeningBalance] [decimal](18,4) NOT NULL DEFAULT(0),
        [CurrentBalance] [decimal](18,4) NOT NULL DEFAULT(0),
        [CreatedDate] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        
        CONSTRAINT [PK_ChartOfAccounts] PRIMARY KEY CLUSTERED ([AccountId] ASC),
        CONSTRAINT [UK_ChartOfAccounts_AccountCode] UNIQUE ([AccountCode]),
        CONSTRAINT [FK_ChartOfAccounts_AccountTypes] FOREIGN KEY([AccountTypeId]) 
            REFERENCES [dbo].[AccountTypes] ([AccountTypeId]),
        CONSTRAINT [FK_ChartOfAccounts_AccountGroups] FOREIGN KEY([AccountGroupId]) 
            REFERENCES [dbo].[AccountGroups] ([AccountGroupId]),
        CONSTRAINT [FK_ChartOfAccounts_ParentAccount] FOREIGN KEY([ParentAccountId]) 
            REFERENCES [dbo].[ChartOfAccounts] ([AccountId]),
        CONSTRAINT [FK_ChartOfAccounts_Currencies] FOREIGN KEY([CurrencyCode]) 
            REFERENCES [dbo].[Currencies] ([CurrencyCode])
    );
END
GO

-- =============================================
-- 4. إنشاء الفهارس لتحسين الأداء
-- Create Indexes for Performance
-- =============================================

-- فهرس على رمز الحساب
CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_AccountCode] 
ON [dbo].[ChartOfAccounts] ([AccountCode] ASC);

-- فهرس على نوع الحساب
CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_AccountType] 
ON [dbo].[ChartOfAccounts] ([AccountTypeId] ASC);

-- فهرس على مجموعة الحساب
CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_AccountGroup] 
ON [dbo].[ChartOfAccounts] ([AccountGroupId] ASC);

-- فهرس على الحساب الأب
CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_ParentAccount] 
ON [dbo].[ChartOfAccounts] ([ParentAccountId] ASC);

-- فهرس على مستوى الحساب
CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_Level] 
ON [dbo].[ChartOfAccounts] ([AccountLevel] ASC);

-- فهرس على الحسابات النشطة
CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_Active] 
ON [dbo].[ChartOfAccounts] ([IsActive] ASC) WHERE [IsActive] = 1;

-- =============================================
-- 5. إنشاء الإجراءات المخزنة
-- Create Stored Procedures
-- =============================================

-- إجراء للحصول على الحسابات الهرمية
IF OBJECT_ID('[dbo].[sp_GetAccountsHierarchy]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[sp_GetAccountsHierarchy];
GO

CREATE PROCEDURE [dbo].[sp_GetAccountsHierarchy]
AS
BEGIN
    SET NOCOUNT ON;
    
    WITH AccountHierarchy AS (
        -- الحسابات الرئيسية (المستوى الأول)
        SELECT 
            AccountId, AccountCode, AccountNameAr, AccountNameEn,
            AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel,
            IsParent, IsActive, AllowPosting, Description,
            CurrencyCode, OpeningBalance, CurrentBalance,
            CAST(AccountNameAr AS NVARCHAR(MAX)) AS HierarchyPath,
            0 AS SortOrder
        FROM ChartOfAccounts 
        WHERE ParentAccountId IS NULL
        
        UNION ALL
        
        -- الحسابات الفرعية
        SELECT 
            c.AccountId, c.AccountCode, c.AccountNameAr, c.AccountNameEn,
            c.AccountTypeId, c.AccountGroupId, c.ParentAccountId, c.AccountLevel,
            c.IsParent, c.IsActive, c.AllowPosting, c.Description,
            c.CurrencyCode, c.OpeningBalance, c.CurrentBalance,
            CAST(h.HierarchyPath + ' > ' + c.AccountNameAr AS NVARCHAR(MAX)),
            h.SortOrder + 1
        FROM ChartOfAccounts c
        INNER JOIN AccountHierarchy h ON c.ParentAccountId = h.AccountId
    )
    SELECT * FROM AccountHierarchy
    ORDER BY AccountCode;
END
GO

-- إجراء للتحقق من صحة رمز الحساب
IF OBJECT_ID('[dbo].[sp_ValidateAccountCode]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[sp_ValidateAccountCode];
GO

CREATE PROCEDURE [dbo].[sp_ValidateAccountCode]
    @AccountCode NVARCHAR(20),
    @AccountId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @IsValid BIT = 1;
    DECLARE @ErrorMessage NVARCHAR(500) = '';
    
    -- التحقق من التنسيق
    IF @AccountCode NOT LIKE '[0-9].[0-9][0-9].[0-9][0-9][0-9]' 
       AND @AccountCode NOT LIKE '[0-9][0-9].[0-9][0-9].[0-9][0-9][0-9]'
    BEGIN
        SET @IsValid = 0;
        SET @ErrorMessage = N'تنسيق رمز الحساب غير صحيح. يجب أن يكون بالشكل: X.XX.XXX أو XX.XX.XXX';
    END
    
    -- التحقق من عدم التكرار
    IF @IsValid = 1 AND EXISTS (
        SELECT 1 FROM ChartOfAccounts 
        WHERE AccountCode = @AccountCode 
        AND (@AccountId IS NULL OR AccountId != @AccountId)
    )
    BEGIN
        SET @IsValid = 0;
        SET @ErrorMessage = N'رمز الحساب موجود مسبقاً';
    END
    
    SELECT @IsValid AS IsValid, @ErrorMessage AS ErrorMessage;
END
GO

PRINT N'تم إنشاء جداول دليل الحسابات المحسن بنجاح';
PRINT N'Enhanced Chart of Accounts tables created successfully';
