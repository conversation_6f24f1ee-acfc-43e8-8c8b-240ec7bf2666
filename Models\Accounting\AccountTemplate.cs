using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج قوالب الحسابات
    /// Account Templates Model
    /// </summary>
    public class AccountTemplate
    {
        #region المعرف الأساسي - Primary Identifier
        
        /// <summary>
        /// معرف القالب
        /// Template ID
        /// </summary>
        public int TemplateId { get; set; }

        #endregion

        #region معلومات القالب الأساسية - Basic Template Information
        
        /// <summary>
        /// اسم القالب
        /// Template Name
        /// </summary>
        [Required(ErrorMessage = "اسم القالب مطلوب")]
        [StringLength(200, ErrorMessage = "اسم القالب يجب ألا يزيد عن 200 حرف")]
        public string TemplateName { get; set; }

        /// <summary>
        /// وصف القالب
        /// Template Description
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف القالب يجب ألا يزيد عن 1000 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// نوع الصناعة أو القطاع
        /// Industry Type
        /// </summary>
        [StringLength(100, ErrorMessage = "نوع الصناعة يجب ألا يزيد عن 100 حرف")]
        public string IndustryType { get; set; }

        #endregion

        #region بيانات القالب - Template Data
        
        /// <summary>
        /// بيانات الحسابات بصيغة JSON
        /// Account Data in JSON Format
        /// </summary>
        public string TemplateData { get; set; }

        /// <summary>
        /// عدد الحسابات في القالب
        /// Number of Accounts in Template
        /// </summary>
        public int AccountsCount { get; set; } = 0;

        #endregion

        #region حالة القالب - Template Status
        
        /// <summary>
        /// هل القالب نشط
        /// Is Template Active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل القالب افتراضي
        /// Is Default Template
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// هل القالب مدمج في النظام
        /// Is System Built-in Template
        /// </summary>
        public bool IsSystemBuiltIn { get; set; } = false;

        #endregion

        #region معلومات التدقيق - Audit Information
        
        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// معرف منشئ القالب
        /// Created By User ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// اسم منشئ القالب
        /// Created By User Name
        /// </summary>
        public string CreatedByName { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف معدل القالب
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }

        /// <summary>
        /// اسم معدل القالب
        /// Modified By User Name
        /// </summary>
        public string ModifiedByName { get; set; }

        /// <summary>
        /// تاريخ آخر استخدام
        /// Last Used Date
        /// </summary>
        public DateTime? LastUsedDate { get; set; }

        /// <summary>
        /// عدد مرات الاستخدام
        /// Usage Count
        /// </summary>
        public int UsageCount { get; set; } = 0;

        #endregion

        #region خصائص محسوبة - Calculated Properties
        
        /// <summary>
        /// حالة القالب كنص
        /// Template Status as Text
        /// </summary>
        public string StatusText => IsActive ? "نشط" : "غير نشط";

        /// <summary>
        /// نوع القالب كنص
        /// Template Type as Text
        /// </summary>
        public string TypeText
        {
            get
            {
                if (IsDefault) return "افتراضي";
                if (IsSystemBuiltIn) return "مدمج";
                return "مخصص";
            }
        }

        /// <summary>
        /// معلومات الاستخدام
        /// Usage Information
        /// </summary>
        public string UsageInfo
        {
            get
            {
                if (UsageCount == 0) return "لم يُستخدم بعد";
                var lastUsed = LastUsedDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
                return $"استُخدم {UsageCount} مرة، آخر استخدام: {lastUsed}";
            }
        }

        /// <summary>
        /// معلومات القالب الكاملة
        /// Full Template Information
        /// </summary>
        public string FullTemplateInfo => $"{TemplateName} ({IndustryType}) - {AccountsCount} حساب";

        #endregion

        #region الثوابت - Constants
        
        /// <summary>
        /// أنواع الصناعات المتاحة
        /// Available Industry Types
        /// </summary>
        public static class IndustryTypes
        {
            public const string Awqaf = "أوقاف";
            public const string Commercial = "تجاري";
            public const string Charity = "خيري";
            public const string RealEstate = "عقاري";
            public const string Manufacturing = "صناعي";
            public const string Services = "خدمي";
            public const string Healthcare = "صحي";
            public const string Education = "تعليمي";
            public const string Government = "حكومي";
            public const string NonProfit = "غير ربحي";
        }

        /// <summary>
        /// قائمة أنواع الصناعات
        /// Industry Types List
        /// </summary>
        public static List<string> GetIndustryTypesList()
        {
            return new List<string>
            {
                IndustryTypes.Awqaf,
                IndustryTypes.Commercial,
                IndustryTypes.Charity,
                IndustryTypes.RealEstate,
                IndustryTypes.Manufacturing,
                IndustryTypes.Services,
                IndustryTypes.Healthcare,
                IndustryTypes.Education,
                IndustryTypes.Government,
                IndustryTypes.NonProfit
            };
        }

        #endregion

        #region التحقق من صحة البيانات - Data Validation
        
        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        /// <returns>قائمة بالأخطاء إن وجدت</returns>
        public List<string> ValidateData()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(TemplateName))
                errors.Add("اسم القالب مطلوب");

            if (string.IsNullOrWhiteSpace(IndustryType))
                errors.Add("نوع الصناعة مطلوب");

            if (string.IsNullOrWhiteSpace(TemplateData))
                errors.Add("بيانات القالب مطلوبة");

            if (AccountsCount < 0)
                errors.Add("عدد الحسابات لا يمكن أن يكون سالباً");

            if (UsageCount < 0)
                errors.Add("عدد مرات الاستخدام لا يمكن أن يكون سالباً");

            return errors;
        }

        /// <summary>
        /// هل البيانات صحيحة
        /// Is Data Valid
        /// </summary>
        public bool IsValid => ValidateData().Count == 0;

        #endregion

        #region طرق مساعدة - Helper Methods
        
        /// <summary>
        /// تحديث عداد الاستخدام
        /// Update Usage Counter
        /// </summary>
        public void UpdateUsage()
        {
            UsageCount++;
            LastUsedDate = DateTime.Now;
        }

        /// <summary>
        /// إعادة تعيين عداد الاستخدام
        /// Reset Usage Counter
        /// </summary>
        public void ResetUsage()
        {
            UsageCount = 0;
            LastUsedDate = null;
        }

        #endregion
    }
}
