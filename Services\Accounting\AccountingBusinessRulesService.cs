using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Awqaf_Managment.Models;
using Awqaf_Managment.Services;

namespace Awqaf_Managment.Services.Accounting
{
    /// <summary>
    /// خدمة القواعد المحاسبية
    /// Accounting Business Rules Service
    /// </summary>
    public class AccountingBusinessRulesService
    {
        #region الثوابت - Constants
        
        /// <summary>
        /// الحد الأقصى لمستويات الحسابات
        /// Maximum Account Levels
        /// </summary>
        public const int MAX_ACCOUNT_LEVELS = 10;

        /// <summary>
        /// الحد الأقصى لطول رمز الحساب
        /// Maximum Account Code Length
        /// </summary>
        public const int MAX_ACCOUNT_CODE_LENGTH = 20;

        /// <summary>
        /// الحد الأقصى للرصيد
        /// Maximum Balance
        /// </summary>
        public const decimal MAX_BALANCE = ************.9999m;

        #endregion

        #region التحقق من القواعد المحاسبية - Accounting Rules Validation

        /// <summary>
        /// التحقق من القواعد المحاسبية للحساب
        /// Validate Account Accounting Rules
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public async Task<ServiceResult<bool>> ValidateAccountBusinessRulesAsync(ChartOfAccount account)
        {
            var errors = new List<string>();

            try
            {
                // التحقق من مستوى الحساب
                if (account.AccountLevel > MAX_ACCOUNT_LEVELS)
                {
                    errors.Add($"مستوى الحساب لا يمكن أن يتجاوز {MAX_ACCOUNT_LEVELS} مستويات");
                }

                // التحقق من طول رمز الحساب
                if (!string.IsNullOrEmpty(account.AccountCode) && account.AccountCode.Length > MAX_ACCOUNT_CODE_LENGTH)
                {
                    errors.Add($"رمز الحساب لا يمكن أن يتجاوز {MAX_ACCOUNT_CODE_LENGTH} حرف");
                }

                // التحقق من الرصيد
                if (Math.Abs(account.OpeningBalance) > MAX_BALANCE)
                {
                    errors.Add($"الرصيد الافتتاحي لا يمكن أن يتجاوز {MAX_BALANCE:N2}");
                }

                // التحقق من تطابق طبيعة الحساب مع نوع الحساب
                var natureValidation = ValidateAccountNature(account);
                if (!natureValidation.IsSuccess)
                {
                    errors.Add(natureValidation.Message);
                }

                // التحقق من قواعد الحساب الأب
                var parentValidation = await ValidateParentAccountRulesAsync(account);
                if (!parentValidation.IsSuccess)
                {
                    errors.Add(parentValidation.Message);
                }

                // التحقق من قواعد الحساب الفرعي
                var childValidation = ValidateChildAccountRules(account);
                if (!childValidation.IsSuccess)
                {
                    errors.Add(childValidation.Message);
                }

                // التحقق من قواعد العملة
                var currencyValidation = ValidateCurrencyRules(account);
                if (!currencyValidation.IsSuccess)
                {
                    errors.Add(currencyValidation.Message);
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.Failure(string.Join(", ", errors));
                }

                return ServiceResult<bool>.Success(true, "جميع القواعد المحاسبية صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من القواعد المحاسبية: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف الحساب
        /// Check if Account Can Be Deleted
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <param name="forceDelete">حذف قسري</param>
        /// <returns>نتيجة التحقق</returns>
        public async Task<ServiceResult<bool>> CanDeleteAccountAsync(ChartOfAccount account, bool forceDelete = false)
        {
            try
            {
                var errors = new List<string>();

                // التحقق من وجود حسابات فرعية
                if (account.IsParent && account.SubAccountsCount > 0 && !forceDelete)
                {
                    errors.Add("لا يمكن حذف الحساب لوجود حسابات فرعية. استخدم الحذف القسري إذا كنت متأكداً");
                }

                // التحقق من وجود حركات مالية (سيتم تنفيذه لاحقاً)
                // var hasTransactions = await CheckAccountTransactionsAsync(account.AccountId);
                // if (hasTransactions && !forceDelete)
                // {
                //     errors.Add("لا يمكن حذف الحساب لوجود حركات مالية. استخدم الحذف القسري إذا كنت متأكداً");
                // }

                // التحقق من الحسابات الأساسية المحمية
                if (IsProtectedSystemAccount(account))
                {
                    errors.Add("لا يمكن حذف الحسابات الأساسية للنظام");
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.Failure(string.Join(", ", errors));
                }

                return ServiceResult<bool>.Success(true, "يمكن حذف الحساب");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من إمكانية الحذف: {ex.Message}");
            }
        }

        #endregion

        #region قواعد التحقق المحددة - Specific Validation Rules

        /// <summary>
        /// التحقق من طبيعة الحساب
        /// Validate Account Nature
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private ServiceResult<bool> ValidateAccountNature(ChartOfAccount account)
        {
            try
            {
                // قواعد طبيعة الحساب حسب النوع
                var accountTypeNatureRules = new Dictionary<int, string>
                {
                    { 1, "مدين" },   // الأصول
                    { 2, "دائن" },   // الخصوم
                    { 3, "دائن" },   // حقوق الملكية
                    { 4, "دائن" },   // الإيرادات
                    { 5, "مدين" }    // المصروفات
                };

                if (accountTypeNatureRules.ContainsKey(account.AccountTypeId))
                {
                    var expectedNature = accountTypeNatureRules[account.AccountTypeId];
                    if (account.Nature != expectedNature)
                    {
                        return ServiceResult<bool>.Failure(
                            $"طبيعة الحساب يجب أن تكون '{expectedNature}' لهذا النوع من الحسابات");
                    }
                }

                return ServiceResult<bool>.Success(true, "طبيعة الحساب صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من طبيعة الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من قواعد الحساب الأب
        /// Validate Parent Account Rules
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private async Task<ServiceResult<bool>> ValidateParentAccountRulesAsync(ChartOfAccount account)
        {
            try
            {
                if (!account.ParentAccountId.HasValue)
                {
                    return ServiceResult<bool>.Success(true, "لا يوجد حساب أب");
                }

                // التحقق من عدم جعل الحساب أب لنفسه
                if (account.ParentAccountId == account.AccountId)
                {
                    return ServiceResult<bool>.Failure("لا يمكن جعل الحساب أب لنفسه");
                }

                // التحقق من عدم إنشاء دورة في الهيكل الهرمي
                // (سيتم تنفيذ هذا التحقق لاحقاً مع قاعدة البيانات)

                return ServiceResult<bool>.Success(true, "قواعد الحساب الأب صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من قواعد الحساب الأب: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من قواعد الحساب الفرعي
        /// Validate Child Account Rules
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private ServiceResult<bool> ValidateChildAccountRules(ChartOfAccount account)
        {
            try
            {
                // إذا كان الحساب أب، يجب ألا يسمح بالترحيل المباشر
                if (account.IsParent && account.AllowPosting)
                {
                    return ServiceResult<bool>.Failure(
                        "الحسابات الأب لا يمكن أن تسمح بالترحيل المباشر");
                }

                // إذا كان الحساب فرعي، يجب أن يسمح بالترحيل
                if (!account.IsParent && !account.AllowPosting)
                {
                    return ServiceResult<bool>.Failure(
                        "الحسابات الفرعية يجب أن تسمح بالترحيل");
                }

                return ServiceResult<bool>.Success(true, "قواعد الحساب الفرعي صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من قواعد الحساب الفرعي: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من قواعد العملة
        /// Validate Currency Rules
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private ServiceResult<bool> ValidateCurrencyRules(ChartOfAccount account)
        {
            try
            {
                // التحقق من وجود العملة
                if (!account.CurrencyId.HasValue || account.CurrencyId <= 0)
                {
                    return ServiceResult<bool>.Failure("العملة مطلوبة");
                }

                // التحقق من رمز العملة
                if (string.IsNullOrEmpty(account.CurrencyCode))
                {
                    return ServiceResult<bool>.Failure("رمز العملة مطلوب");
                }

                // التحقق من طول رمز العملة
                if (account.CurrencyCode.Length != 3)
                {
                    return ServiceResult<bool>.Failure("رمز العملة يجب أن يكون 3 أحرف");
                }

                return ServiceResult<bool>.Success(true, "قواعد العملة صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من قواعد العملة: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من كون الحساب محمي من النظام
        /// Check if Account is System Protected
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>هل الحساب محمي</returns>
        private bool IsProtectedSystemAccount(ChartOfAccount account)
        {
            // قائمة الحسابات المحمية (يمكن توسيعها)
            var protectedAccountCodes = new[]
            {
                "AST01", "AST02", "LIB01", "LIB02", "EQT01", "EQT02", "REV01", "EXP01"
            };

            return protectedAccountCodes.Contains(account.AccountCode);
        }

        #endregion

        #region قواعد التوازن المحاسبي - Accounting Balance Rules

        /// <summary>
        /// التحقق من التوازن المحاسبي
        /// Validate Accounting Balance
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccountingBalance(List<ChartOfAccount> accounts)
        {
            try
            {
                var totalDebits = accounts
                    .Where(a => a.Nature == "مدين")
                    .Sum(a => a.CurrentBalance);

                var totalCredits = accounts
                    .Where(a => a.Nature == "دائن")
                    .Sum(a => a.CurrentBalance);

                var difference = Math.Abs(totalDebits - totalCredits);
                const decimal tolerance = 0.01m; // هامش تسامح للفروقات الطفيفة

                if (difference > tolerance)
                {
                    return ServiceResult<bool>.Failure(
                        $"عدم توازن في الحسابات: إجمالي المدين {totalDebits:N2}، إجمالي الدائن {totalCredits:N2}، الفرق {difference:N2}");
                }

                return ServiceResult<bool>.Success(true, 
                    $"التوازن المحاسبي صحيح: إجمالي المدين {totalDebits:N2}، إجمالي الدائن {totalCredits:N2}");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من التوازن المحاسبي: {ex.Message}");
            }
        }

        /// <summary>
        /// حساب إجماليات الحسابات حسب النوع
        /// Calculate Account Totals by Type
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>إجماليات الحسابات</returns>
        public Dictionary<string, decimal> CalculateAccountTotalsByType(List<ChartOfAccount> accounts)
        {
            try
            {
                return accounts
                    .GroupBy(a => a.AccountTypeName ?? "غير محدد")
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(a => a.CurrentBalance)
                    );
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب إجماليات الحسابات: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
