using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.DataAccess.Base;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بمجموعات الحسابات
    /// Account Groups Data Access Layer
    /// </summary>
    public class AccountGroupDataAccess : BaseDataAccess
    {
        #region Account Group CRUD Operations

        /// <summary>
        /// الحصول على جميع مجموعات الحسابات
        /// Get All Account Groups
        /// </summary>
        /// <returns>قائمة مجموعات الحسابات</returns>
        public List<AccountGroup> GetAllAccountGroups()
        {
            try
            {
                var accountGroups = new List<AccountGroup>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAllAccountGroups"))
                {
                    while (reader.Read())
                    {
                        accountGroups.Add(MapAccountGroupFromReader(reader));
                    }
                }

                return accountGroups;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على مجموعات الحسابات");
                throw new Exception(HandleException(ex, "الحصول على مجموعات الحسابات"));
            }
        }

        /// <summary>
        /// الحصول على مجموعة حسابات بالمعرف
        /// Get Account Group by ID
        /// </summary>
        /// <param name="accountGroupId">معرف مجموعة الحسابات</param>
        /// <returns>بيانات مجموعة الحسابات</returns>
        public AccountGroup GetAccountGroupById(int accountGroupId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountGroupId", accountGroupId)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountGroupById", parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountGroupFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على مجموعة الحسابات");
                throw new Exception(HandleException(ex, "الحصول على مجموعة الحسابات"));
            }
        }

        /// <summary>
        /// الحصول على مجموعات الحسابات حسب النوع
        /// Get Account Groups by Type
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <returns>قائمة مجموعات الحسابات</returns>
        public List<AccountGroup> GetAccountGroupsByType(int accountTypeId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", accountTypeId)
                };

                var accountGroups = new List<AccountGroup>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountGroupsByType", parameters))
                {
                    while (reader.Read())
                    {
                        accountGroups.Add(MapAccountGroupFromReader(reader));
                    }
                }

                return accountGroups;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على مجموعات الحسابات حسب النوع");
                throw new Exception(HandleException(ex, "الحصول على مجموعات الحسابات حسب النوع"));
            }
        }

        /// <summary>
        /// الحصول على مجموعة حسابات بالكود
        /// Get Account Group by Code
        /// </summary>
        /// <param name="groupCode">كود مجموعة الحسابات</param>
        /// <returns>بيانات مجموعة الحسابات</returns>
        public AccountGroup GetAccountGroupByCode(string groupCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@GroupCode", groupCode, SqlDbType.NVarChar, 10)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountGroupByCode", parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountGroupFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على مجموعة الحسابات بالكود");
                throw new Exception(HandleException(ex, "الحصول على مجموعة الحسابات بالكود"));
            }
        }

        /// <summary>
        /// حفظ مجموعة حسابات جديدة أو تحديث موجودة
        /// Save New Account Group or Update Existing
        /// </summary>
        /// <param name="accountGroup">بيانات مجموعة الحسابات</param>
        /// <returns>معرف مجموعة الحسابات المحفوظة</returns>
        public int SaveAccountGroup(AccountGroup accountGroup)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountGroupId", accountGroup.AccountGroupId),
                    CreateParameter("@GroupCode", accountGroup.GroupCode, SqlDbType.NVarChar, 10),
                    CreateParameter("@GroupName", accountGroup.GroupName, SqlDbType.NVarChar, 100),
                    CreateParameter("@GroupNameAr", accountGroup.GroupNameAr, SqlDbType.NVarChar, 100),
                    CreateParameter("@AccountTypeId", accountGroup.AccountTypeId),
                    CreateParameter("@ParentGroupId", ConvertToDb(accountGroup.ParentGroupId)),
                    CreateParameter("@Description", ConvertToDb(accountGroup.Description), SqlDbType.NVarChar, 500),
                    CreateParameter("@IsActive", accountGroup.IsActive),
                    CreateParameter("@CreatedBy", accountGroup.CreatedBy, SqlDbType.NVarChar, 100),
                    CreateOutputParameter("@NewAccountGroupId", SqlDbType.Int)
                };

                ExecuteStoredProcedure("SP_SaveAccountGroup", parameters);
                
                return ConvertFromDb<int>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "حفظ مجموعة الحسابات");
                throw new Exception(HandleException(ex, "حفظ مجموعة الحسابات"));
            }
        }

        /// <summary>
        /// حذف مجموعة حسابات
        /// Delete Account Group
        /// </summary>
        /// <param name="accountGroupId">معرف مجموعة الحسابات</param>
        /// <returns>هل تم الحذف بنجاح</returns>
        public bool DeleteAccountGroup(int accountGroupId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountGroupId", accountGroupId)
                };

                int result = ExecuteStoredProcedure("SP_DeleteAccountGroup", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "حذف مجموعة الحسابات");
                throw new Exception(HandleException(ex, "حذف مجموعة الحسابات"));
            }
        }

        #endregion

        #region Account Group Hierarchy

        /// <summary>
        /// الحصول على مجموعات الحسابات الهرمية
        /// Get Account Groups Hierarchy
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="parentGroupId">معرف المجموعة الأب</param>
        /// <returns>قائمة مجموعات الحسابات الهرمية</returns>
        public List<AccountGroup> GetAccountGroupsHierarchy(int? accountTypeId = null, int? parentGroupId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", ConvertToDb(accountTypeId)),
                    CreateParameter("@ParentGroupId", ConvertToDb(parentGroupId))
                };

                var accountGroups = new List<AccountGroup>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountGroupsHierarchy", parameters))
                {
                    while (reader.Read())
                    {
                        accountGroups.Add(MapAccountGroupFromReader(reader));
                    }
                }

                return accountGroups;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على مجموعات الحسابات الهرمية");
                throw new Exception(HandleException(ex, "الحصول على مجموعات الحسابات الهرمية"));
            }
        }

        /// <summary>
        /// الحصول على المجموعات الفرعية
        /// Get Child Groups
        /// </summary>
        /// <param name="parentGroupId">معرف المجموعة الأب</param>
        /// <returns>قائمة المجموعات الفرعية</returns>
        public List<AccountGroup> GetChildGroups(int parentGroupId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@ParentGroupId", parentGroupId)
                };

                var accountGroups = new List<AccountGroup>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetChildGroups", parameters))
                {
                    while (reader.Read())
                    {
                        accountGroups.Add(MapAccountGroupFromReader(reader));
                    }
                }

                return accountGroups;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على المجموعات الفرعية");
                throw new Exception(HandleException(ex, "الحصول على المجموعات الفرعية"));
            }
        }

        #endregion

        #region Account Group Validation

        /// <summary>
        /// التحقق من وجود كود مجموعة الحسابات
        /// Check if Account Group Code Exists
        /// </summary>
        /// <param name="groupCode">كود مجموعة الحسابات</param>
        /// <param name="excludeId">معرف مجموعة الحسابات المستثناة من التحقق</param>
        /// <returns>هل الكود موجود</returns>
        public bool IsAccountGroupCodeExists(string groupCode, int? excludeId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@GroupCode", groupCode, SqlDbType.NVarChar, 10),
                    CreateParameter("@ExcludeId", ConvertToDb(excludeId))
                };

                var result = ExecuteStoredProcedureScalar("SP_CheckAccountGroupCodeExists", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من كود مجموعة الحسابات");
                throw new Exception(HandleException(ex, "التحقق من كود مجموعة الحسابات"));
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف مجموعة الحسابات
        /// Check if Account Group Can Be Deleted
        /// </summary>
        /// <param name="accountGroupId">معرف مجموعة الحسابات</param>
        /// <returns>هل يمكن الحذف</returns>
        public bool CanDeleteAccountGroup(int accountGroupId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountGroupId", accountGroupId)
                };

                var result = ExecuteStoredProcedureScalar("SP_CanDeleteAccountGroup", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من إمكانية حذف مجموعة الحسابات");
                throw new Exception(HandleException(ex, "التحقق من إمكانية حذف مجموعة الحسابات"));
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن مجموعة الحسابات
        /// Map Data Reader to Account Group Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن مجموعة الحسابات</returns>
        private AccountGroup MapAccountGroupFromReader(SqlDataReader reader)
        {
            return new AccountGroup
            {
                AccountGroupId = ConvertFromDb<int>(reader["AccountGroupId"]),
                GroupCode = ConvertFromDb<string>(reader["GroupCode"]),
                GroupName = ConvertFromDb<string>(reader["GroupName"]),
                GroupNameAr = ConvertFromDb<string>(reader["GroupNameAr"]),
                AccountTypeId = ConvertFromDb<int>(reader["AccountTypeId"]),
                ParentGroupId = ConvertFromDb<int?>(reader["ParentGroupId"]),
                GroupLevel = ConvertFromDb<int>(reader["GroupLevel"]),
                GroupPath = ConvertFromDb<string>(reader["GroupPath"]),
                IsParent = ConvertFromDb<bool>(reader["IsParent"]),
                Description = ConvertFromDb<string>(reader["Description"]),
                IsActive = ConvertFromDb<bool>(reader["IsActive"]),
                CreatedDate = ConvertFromDb<DateTime>(reader["CreatedDate"]),
                CreatedBy = ConvertFromDb<string>(reader["CreatedBy"]),
                ModifiedDate = ConvertFromDb<DateTime?>(reader["ModifiedDate"]),
                ModifiedBy = ConvertFromDb<string>(reader["ModifiedBy"])
            };
        }

        #endregion
    }
}
