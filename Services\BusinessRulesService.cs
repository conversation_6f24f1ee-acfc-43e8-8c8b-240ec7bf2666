using System;
using System.Collections.Generic;
using System.Linq;
using Awqaf_Managment.DataAccess;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.Services
{
    /// <summary>
    /// خدمة قواعد الأعمال المحاسبية
    /// Accounting Business Rules Service
    /// </summary>
    public class BusinessRulesService
    {
        #region Private Fields

        private readonly ChartOfAccountsDataAccess _chartOfAccountsDataAccess;
        private readonly AccountTypeDataAccess _accountTypeDataAccess;
        private readonly AccountGroupDataAccess _accountGroupDataAccess;
        private readonly CurrencyDataAccess _currencyDataAccess;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ خدمة قواعد الأعمال
        /// Business Rules Service Constructor
        /// </summary>
        public BusinessRulesService()
        {
            _chartOfAccountsDataAccess = new ChartOfAccountsDataAccess();
            _accountTypeDataAccess = new AccountTypeDataAccess();
            _accountGroupDataAccess = new AccountGroupDataAccess();
            _currencyDataAccess = new CurrencyDataAccess();
        }

        #endregion

        #region Account Posting Rules

        /// <summary>
        /// التحقق من إمكانية الترحيل للحساب
        /// Check if Account Can Allow Posting
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> CanAccountAllowPosting(int accountId)
        {
            try
            {
                var account = _chartOfAccountsDataAccess.GetAccountById(accountId);
                if (account == null)
                {
                    return ServiceResult<bool>.Failure("الحساب غير موجود");
                }

                // القاعدة: الحسابات الأب لا يمكن أن تسمح بالترحيل إذا كان لديها حسابات فرعية
                if (account.IsParent)
                {
                    var childAccounts = _chartOfAccountsDataAccess.GetChildAccounts(accountId);
                    if (childAccounts.Any())
                    {
                        return ServiceResult<bool>.Success(false, "الحسابات الأب التي تحتوي على حسابات فرعية لا يمكن أن تسمح بالترحيل");
                    }
                }

                // القاعدة: الحسابات غير النشطة لا يمكن أن تسمح بالترحيل
                if (!account.IsActive)
                {
                    return ServiceResult<bool>.Success(false, "الحسابات غير النشطة لا يمكن أن تسمح بالترحيل");
                }

                return ServiceResult<bool>.Success(true, "يمكن للحساب أن يسمح بالترحيل");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من قواعد الترحيل: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة الرصيد الافتتاحي
        /// Validate Opening Balance
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="openingBalance">الرصيد الافتتاحي</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateOpeningBalance(int accountTypeId, decimal openingBalance)
        {
            try
            {
                var accountType = _accountTypeDataAccess.GetAccountTypeById(accountTypeId);
                if (accountType == null)
                {
                    return ServiceResult<bool>.Failure("نوع الحساب غير موجود");
                }

                // قواعد الرصيد حسب نوع الحساب
                switch (accountType.TypeCode.ToUpper())
                {
                    case "ASSETS": // الأصول
                        // الأصول يجب أن تكون موجبة أو صفر
                        if (openingBalance < 0)
                        {
                            return ServiceResult<bool>.Success(false, "رصيد الأصول يجب أن يكون موجباً أو صفراً");
                        }
                        break;

                    case "LIABILITIES": // الخصوم
                        // الخصوم عادة تكون سالبة أو صفر (دائنة)
                        if (openingBalance > 0)
                        {
                            return ServiceResult<bool>.Success(false, "رصيد الخصوم عادة يكون سالباً أو صفراً (دائن)");
                        }
                        break;

                    case "EQUITY": // حقوق الملكية
                        // حقوق الملكية عادة تكون سالبة أو صفر (دائنة)
                        if (openingBalance > 0)
                        {
                            return ServiceResult<bool>.Success(false, "رصيد حقوق الملكية عادة يكون سالباً أو صفراً (دائن)");
                        }
                        break;

                    case "REVENUE": // الإيرادات
                        // الإيرادات عادة تكون سالبة (دائنة)
                        if (openingBalance > 0)
                        {
                            return ServiceResult<bool>.Success(false, "رصيد الإيرادات عادة يكون سالباً (دائن)");
                        }
                        break;

                    case "EXPENSES": // المصروفات
                        // المصروفات عادة تكون موجبة (مدينة)
                        if (openingBalance < 0)
                        {
                            return ServiceResult<bool>.Success(false, "رصيد المصروفات عادة يكون موجباً (مدين)");
                        }
                        break;
                }

                return ServiceResult<bool>.Success(true, "الرصيد الافتتاحي صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من الرصيد الافتتاحي: {ex.Message}");
            }
        }

        #endregion

        #region Currency Rules

        /// <summary>
        /// التحقق من صحة تحويل العملة
        /// Validate Currency Conversion
        /// </summary>
        /// <param name="fromCurrencyCode">كود العملة المصدر</param>
        /// <param name="toCurrencyCode">كود العملة الهدف</param>
        /// <param name="amount">المبلغ</param>
        /// <returns>نتيجة التحويل</returns>
        public ServiceResult<decimal> ValidateCurrencyConversion(string fromCurrencyCode, string toCurrencyCode, decimal amount)
        {
            try
            {
                // التحقق من وجود العملات
                var fromCurrency = _currencyDataAccess.GetCurrencyByCode(fromCurrencyCode);
                var toCurrency = _currencyDataAccess.GetCurrencyByCode(toCurrencyCode);

                if (fromCurrency == null)
                {
                    return ServiceResult<decimal>.Failure($"العملة المصدر غير موجودة: {fromCurrencyCode}");
                }

                if (toCurrency == null)
                {
                    return ServiceResult<decimal>.Failure($"العملة الهدف غير موجودة: {toCurrencyCode}");
                }

                // التحقق من نشاط العملات
                if (!fromCurrency.IsActive)
                {
                    return ServiceResult<decimal>.Failure($"العملة المصدر غير نشطة: {fromCurrencyCode}");
                }

                if (!toCurrency.IsActive)
                {
                    return ServiceResult<decimal>.Failure($"العملة الهدف غير نشطة: {toCurrencyCode}");
                }

                // إذا كانت العملات متشابهة، لا حاجة للتحويل
                if (fromCurrencyCode.Equals(toCurrencyCode, StringComparison.OrdinalIgnoreCase))
                {
                    return ServiceResult<decimal>.Success(amount, "لا حاجة للتحويل - نفس العملة");
                }

                // التحقق من وجود سعر الصرف
                if (fromCurrency.ExchangeRate <= 0 || toCurrency.ExchangeRate <= 0)
                {
                    return ServiceResult<decimal>.Failure("سعر الصرف غير صحيح للعملات المحددة");
                }

                // حساب التحويل
                // التحويل إلى العملة الأساسية أولاً ثم إلى العملة الهدف
                var baseAmount = amount / fromCurrency.ExchangeRate;
                var convertedAmount = baseAmount * toCurrency.ExchangeRate;

                // تقريب النتيجة حسب دقة العملة الهدف
                convertedAmount = Math.Round(convertedAmount, toCurrency.DecimalPlaces);

                return ServiceResult<decimal>.Success(convertedAmount, $"تم التحويل من {fromCurrencyCode} إلى {toCurrencyCode}");
            }
            catch (Exception ex)
            {
                return ServiceResult<decimal>.Failure($"خطأ في تحويل العملة: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة سعر الصرف
        /// Validate Exchange Rate
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <param name="exchangeRate">سعر الصرف</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateExchangeRate(string currencyCode, decimal exchangeRate)
        {
            try
            {
                // التحقق من وجود العملة
                var currency = _currencyDataAccess.GetCurrencyByCode(currencyCode);
                if (currency == null)
                {
                    return ServiceResult<bool>.Failure($"العملة غير موجودة: {currencyCode}");
                }

                // سعر الصرف يجب أن يكون موجباً
                if (exchangeRate <= 0)
                {
                    return ServiceResult<bool>.Success(false, "سعر الصرف يجب أن يكون أكبر من الصفر");
                }

                // التحقق من المعقولية (سعر الصرف لا يجب أن يكون مرتفعاً جداً أو منخفضاً جداً)
                if (exchangeRate > 10000)
                {
                    return ServiceResult<bool>.Success(false, "سعر الصرف مرتفع جداً - يرجى التحقق");
                }

                if (exchangeRate < 0.0001m)
                {
                    return ServiceResult<bool>.Success(false, "سعر الصرف منخفض جداً - يرجى التحقق");
                }

                return ServiceResult<bool>.Success(true, "سعر الصرف صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من سعر الصرف: {ex.Message}");
            }
        }

        #endregion

        #region Hierarchy Rules

        /// <summary>
        /// التحقق من صحة الهيكل الهرمي
        /// Validate Hierarchy Structure
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="newParentId">معرف الحساب الأب الجديد</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateHierarchyStructure(int accountId, int? newParentId)
        {
            try
            {
                // إذا لم يكن هناك حساب أب، فالهيكل صحيح
                if (!newParentId.HasValue)
                {
                    return ServiceResult<bool>.Success(true, "الحساب سيكون في المستوى الأول");
                }

                // التحقق من وجود الحساب والحساب الأب
                var account = _chartOfAccountsDataAccess.GetAccountById(accountId);
                var parentAccount = _chartOfAccountsDataAccess.GetAccountById(newParentId.Value);

                if (account == null)
                {
                    return ServiceResult<bool>.Failure("الحساب غير موجود");
                }

                if (parentAccount == null)
                {
                    return ServiceResult<bool>.Failure("الحساب الأب غير موجود");
                }

                // التحقق من أن الحساب لا يحاول أن يكون أباً لنفسه
                if (accountId == newParentId.Value)
                {
                    return ServiceResult<bool>.Success(false, "الحساب لا يمكن أن يكون أباً لنفسه");
                }

                // التحقق من عدم وجود مراجع دائرية
                if (WouldCreateCircularReference(accountId, newParentId.Value))
                {
                    return ServiceResult<bool>.Success(false, "هذا التغيير سيؤدي إلى مرجع دائري في الهيكل الهرمي");
                }

                // التحقق من أن نوع الحساب متوافق مع نوع الحساب الأب
                if (account.AccountTypeId != parentAccount.AccountTypeId)
                {
                    return ServiceResult<bool>.Success(false, "نوع الحساب يجب أن يتطابق مع نوع الحساب الأب");
                }

                // التحقق من أن مجموعة الحساب متوافقة مع مجموعة الحساب الأب
                if (account.AccountGroupId != parentAccount.AccountGroupId)
                {
                    return ServiceResult<bool>.Success(false, "مجموعة الحساب يجب أن تتطابق مع مجموعة الحساب الأب");
                }

                // التحقق من أن الحساب الأب نشط
                if (!parentAccount.IsActive)
                {
                    return ServiceResult<bool>.Success(false, "الحساب الأب يجب أن يكون نشطاً");
                }

                // التحقق من عمق الهيكل الهرمي (حد أقصى 5 مستويات)
                var depth = GetAccountDepth(newParentId.Value) + 1;
                if (depth > 5)
                {
                    return ServiceResult<bool>.Success(false, "تم الوصول للحد الأقصى لعمق الهيكل الهرمي (5 مستويات)");
                }

                return ServiceResult<bool>.Success(true, "الهيكل الهرمي صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من الهيكل الهرمي: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود مرجع دائري
        /// Check for Circular Reference
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="potentialParentId">معرف الحساب الأب المحتمل</param>
        /// <returns>هل سيؤدي إلى مرجع دائري</returns>
        private bool WouldCreateCircularReference(int accountId, int potentialParentId)
        {
            var visited = new HashSet<int>();
            var currentId = potentialParentId;

            while (currentId != 0)
            {
                if (currentId == accountId)
                {
                    return true; // مرجع دائري
                }

                if (visited.Contains(currentId))
                {
                    return true; // حلقة مفرغة
                }

                visited.Add(currentId);

                var parentAccount = _chartOfAccountsDataAccess.GetAccountById(currentId);
                if (parentAccount?.ParentAccountId == null)
                {
                    break;
                }

                currentId = parentAccount.ParentAccountId.Value;
            }

            return false;
        }

        /// <summary>
        /// الحصول على عمق الحساب في الهيكل الهرمي
        /// Get Account Depth in Hierarchy
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>عمق الحساب</returns>
        private int GetAccountDepth(int accountId)
        {
            var depth = 0;
            var currentId = accountId;

            while (currentId != 0)
            {
                var account = _chartOfAccountsDataAccess.GetAccountById(currentId);
                if (account?.ParentAccountId == null)
                {
                    break;
                }

                depth++;
                currentId = account.ParentAccountId.Value;

                // حماية من الحلقة المفرغة
                if (depth > 10)
                {
                    break;
                }
            }

            return depth;
        }

        #endregion

        #region Account Code Rules

        /// <summary>
        /// التحقق من صحة كود الحساب
        /// Validate Account Code
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="accountGroupId">معرف مجموعة الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccountCode(string accountCode, int accountTypeId, int accountGroupId)
        {
            try
            {
                // التحقق من تنسيق كود الحساب
                if (!IsValidAccountCodeFormat(accountCode))
                {
                    return ServiceResult<bool>.Success(false, "تنسيق كود الحساب غير صحيح. يجب أن يكون بالتنسيق XX.XX.XXX");
                }

                // استخراج أجزاء كود الحساب
                var parts = accountCode.Split('.');
                var typeCode = parts[0];
                var groupCode = parts[1];

                // التحقق من تطابق كود النوع
                var accountType = _accountTypeDataAccess.GetAccountTypeById(accountTypeId);
                if (accountType == null)
                {
                    return ServiceResult<bool>.Failure("نوع الحساب غير موجود");
                }

                var expectedTypeCode = GetTypeCodeFromId(accountTypeId);
                if (typeCode != expectedTypeCode)
                {
                    return ServiceResult<bool>.Success(false, $"كود النوع في كود الحساب ({typeCode}) لا يتطابق مع نوع الحساب المحدد ({expectedTypeCode})");
                }

                // التحقق من تطابق كود المجموعة
                var accountGroup = _accountGroupDataAccess.GetAccountGroupById(accountGroupId);
                if (accountGroup == null)
                {
                    return ServiceResult<bool>.Failure("مجموعة الحساب غير موجودة");
                }

                var expectedGroupCode = GetGroupCodeFromId(accountGroupId);
                if (groupCode != expectedGroupCode)
                {
                    return ServiceResult<bool>.Success(false, $"كود المجموعة في كود الحساب ({groupCode}) لا يتطابق مع مجموعة الحساب المحددة ({expectedGroupCode})");
                }

                return ServiceResult<bool>.Success(true, "كود الحساب صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من كود الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من تنسيق كود الحساب
        /// Check Account Code Format
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <returns>هل التنسيق صحيح</returns>
        private bool IsValidAccountCodeFormat(string accountCode)
        {
            if (string.IsNullOrWhiteSpace(accountCode))
                return false;

            // تنسيق كود الحساب: XX.XX.XXX (رقمين.رقمين.ثلاثة أرقام)
            var pattern = @"^\d{2}\.\d{2}\.\d{3}$";
            return System.Text.RegularExpressions.Regex.IsMatch(accountCode, pattern);
        }

        /// <summary>
        /// الحصول على كود النوع من معرف النوع
        /// Get Type Code from Type ID
        /// </summary>
        /// <param name="typeId">معرف النوع</param>
        /// <returns>كود النوع</returns>
        private string GetTypeCodeFromId(int typeId)
        {
            // هذه دالة مساعدة - يجب تطويرها حسب منطق النظام
            // يمكن استخدام جدول مطابقة أو قواعد محددة
            switch (typeId)
            {
                case 1: return "01"; // الأصول
                case 2: return "02"; // الخصوم
                case 3: return "03"; // حقوق الملكية
                case 4: return "04"; // الإيرادات
                case 5: return "05"; // المصروفات
                case 6: return "06"; // الحسابات النظامية
                default: return "99"; // غير محدد
            }
        }

        /// <summary>
        /// الحصول على كود المجموعة من معرف المجموعة
        /// Get Group Code from Group ID
        /// </summary>
        /// <param name="groupId">معرف المجموعة</param>
        /// <returns>كود المجموعة</returns>
        private string GetGroupCodeFromId(int groupId)
        {
            // هذه دالة مساعدة - يجب تطويرها حسب منطق النظام
            // يمكن استخدام جدول مطابقة أو قواعد محددة
            return groupId.ToString("D2");
        }

        #endregion
    }
}
