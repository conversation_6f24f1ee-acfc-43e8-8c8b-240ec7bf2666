using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج المعلومات الشخصية
    /// Personal Information Model
    /// </summary>
    public class PersonalInformation
    {
        /// <summary>
        /// معرف المعلومات الشخصية
        /// Personal Information ID
        /// </summary>
        public int PersonalInfoId { get; set; }

        /// <summary>
        /// الاسم الكامل بالعربية
        /// Full Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل بالعربية مطلوب")]
        [StringLength(200, ErrorMessage = "الاسم الكامل يجب ألا يزيد عن 200 حرف")]
        public string FullNameAr { get; set; }

        /// <summary>
        /// الاسم الكامل بالإنجليزية
        /// Full Name in English
        /// </summary>
        [StringLength(200, ErrorMessage = "الاسم الكامل يجب ألا يزيد عن 200 حرف")]
        public string FullNameEn { get; set; }

        /// <summary>
        /// رقم الهوية/الإقامة
        /// ID/Residence Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهوية يجب ألا يزيد عن 20 رقم")]
        public string IdNumber { get; set; }

        /// <summary>
        /// رقم الهوية الوطنية
        /// National ID Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهوية الوطنية يجب ألا يزيد عن 20 رقم")]
        public string NationalId { get; set; }

        /// <summary>
        /// الاسم الأول بالعربية
        /// First Name in Arabic
        /// </summary>
        [StringLength(50, ErrorMessage = "الاسم الأول يجب ألا يزيد عن 50 حرف")]
        public string FirstNameAr { get; set; }

        /// <summary>
        /// اسم العائلة بالعربية
        /// Last Name in Arabic
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم العائلة يجب ألا يزيد عن 50 حرف")]
        public string LastNameAr { get; set; }

        /// <summary>
        /// اسم الأب بالعربية
        /// Father Name in Arabic
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم الأب يجب ألا يزيد عن 50 حرف")]
        public string FatherNameAr { get; set; }

        /// <summary>
        /// اسم الجد بالعربية
        /// Grandfather Name in Arabic
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم الجد يجب ألا يزيد عن 50 حرف")]
        public string GrandFatherNameAr { get; set; }

        /// <summary>
        /// رقم جواز السفر
        /// Passport Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم جواز السفر يجب ألا يزيد عن 20 حرف")]
        public string PassportNumber { get; set; }

        /// <summary>
        /// تاريخ انتهاء الهوية
        /// ID Expiry Date
        /// </summary>
        public DateTime? IdExpiryDate { get; set; }

        /// <summary>
        /// نوع الهوية
        /// ID Type
        /// </summary>
        [StringLength(50, ErrorMessage = "نوع الهوية يجب ألا يزيد عن 50 حرف")]
        public string IdType { get; set; }

        /// <summary>
        /// رقم الهاتف الأساسي
        /// Primary Phone Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب ألا يزيد عن 20 رقم")]
        [RegularExpression(@"^[\+]?[0-9\-\(\)\s]+$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// Phone Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب ألا يزيد عن 20 رقم")]
        public string Phone { get; set; }

        /// <summary>
        /// رقم الجوال
        /// Mobile Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الجوال يجب ألا يزيد عن 20 رقم")]
        public string Mobile { get; set; }

        /// <summary>
        /// رقم الهاتف الثانوي
        /// Secondary Phone Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب ألا يزيد عن 20 رقم")]
        [RegularExpression(@"^[\+]?[0-9\-\(\)\s]+$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string SecondaryPhone { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// Email Address
        /// </summary>
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب ألا يزيد عن 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; }

        /// <summary>
        /// العنوان بالعربية
        /// Address in Arabic
        /// </summary>
        [StringLength(500, ErrorMessage = "العنوان يجب ألا يزيد عن 500 حرف")]
        public string AddressAr { get; set; }

        /// <summary>
        /// العنوان بالإنجليزية
        /// Address in English
        /// </summary>
        [StringLength(500, ErrorMessage = "العنوان يجب ألا يزيد عن 500 حرف")]
        public string AddressEn { get; set; }

        /// <summary>
        /// العنوان المبسط
        /// Simple Address
        /// </summary>
        [StringLength(500, ErrorMessage = "العنوان يجب ألا يزيد عن 500 حرف")]
        public string Address { get; set; }

        /// <summary>
        /// المدينة
        /// City
        /// </summary>
        [StringLength(100, ErrorMessage = "المدينة يجب ألا تزيد عن 100 حرف")]
        public string City { get; set; }

        /// <summary>
        /// المنطقة/الولاية
        /// State/Province
        /// </summary>
        [StringLength(100, ErrorMessage = "المنطقة يجب ألا تزيد عن 100 حرف")]
        public string State { get; set; }

        /// <summary>
        /// الرمز البريدي
        /// Postal Code
        /// </summary>
        [StringLength(20, ErrorMessage = "الرمز البريدي يجب ألا يزيد عن 20 حرف")]
        public string PostalCode { get; set; }

        /// <summary>
        /// الدولة
        /// Country
        /// </summary>
        [StringLength(100, ErrorMessage = "الدولة يجب ألا تزيد عن 100 حرف")]
        public string Country { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// Date of Birth
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// الجنسية
        /// Nationality
        /// </summary>
        [StringLength(100, ErrorMessage = "الجنسية يجب ألا تزيد عن 100 حرف")]
        public string Nationality { get; set; }

        /// <summary>
        /// الجنس
        /// Gender
        /// </summary>
        [StringLength(10, ErrorMessage = "الجنس يجب ألا يزيد عن 10 أحرف")]
        public string Gender { get; set; }

        /// <summary>
        /// الحالة الاجتماعية
        /// Marital Status
        /// </summary>
        [StringLength(20, ErrorMessage = "الحالة الاجتماعية يجب ألا تزيد عن 20 حرف")]
        public string MaritalStatus { get; set; }

        /// <summary>
        /// المهنة
        /// Profession
        /// </summary>
        [StringLength(100, ErrorMessage = "المهنة يجب ألا تزيد عن 100 حرف")]
        public string Profession { get; set; }

        /// <summary>
        /// جهة العمل
        /// Employer
        /// </summary>
        [StringLength(200, ErrorMessage = "جهة العمل يجب ألا تزيد عن 200 حرف")]
        public string Employer { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// Additional Notes
        /// </summary>
        [StringLength(1000, ErrorMessage = "الملاحظات يجب ألا تزيد عن 1000 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// منشئ السجل
        /// Created By
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل السجل
        /// Modified By
        /// </summary>
        [StringLength(100)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// الحصول على الاسم الكامل حسب اللغة
        /// Get full name based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>الاسم الكامل</returns>
        public string GetDisplayName(bool isArabic = true)
        {
            if (isArabic)
                return !string.IsNullOrWhiteSpace(FullNameAr) ? FullNameAr : FullNameEn;
            else
                return !string.IsNullOrWhiteSpace(FullNameEn) ? FullNameEn : FullNameAr;
        }

        /// <summary>
        /// الحصول على العنوان حسب اللغة
        /// Get address based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>العنوان</returns>
        public string GetDisplayAddress(bool isArabic = true)
        {
            if (isArabic)
                return !string.IsNullOrWhiteSpace(AddressAr) ? AddressAr : AddressEn;
            else
                return !string.IsNullOrWhiteSpace(AddressEn) ? AddressEn : AddressAr;
        }

        /// <summary>
        /// الحصول على العنوان الكامل
        /// Get full address
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>العنوان الكامل</returns>
        public string GetFullAddress(bool isArabic = true)
        {
            var address = GetDisplayAddress(isArabic);
            var parts = new List<string>();

            if (!string.IsNullOrWhiteSpace(address))
                parts.Add(address);

            if (!string.IsNullOrWhiteSpace(City))
                parts.Add(City);

            if (!string.IsNullOrWhiteSpace(State))
                parts.Add(State);

            if (!string.IsNullOrWhiteSpace(PostalCode))
                parts.Add(PostalCode);

            if (!string.IsNullOrWhiteSpace(Country))
                parts.Add(Country);

            return string.Join(", ", parts);
        }

        /// <summary>
        /// حساب العمر
        /// Calculate age
        /// </summary>
        /// <returns>العمر بالسنوات</returns>
        public int? GetAge()
        {
            if (!DateOfBirth.HasValue)
                return null;

            var today = DateTime.Today;
            var age = today.Year - DateOfBirth.Value.Year;

            if (DateOfBirth.Value.Date > today.AddYears(-age))
                age--;

            return age;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (string.IsNullOrWhiteSpace(FullNameAr))
                return "الاسم الكامل بالعربية مطلوب";

            if (!string.IsNullOrWhiteSpace(Email))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(Email);
                    if (addr.Address != Email)
                        return "البريد الإلكتروني غير صحيح";
                }
                catch
                {
                    return "البريد الإلكتروني غير صحيح";
                }
            }

            if (DateOfBirth.HasValue && DateOfBirth.Value > DateTime.Today)
                return "تاريخ الميلاد لا يمكن أن يكون في المستقبل";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// نسخ البيانات من نموذج آخر
        /// Copy data from another model
        /// </summary>
        /// <param name="source">النموذج المصدر</param>
        public void CopyFrom(PersonalInformation source)
        {
            if (source == null) return;

            FullNameAr = source.FullNameAr;
            FullNameEn = source.FullNameEn;
            IdNumber = source.IdNumber;
            IdType = source.IdType;
            PhoneNumber = source.PhoneNumber;
            SecondaryPhone = source.SecondaryPhone;
            Email = source.Email;
            AddressAr = source.AddressAr;
            AddressEn = source.AddressEn;
            City = source.City;
            State = source.State;
            PostalCode = source.PostalCode;
            Country = source.Country;
            DateOfBirth = source.DateOfBirth;
            Nationality = source.Nationality;
            Gender = source.Gender;
            MaritalStatus = source.MaritalStatus;
            Profession = source.Profession;
            Employer = source.Employer;
            Notes = source.Notes;
            IsActive = source.IsActive;
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return GetDisplayName(true);
        }
    }
}
