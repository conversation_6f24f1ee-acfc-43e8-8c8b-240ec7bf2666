using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Awqaf_Managment.UI.Forms.Accounting;
using Awqaf_Managment.Services.Accounting;

namespace Awqaf_Managment.Tests
{
    /// <summary>
    /// اختبار نظام إدارة دليل الحسابات
    /// Chart of Accounts Management System Test
    /// </summary>
    public static class ChartOfAccountsTest
    {
        /// <summary>
        /// تشغيل اختبار شامل للنظام
        /// Run Comprehensive System Test
        /// </summary>
        public static async Task RunComprehensiveTestAsync()
        {
            try
            {
                Console.WriteLine("🧪 بدء اختبار نظام إدارة دليل الحسابات...");
                Console.WriteLine("=" * 50);

                // اختبار الاتصال بقاعدة البيانات
                await TestDatabaseConnectionAsync();

                // اختبار طبقة الخدمات
                await TestServiceLayerAsync();

                // اختبار واجهة المستخدم
                TestUserInterface();

                Console.WriteLine("=" * 50);
                Console.WriteLine("✅ تم اكتمال جميع الاختبارات بنجاح!");
                Console.WriteLine("🎉 النظام جاهز للاستخدام!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في الاختبار: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// Test Database Connection
        /// </summary>
        private static async Task TestDatabaseConnectionAsync()
        {
            try
            {
                Console.WriteLine("🔗 اختبار الاتصال بقاعدة البيانات...");
                
                var service = new ChartOfAccountsService();
                
                // اختبار تحميل أنواع الحسابات
                var accountTypes = await service.GetAllAccountTypesAsync();
                Console.WriteLine($"   ✓ تم تحميل {accountTypes.Count} نوع حساب");

                // اختبار تحميل الحسابات
                var accounts = await service.GetHierarchicalAccountsAsync();
                Console.WriteLine($"   ✓ تم تحميل {accounts.Count} حساب");

                Console.WriteLine("✅ اختبار قاعدة البيانات مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل اختبار قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار طبقة الخدمات
        /// Test Service Layer
        /// </summary>
        private static async Task TestServiceLayerAsync()
        {
            try
            {
                Console.WriteLine("⚙️ اختبار طبقة الخدمات...");
                
                var service = new ChartOfAccountsService();

                // اختبار توليد رمز الحساب
                var newCode = await service.GenerateNextAccountCodeAsync(1, null);
                Console.WriteLine($"   ✓ تم توليد رمز حساب جديد: {newCode}");

                // اختبار تحميل مجموعات الحسابات
                var accountGroups = await service.GetAccountGroupsByTypeAsync(1);
                Console.WriteLine($"   ✓ تم تحميل {accountGroups.Count} مجموعة حساب");

                Console.WriteLine("✅ اختبار طبقة الخدمات مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل اختبار طبقة الخدمات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار واجهة المستخدم
        /// Test User Interface
        /// </summary>
        private static void TestUserInterface()
        {
            try
            {
                Console.WriteLine("🖥️ اختبار واجهة المستخدم...");

                // إنشاء النموذج
                var form = new ChartOfAccountsManagementForm();
                Console.WriteLine("   ✓ تم إنشاء نموذج إدارة دليل الحسابات");

                // التحقق من العناصر الأساسية
                if (form.Controls.Count > 0)
                    Console.WriteLine("   ✓ تم تحميل عناصر التحكم");

                Console.WriteLine("✅ اختبار واجهة المستخدم مكتمل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل اختبار واجهة المستخدم: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// عرض النموذج للاختبار اليدوي
        /// Show Form for Manual Testing
        /// </summary>
        public static void ShowFormForManualTesting()
        {
            try
            {
                Console.WriteLine("🖱️ فتح النموذج للاختبار اليدوي...");
                
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var form = new ChartOfAccountsManagementForm();
                Application.Run(form);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في فتح النموذج: {ex.Message}");
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع للنظام
        /// Quick System Test
        /// </summary>
        public static async Task<bool> QuickTestAsync()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع للنظام...");

                var service = new ChartOfAccountsService();
                
                // اختبار أساسي
                var accountTypes = await service.GetAllAccountTypesAsync();
                var accounts = await service.GetHierarchicalAccountsAsync();

                bool isWorking = accountTypes.Count > 0 && accounts.Count > 0;
                
                if (isWorking)
                {
                    Console.WriteLine("✅ الاختبار السريع نجح - النظام يعمل بشكل صحيح");
                }
                else
                {
                    Console.WriteLine("❌ الاختبار السريع فشل - يوجد مشكلة في النظام");
                }

                return isWorking;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل الاختبار السريع: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// طباعة معلومات النظام
        /// Print System Information
        /// </summary>
        public static void PrintSystemInfo()
        {
            Console.WriteLine("📋 معلومات النظام:");
            Console.WriteLine("=" * 30);
            Console.WriteLine($"📅 تاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"🏗️ المطور: نظام إدارة الأوقاف");
            Console.WriteLine($"📦 الوحدة: إدارة دليل الحسابات");
            Console.WriteLine($"🔢 الإصدار: 1.0.0");
            Console.WriteLine($"🎯 الحالة: جاهز للاستخدام");
            Console.WriteLine("=" * 30);
        }
    }

    /// <summary>
    /// نقطة دخول الاختبار
    /// Test Entry Point
    /// </summary>
    public class Program
    {
        [STAThread]
        public static async Task Main(string[] args)
        {
            try
            {
                // طباعة معلومات النظام
                ChartOfAccountsTest.PrintSystemInfo();

                // تشغيل الاختبار الشامل
                await ChartOfAccountsTest.RunComprehensiveTestAsync();

                // اختبار سريع إضافي
                await ChartOfAccountsTest.QuickTestAsync();

                Console.WriteLine("\n🚀 هل تريد فتح النموذج للاختبار اليدوي؟ (y/n)");
                var response = Console.ReadLine();
                
                if (response?.ToLower() == "y" || response?.ToLower() == "yes")
                {
                    ChartOfAccountsTest.ShowFormForManualTesting();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 خطأ عام في النظام: {ex.Message}");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }
    }
}
