using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.DataAccess.Base;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بسجل التعديلات
    /// Audit Log Data Access Layer
    /// </summary>
    public class AuditLogDataAccess : BaseDataAccess
    {
        #region Audit Log Operations

        /// <summary>
        /// إضافة سجل تعديل جديد
        /// Add New Audit Log Entry
        /// </summary>
        /// <param name="auditLog">بيانات سجل التعديل</param>
        /// <returns>معرف سجل التعديل المضاف</returns>
        public int AddAuditLog(AccountAuditLog auditLog)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountId", auditLog.AccountId),
                    CreateParameter("@Operation", auditLog.Operation, SqlDbType.NVarChar, 50),
                    CreateParameter("@FieldName", ConvertToDb(auditLog.FieldName), SqlDbType.NVarChar, 100),
                    CreateParameter("@OldValue", ConvertToDb(auditLog.OldValue), SqlDbType.NVarChar, 500),
                    CreateParameter("@NewValue", ConvertToDb(auditLog.NewValue), SqlDbType.NVarChar, 500),
                    CreateParameter("@ChangeReason", ConvertToDb(auditLog.ChangeReason), SqlDbType.NVarChar, 500),
                    CreateParameter("@UserId", auditLog.UserId, SqlDbType.NVarChar, 100),
                    CreateParameter("@UserName", auditLog.UserName, SqlDbType.NVarChar, 200),
                    CreateParameter("@IPAddress", ConvertToDb(auditLog.IPAddress), SqlDbType.NVarChar, 50),
                    CreateParameter("@UserAgent", ConvertToDb(auditLog.UserAgent), SqlDbType.NVarChar, 500),
                    CreateOutputParameter("@NewAuditLogId", SqlDbType.Int)
                };

                ExecuteStoredProcedure("SP_AddAuditLog", parameters);
                
                return ConvertFromDb<int>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "إضافة سجل التعديل");
                throw new Exception(HandleException(ex, "إضافة سجل التعديل"));
            }
        }

        /// <summary>
        /// الحصول على سجلات التعديل للحساب
        /// Get Audit Logs for Account
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <returns>قائمة سجلات التعديل</returns>
        public List<AccountAuditLog> GetAuditLogsByAccount(int accountId, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountId", accountId),
                    CreateParameter("@PageNumber", pageNumber),
                    CreateParameter("@PageSize", pageSize)
                };

                var auditLogs = new List<AccountAuditLog>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAuditLogsByAccount", parameters))
                {
                    while (reader.Read())
                    {
                        auditLogs.Add(MapAuditLogFromReader(reader));
                    }
                }

                return auditLogs;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على سجلات التعديل للحساب");
                throw new Exception(HandleException(ex, "الحصول على سجلات التعديل للحساب"));
            }
        }

        /// <summary>
        /// الحصول على سجلات التعديل للمستخدم
        /// Get Audit Logs for User
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <returns>قائمة سجلات التعديل</returns>
        public List<AccountAuditLog> GetAuditLogsByUser(string userId, DateTime? fromDate = null, DateTime? toDate = null, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@UserId", userId, SqlDbType.NVarChar, 100),
                    CreateParameter("@FromDate", ConvertToDb(fromDate)),
                    CreateParameter("@ToDate", ConvertToDb(toDate)),
                    CreateParameter("@PageNumber", pageNumber),
                    CreateParameter("@PageSize", pageSize)
                };

                var auditLogs = new List<AccountAuditLog>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAuditLogsByUser", parameters))
                {
                    while (reader.Read())
                    {
                        auditLogs.Add(MapAuditLogFromReader(reader));
                    }
                }

                return auditLogs;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على سجلات التعديل للمستخدم");
                throw new Exception(HandleException(ex, "الحصول على سجلات التعديل للمستخدم"));
            }
        }

        /// <summary>
        /// البحث في سجلات التعديل
        /// Search Audit Logs
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <param name="operation">نوع العملية</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <returns>قائمة سجلات التعديل</returns>
        public List<AccountAuditLog> SearchAuditLogs(string searchText = null, string operation = null, DateTime? fromDate = null, DateTime? toDate = null, string userId = null, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@SearchText", ConvertToDb(searchText), SqlDbType.NVarChar, 200),
                    CreateParameter("@Operation", ConvertToDb(operation), SqlDbType.NVarChar, 50),
                    CreateParameter("@FromDate", ConvertToDb(fromDate)),
                    CreateParameter("@ToDate", ConvertToDb(toDate)),
                    CreateParameter("@UserId", ConvertToDb(userId), SqlDbType.NVarChar, 100),
                    CreateParameter("@PageNumber", pageNumber),
                    CreateParameter("@PageSize", pageSize)
                };

                var auditLogs = new List<AccountAuditLog>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_SearchAuditLogs", parameters))
                {
                    while (reader.Read())
                    {
                        auditLogs.Add(MapAuditLogFromReader(reader));
                    }
                }

                return auditLogs;
            }
            catch (Exception ex)
            {
                LogError(ex, "البحث في سجلات التعديل");
                throw new Exception(HandleException(ex, "البحث في سجلات التعديل"));
            }
        }

        /// <summary>
        /// الحصول على إحصائيات سجلات التعديل
        /// Get Audit Log Statistics
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>إحصائيات سجلات التعديل</returns>
        public AuditLogStatistics GetAuditLogStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@FromDate", ConvertToDb(fromDate)),
                    CreateParameter("@ToDate", ConvertToDb(toDate))
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAuditLogStatistics", parameters))
                {
                    if (reader.Read())
                    {
                        return new AuditLogStatistics
                        {
                            TotalOperations = ConvertFromDb<int>(reader["TotalOperations"]),
                            InsertOperations = ConvertFromDb<int>(reader["InsertOperations"]),
                            UpdateOperations = ConvertFromDb<int>(reader["UpdateOperations"]),
                            DeleteOperations = ConvertFromDb<int>(reader["DeleteOperations"]),
                            UniqueUsers = ConvertFromDb<int>(reader["UniqueUsers"]),
                            UniqueAccounts = ConvertFromDb<int>(reader["UniqueAccounts"]),
                            FromDate = ConvertFromDb<DateTime?>(fromDate),
                            ToDate = ConvertFromDb<DateTime?>(toDate)
                        };
                    }
                }

                return new AuditLogStatistics();
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على إحصائيات سجلات التعديل");
                throw new Exception(HandleException(ex, "الحصول على إحصائيات سجلات التعديل"));
            }
        }

        /// <summary>
        /// حذف سجلات التعديل القديمة
        /// Delete Old Audit Logs
        /// </summary>
        /// <param name="olderThanDays">أقدم من عدد الأيام</param>
        /// <returns>عدد السجلات المحذوفة</returns>
        public int DeleteOldAuditLogs(int olderThanDays)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@OlderThanDays", olderThanDays),
                    CreateOutputParameter("@DeletedCount", SqlDbType.Int)
                };

                ExecuteStoredProcedure("SP_DeleteOldAuditLogs", parameters);
                
                return ConvertFromDb<int>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "حذف سجلات التعديل القديمة");
                throw new Exception(HandleException(ex, "حذف سجلات التعديل القديمة"));
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن سجل التعديل
        /// Map Data Reader to Audit Log Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن سجل التعديل</returns>
        private AccountAuditLog MapAuditLogFromReader(SqlDataReader reader)
        {
            return new AccountAuditLog
            {
                AuditLogId = ConvertFromDb<int>(reader["AuditLogId"]),
                AccountId = ConvertFromDb<int>(reader["AccountId"]),
                Operation = ConvertFromDb<string>(reader["Operation"]),
                FieldName = ConvertFromDb<string>(reader["FieldName"]),
                OldValue = ConvertFromDb<string>(reader["OldValue"]),
                NewValue = ConvertFromDb<string>(reader["NewValue"]),
                ChangeReason = ConvertFromDb<string>(reader["ChangeReason"]),
                ChangeDate = ConvertFromDb<DateTime>(reader["ChangeDate"]),
                UserId = ConvertFromDb<string>(reader["UserId"]),
                UserName = ConvertFromDb<string>(reader["UserName"]),
                IPAddress = ConvertFromDb<string>(reader["IPAddress"]),
                UserAgent = ConvertFromDb<string>(reader["UserAgent"])
            };
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات سجلات التعديل
    /// Audit Log Statistics
    /// </summary>
    public class AuditLogStatistics
    {
        public int TotalOperations { get; set; }
        public int InsertOperations { get; set; }
        public int UpdateOperations { get; set; }
        public int DeleteOperations { get; set; }
        public int UniqueUsers { get; set; }
        public int UniqueAccounts { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
}
