using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.DataAccess.Base;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بأنواع الحسابات
    /// Account Types Data Access Layer
    /// </summary>
    public class AccountTypeDataAccess : BaseDataAccess
    {
        #region Account Type CRUD Operations

        /// <summary>
        /// الحصول على جميع أنواع الحسابات
        /// Get All Account Types
        /// </summary>
        /// <returns>قائمة أنواع الحسابات</returns>
        public List<AccountType> GetAllAccountTypes()
        {
            try
            {
                var accountTypes = new List<AccountType>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAllAccountTypes"))
                {
                    while (reader.Read())
                    {
                        accountTypes.Add(MapAccountTypeFromReader(reader));
                    }
                }

                return accountTypes;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على أنواع الحسابات");
                throw new Exception(HandleException(ex, "الحصول على أنواع الحسابات"));
            }
        }

        /// <summary>
        /// الحصول على نوع حساب بالمعرف
        /// Get Account Type by ID
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <returns>بيانات نوع الحساب</returns>
        public AccountType GetAccountTypeById(int accountTypeId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", accountTypeId)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountTypeById", parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountTypeFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على نوع الحساب");
                throw new Exception(HandleException(ex, "الحصول على نوع الحساب"));
            }
        }

        /// <summary>
        /// الحصول على نوع حساب بالكود
        /// Get Account Type by Code
        /// </summary>
        /// <param name="typeCode">كود نوع الحساب</param>
        /// <returns>بيانات نوع الحساب</returns>
        public AccountType GetAccountTypeByCode(string typeCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@TypeCode", typeCode, SqlDbType.NVarChar, 10)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountTypeByCode", parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountTypeFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على نوع الحساب بالكود");
                throw new Exception(HandleException(ex, "الحصول على نوع الحساب بالكود"));
            }
        }

        /// <summary>
        /// حفظ نوع حساب جديد أو تحديث موجود
        /// Save New Account Type or Update Existing
        /// </summary>
        /// <param name="accountType">بيانات نوع الحساب</param>
        /// <returns>معرف نوع الحساب المحفوظ</returns>
        public int SaveAccountType(AccountType accountType)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", accountType.AccountTypeId),
                    CreateParameter("@TypeCode", accountType.TypeCode, SqlDbType.NVarChar, 10),
                    CreateParameter("@TypeName", accountType.TypeName, SqlDbType.NVarChar, 100),
                    CreateParameter("@TypeNameAr", accountType.TypeNameAr, SqlDbType.NVarChar, 100),
                    CreateParameter("@NormalBalance", accountType.NormalBalance, SqlDbType.NVarChar, 10),
                    CreateParameter("@Description", ConvertToDb(accountType.Description), SqlDbType.NVarChar, 500),
                    CreateParameter("@IsActive", accountType.IsActive),
                    CreateParameter("@CreatedBy", accountType.CreatedBy, SqlDbType.NVarChar, 100),
                    CreateOutputParameter("@NewAccountTypeId", SqlDbType.Int)
                };

                ExecuteStoredProcedure("SP_SaveAccountType", parameters);
                
                return ConvertFromDb<int>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "حفظ نوع الحساب");
                throw new Exception(HandleException(ex, "حفظ نوع الحساب"));
            }
        }

        /// <summary>
        /// حذف نوع حساب
        /// Delete Account Type
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <returns>هل تم الحذف بنجاح</returns>
        public bool DeleteAccountType(int accountTypeId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", accountTypeId)
                };

                int result = ExecuteStoredProcedure("SP_DeleteAccountType", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "حذف نوع الحساب");
                throw new Exception(HandleException(ex, "حذف نوع الحساب"));
            }
        }

        #endregion

        #region Account Type Validation

        /// <summary>
        /// التحقق من وجود كود نوع الحساب
        /// Check if Account Type Code Exists
        /// </summary>
        /// <param name="typeCode">كود نوع الحساب</param>
        /// <param name="excludeId">معرف نوع الحساب المستثنى من التحقق</param>
        /// <returns>هل الكود موجود</returns>
        public bool IsAccountTypeCodeExists(string typeCode, int? excludeId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@TypeCode", typeCode, SqlDbType.NVarChar, 10),
                    CreateParameter("@ExcludeId", ConvertToDb(excludeId))
                };

                var result = ExecuteStoredProcedureScalar("SP_CheckAccountTypeCodeExists", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من كود نوع الحساب");
                throw new Exception(HandleException(ex, "التحقق من كود نوع الحساب"));
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف نوع الحساب
        /// Check if Account Type Can Be Deleted
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <returns>هل يمكن الحذف</returns>
        public bool CanDeleteAccountType(int accountTypeId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", accountTypeId)
                };

                var result = ExecuteStoredProcedureScalar("SP_CanDeleteAccountType", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من إمكانية حذف نوع الحساب");
                throw new Exception(HandleException(ex, "التحقق من إمكانية حذف نوع الحساب"));
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن نوع الحساب
        /// Map Data Reader to Account Type Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن نوع الحساب</returns>
        private AccountType MapAccountTypeFromReader(SqlDataReader reader)
        {
            return new AccountType
            {
                AccountTypeId = ConvertFromDb<int>(reader["AccountTypeId"]),
                TypeCode = ConvertFromDb<string>(reader["TypeCode"]),
                TypeName = ConvertFromDb<string>(reader["TypeName"]),
                TypeNameAr = ConvertFromDb<string>(reader["TypeNameAr"]),
                NormalBalance = ConvertFromDb<string>(reader["NormalBalance"]),
                Description = ConvertFromDb<string>(reader["Description"]),
                IsActive = ConvertFromDb<bool>(reader["IsActive"]),
                CreatedDate = ConvertFromDb<DateTime>(reader["CreatedDate"]),
                CreatedBy = ConvertFromDb<string>(reader["CreatedBy"]),
                ModifiedDate = ConvertFromDb<DateTime?>(reader["ModifiedDate"]),
                ModifiedBy = ConvertFromDb<string>(reader["ModifiedBy"])
            };
        }

        #endregion
    }
}
