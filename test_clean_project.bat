@echo off
echo ========================================
echo اختبار المشروع النظيف
echo Testing Clean Project
echo ========================================

cd /d "%~dp0"

echo.
echo تجميع واختبار المشروع بدون النظام المحاسبي...
echo Compiling and testing project without accounting system...

REM تنظيف المشروع
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if not exist "bin\Debug" mkdir "bin\Debug"

echo.
echo تجميع الملفات الأساسية...
echo Compiling core files...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Data.dll ^
    /reference:System.ComponentModel.DataAnnotations.dll ^
    /reference:System.Data.SqlClient.dll ^
    /out:bin\Debug\Awqaf_Managment_Clean.exe ^
    "Program.cs" ^
    "UI\Forms\MainForm.cs" ^
    "UI\Forms\MainForm.Designer.cs" ^
    "UI\Forms\DashboardForm.cs" ^
    "UI\Forms\DashboardForm.Designer.cs" ^
    "UI\Forms\Security\LoginForm.cs" ^
    "UI\Forms\Security\LoginForm.Designer.cs" ^
    "UI\Forms\Security\UserManagementForm.cs" ^
    "UI\Forms\Security\UserManagementForm.Designer.cs" ^
    "UI\Forms\Accounting\CurrencyManagementForm.cs" ^
    "UI\Forms\Accounting\CurrencyManagementForm.Designer.cs" ^
    "UI\Helpers\ComboBoxItem.cs" ^
    "UI\Helpers\UIHelper.cs" ^
    "Common\Constants.cs" ^
    "Common\Helpers\UIHelper.cs" ^
    "Models\Security\User.cs" ^
    "Models\Accounting\Currency.cs" ^
    "Models\Accounting\CostCenter.cs" ^
    "DataAccess\DatabaseConnection.cs" ^
    "DataAccess\Security\UserDataAccess.cs" ^
    "DataAccess\Security\RoleDataAccess.cs" ^
    "DataAccess\Base\BaseDataAccess.cs" ^
    "DataAccess\Accounting\CurrencyDataAccess.cs" ^
    "Services\Security\AuthenticationService.cs" ^
    "Services\Accounting\CostCenterService.cs" ^
    "TestDatabaseConnection.cs" ^
    "Properties\AssemblyInfo.cs"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم التجميع بنجاح!
    echo ✅ Compilation successful!
    echo.
    echo النماذج المتاحة في المشروع النظيف:
    echo Available forms in clean project:
    echo.
    echo 🏠 MainForm - النموذج الرئيسي
    echo   └── القائمة الرئيسية للتطبيق
    echo.
    echo 📊 DashboardForm - لوحة التحكم  
    echo   └── عرض الإحصائيات والمعلومات العامة
    echo.
    echo 👤 Security Forms - نماذج الأمان
    echo   ├── LoginForm - تسجيل الدخول
    echo   └── UserManagementForm - إدارة المستخدمين
    echo.
    echo 💰 CurrencyManagementForm - إدارة العملات
    echo   └── إدارة العملات المتاحة في النظام
    echo.
    echo تشغيل التطبيق النظيف...
    echo Running clean application...
    start "" "bin\Debug\Awqaf_Managment_Clean.exe"
) else (
    echo.
    echo ❌ فشل في التجميع
    echo ❌ Compilation failed
    echo.
    echo الأخطاء المحتملة:
    echo Possible errors:
    echo 1. ملفات مفقودة
    echo 1. Missing files
    echo 2. مراجع خاطئة
    echo 2. Wrong references
    echo 3. أخطاء في الكود
    echo 3. Code errors
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
