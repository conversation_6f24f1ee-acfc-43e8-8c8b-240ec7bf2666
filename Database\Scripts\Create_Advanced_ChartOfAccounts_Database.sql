-- ========================================
-- سكريبت إنشاء قاعدة البيانات المتطورة للدليل المحاسبي
-- Advanced Chart of Accounts Database Creation Script
-- ========================================
-- قاعدة البيانات: AwqafManagement
-- الخادم: NAJEEB
-- ========================================

USE AwqafManagement;
GO

PRINT '========================================';
PRINT 'بدء إنشاء قاعدة البيانات المتطورة للدليل المحاسبي';
PRINT 'Starting Advanced Chart of Accounts Database Creation';
PRINT '========================================';

-- ========================================
-- 1. إنشاء الجداول المرجعية الأساسية
-- ========================================

-- جدول أنواع الحسابات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountTypes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountTypes] (
        [AccountTypeId] INT IDENTITY(1,1) PRIMARY KEY,
        [TypeCode] NVARCHAR(10) NOT NULL UNIQUE,
        [TypeName] NVARCHAR(100) NOT NULL,
        [TypeNameAr] NVARCHAR(100) NOT NULL,
        [TypeDescription] NVARCHAR(500),
        [TypeDescriptionAr] NVARCHAR(500),
        [NormalBalance] NVARCHAR(10) NOT NULL CHECK (NormalBalance IN ('Debit', 'Credit')), -- طبيعة الرصيد
        [IsActive] BIT NOT NULL DEFAULT 1,
        [DisplayOrder] INT NOT NULL DEFAULT 0,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [ModifiedDate] DATETIME2,
        [ModifiedBy] NVARCHAR(100)
    );
    
    PRINT '✅ تم إنشاء جدول أنواع الحسابات (AccountTypes)';
END

-- جدول مجموعات الحسابات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountGroups]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountGroups] (
        [AccountGroupId] INT IDENTITY(1,1) PRIMARY KEY,
        [GroupCode] NVARCHAR(10) NOT NULL UNIQUE,
        [GroupName] NVARCHAR(100) NOT NULL,
        [GroupNameAr] NVARCHAR(100) NOT NULL,
        [GroupDescription] NVARCHAR(500),
        [GroupDescriptionAr] NVARCHAR(500),
        [AccountTypeId] INT NOT NULL,
        [ParentGroupId] INT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [DisplayOrder] INT NOT NULL DEFAULT 0,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [ModifiedDate] DATETIME2,
        [ModifiedBy] NVARCHAR(100),
        
        CONSTRAINT FK_AccountGroups_AccountTypes FOREIGN KEY (AccountTypeId) 
            REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_AccountGroups_ParentGroup FOREIGN KEY (ParentGroupId) 
            REFERENCES AccountGroups(AccountGroupId)
    );
    
    PRINT '✅ تم إنشاء جدول مجموعات الحسابات (AccountGroups)';
END

-- جدول العملات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Currencies] (
        [CurrencyId] INT IDENTITY(1,1) PRIMARY KEY,
        [CurrencyCode] NVARCHAR(3) NOT NULL UNIQUE,
        [CurrencyName] NVARCHAR(100) NOT NULL,
        [CurrencyNameAr] NVARCHAR(100) NOT NULL,
        [CurrencySymbol] NVARCHAR(10) NOT NULL,
        [ExchangeRate] DECIMAL(18,6) NOT NULL DEFAULT 1.0,
        [IsBaseCurrency] BIT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [DecimalPlaces] INT NOT NULL DEFAULT 2,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [ModifiedDate] DATETIME2,
        [ModifiedBy] NVARCHAR(100)
    );
    
    PRINT '✅ تم إنشاء جدول العملات (Currencies)';
END

-- جدول البيانات الشخصية
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PersonalInformation]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PersonalInformation] (
        [PersonalInfoId] INT IDENTITY(1,1) PRIMARY KEY,
        [FullName] NVARCHAR(200) NOT NULL,
        [FullNameAr] NVARCHAR(200) NOT NULL,
        [Email] NVARCHAR(100),
        [Phone] NVARCHAR(20),
        [Mobile] NVARCHAR(20),
        [Address] NVARCHAR(500),
        [AddressAr] NVARCHAR(500),
        [City] NVARCHAR(100),
        [CityAr] NVARCHAR(100),
        [Country] NVARCHAR(100),
        [CountryAr] NVARCHAR(100),
        [PostalCode] NVARCHAR(20),
        [IdentityNumber] NVARCHAR(50),
        [IdentityType] NVARCHAR(50), -- نوع الهوية (بطاقة شخصية، جواز سفر، إلخ)
        [TaxNumber] NVARCHAR(50),
        [CompanyName] NVARCHAR(200),
        [CompanyNameAr] NVARCHAR(200),
        [JobTitle] NVARCHAR(100),
        [JobTitleAr] NVARCHAR(100),
        [DateOfBirth] DATE,
        [Gender] NVARCHAR(10) CHECK (Gender IN ('Male', 'Female')),
        [Nationality] NVARCHAR(100),
        [NationalityAr] NVARCHAR(100),
        [Notes] NVARCHAR(1000),
        [NotesAr] NVARCHAR(1000),
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [ModifiedDate] DATETIME2,
        [ModifiedBy] NVARCHAR(100)
    );
    
    PRINT '✅ تم إنشاء جدول البيانات الشخصية (PersonalInformation)';
END

-- ========================================
-- 2. الجدول الرئيسي - دليل الحسابات
-- ========================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ChartOfAccounts] (
        [AccountId] INT IDENTITY(1,1) PRIMARY KEY,
        [AccountCode] NVARCHAR(20) NOT NULL UNIQUE,
        [AccountName] NVARCHAR(200) NOT NULL,
        [AccountNameAr] NVARCHAR(200) NOT NULL,
        [AccountTypeId] INT NOT NULL,
        [AccountGroupId] INT,
        [ParentAccountId] INT NULL,
        [PersonalInfoId] INT NULL,
        [CurrencyId] INT NOT NULL,
        [AccountLevel] INT NOT NULL DEFAULT 1,
        [IsParent] BIT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [AllowPosting] BIT NOT NULL DEFAULT 1,
        [Description] NVARCHAR(1000),
        [DescriptionAr] NVARCHAR(1000),
        [OpeningBalance] DECIMAL(18,4) NOT NULL DEFAULT 0,
        [OpeningBalanceDate] DATE,
        [CurrentBalance] DECIMAL(18,4) NOT NULL DEFAULT 0,
        [DebitBalance] DECIMAL(18,4) NOT NULL DEFAULT 0,
        [CreditBalance] DECIMAL(18,4) NOT NULL DEFAULT 0,
        [LastTransactionDate] DATETIME2,
        [IsSystemAccount] BIT NOT NULL DEFAULT 0, -- حساب نظام لا يمكن حذفه
        [AccountPath] NVARCHAR(500), -- مسار الحساب الهرمي
        [SortOrder] INT NOT NULL DEFAULT 0,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [ModifiedDate] DATETIME2,
        [ModifiedBy] NVARCHAR(100),
        
        CONSTRAINT FK_ChartOfAccounts_AccountTypes FOREIGN KEY (AccountTypeId) 
            REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_ChartOfAccounts_AccountGroups FOREIGN KEY (AccountGroupId) 
            REFERENCES AccountGroups(AccountGroupId),
        CONSTRAINT FK_ChartOfAccounts_ParentAccount FOREIGN KEY (ParentAccountId) 
            REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_ChartOfAccounts_PersonalInfo FOREIGN KEY (PersonalInfoId) 
            REFERENCES PersonalInformation(PersonalInfoId),
        CONSTRAINT FK_ChartOfAccounts_Currency FOREIGN KEY (CurrencyId) 
            REFERENCES Currencies(CurrencyId)
    );
    
    PRINT '✅ تم إنشاء جدول دليل الحسابات (ChartOfAccounts)';
END

-- ========================================
-- 3. جدول سجل التعديلات
-- ========================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountAuditLog]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountAuditLog] (
        [AuditId] INT IDENTITY(1,1) PRIMARY KEY,
        [AccountId] INT NOT NULL,
        [Operation] NVARCHAR(20) NOT NULL CHECK (Operation IN ('INSERT', 'UPDATE', 'DELETE')),
        [FieldName] NVARCHAR(100),
        [OldValue] NVARCHAR(MAX),
        [NewValue] NVARCHAR(MAX),
        [ChangeDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [ChangedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [IPAddress] NVARCHAR(50),
        [UserAgent] NVARCHAR(500),
        [SessionId] NVARCHAR(100),
        [Reason] NVARCHAR(500), -- سبب التغيير
        
        CONSTRAINT FK_AccountAuditLog_Account FOREIGN KEY (AccountId) 
            REFERENCES ChartOfAccounts(AccountId) ON DELETE CASCADE
    );
    
    PRINT '✅ تم إنشاء جدول سجل التعديلات (AccountAuditLog)';
END

-- ========================================
-- 4. إنشاء الفهارس لتحسين الأداء
-- ========================================

-- فهارس جدول دليل الحسابات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountCode')
    CREATE UNIQUE INDEX IX_ChartOfAccounts_AccountCode ON ChartOfAccounts(AccountCode);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountName')
    CREATE INDEX IX_ChartOfAccounts_AccountName ON ChartOfAccounts(AccountName);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountNameAr')
    CREATE INDEX IX_ChartOfAccounts_AccountNameAr ON ChartOfAccounts(AccountNameAr);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_ParentAccount')
    CREATE INDEX IX_ChartOfAccounts_ParentAccount ON ChartOfAccounts(ParentAccountId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountType')
    CREATE INDEX IX_ChartOfAccounts_AccountType ON ChartOfAccounts(AccountTypeId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountGroup')
    CREATE INDEX IX_ChartOfAccounts_AccountGroup ON ChartOfAccounts(AccountGroupId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_IsActive')
    CREATE INDEX IX_ChartOfAccounts_IsActive ON ChartOfAccounts(IsActive);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountLevel')
    CREATE INDEX IX_ChartOfAccounts_AccountLevel ON ChartOfAccounts(AccountLevel);

-- فهارس جدول سجل التعديلات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AccountAuditLog_AccountId')
    CREATE INDEX IX_AccountAuditLog_AccountId ON AccountAuditLog(AccountId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AccountAuditLog_ChangeDate')
    CREATE INDEX IX_AccountAuditLog_ChangeDate ON AccountAuditLog(ChangeDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AccountAuditLog_Operation')
    CREATE INDEX IX_AccountAuditLog_Operation ON AccountAuditLog(Operation);

-- فهارس البيانات الشخصية
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PersonalInformation_FullName')
    CREATE INDEX IX_PersonalInformation_FullName ON PersonalInformation(FullName);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PersonalInformation_Email')
    CREATE INDEX IX_PersonalInformation_Email ON PersonalInformation(Email);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PersonalInformation_IdentityNumber')
    CREATE INDEX IX_PersonalInformation_IdentityNumber ON PersonalInformation(IdentityNumber);

PRINT '✅ تم إنشاء جميع الفهارس بنجاح';

PRINT '========================================';
PRINT '✅ تم إنشاء جميع الجداول والفهارس بنجاح';
PRINT '✅ All tables and indexes created successfully';
PRINT '========================================';

GO
