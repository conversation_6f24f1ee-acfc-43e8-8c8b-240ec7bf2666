using System;
using System.Configuration;

namespace Awqaf_Managment
{
    /// <summary>
    /// إعدادات قاعدة البيانات
    /// Database Configuration
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// سلسلة الاتصال الافتراضية
        /// Default Connection String
        /// </summary>
        public static string DefaultConnectionString
        {
            get
            {
                try
                {
                    // محاولة قراءة من App.config
                    var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                    
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        return connectionString;
                    }
                }
                catch
                {
                    // في حالة عدم وجود App.config
                }

                // سلسلة اتصال افتراضية للاختبار
                return @"Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\AwqafTest.mdf;Integrated Security=True;Connect Timeout=30";
            }
        }

        /// <summary>
        /// سلسلة اتصال للاختبار المحلي
        /// Local Test Connection String
        /// </summary>
        public static string TestConnectionString => 
            @"Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\AwqafTest.mdf;Integrated Security=True;Connect Timeout=30";

        /// <summary>
        /// التحقق من صحة الاتصال
        /// Validate Connection
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <returns>هل الاتصال صحيح</returns>
        public static bool ValidateConnection(string connectionString)
        {
            try
            {
                using (var connection = new System.Data.SqlClient.SqlConnection(connectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على سلسلة اتصال صالحة
        /// Get Valid Connection String
        /// </summary>
        /// <returns>سلسلة اتصال صالحة أو null</returns>
        public static string GetValidConnectionString()
        {
            // تجربة سلسلة الاتصال الافتراضية
            if (ValidateConnection(DefaultConnectionString))
            {
                return DefaultConnectionString;
            }

            // تجربة سلسلة الاتصال المحلية
            if (ValidateConnection(TestConnectionString))
            {
                return TestConnectionString;
            }

            // سلاسل اتصال بديلة للاختبار
            var alternativeConnections = new[]
            {
                @"Data Source=localhost;Initial Catalog=AwqafManagement;Integrated Security=True",
                @"Data Source=.\SQLEXPRESS;Initial Catalog=AwqafManagement;Integrated Security=True",
                @"Data Source=(local);Initial Catalog=AwqafManagement;Integrated Security=True"
            };

            foreach (var connectionString in alternativeConnections)
            {
                if (ValidateConnection(connectionString))
                {
                    return connectionString;
                }
            }

            return null;
        }
    }
}
