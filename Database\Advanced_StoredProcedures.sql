-- =============================================
-- نظام إدارة الأوقاف - الإجراءات المخزنة المتقدمة للدليل المحاسبي
-- Awqaf Management System - Advanced Chart of Accounts Stored Procedures
-- =============================================
-- التاريخ: 2025-07-05
-- الإصدار: 2.0 المتقدم
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. إجراء الحصول على جميع الحسابات مع التفاصيل الكاملة
-- Get All Accounts with Full Details
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetAllAccountsWithDetails
    @IncludePersonalInfo BIT = 0,
    @IncludeInactive BIT = 0,
    @AccountTypeId INT = NULL,
    @AccountGroupId INT = NULL,
    @ParentAccountId INT = NULL,
    @SearchTerm NVARCHAR(200) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ca.AccountId,
        ca.AccountCode,
        ca.AccountNameAr,
        ca.AccountNameEn,
        ca.AccountTypeId,
        at.TypeNameAr AS AccountTypeName,
        at.TypeNameEn AS AccountTypeNameEn,
        ca.AccountGroupId,
        ag.GroupNameAr AS AccountGroupName,
        ag.GroupNameEn AS AccountGroupNameEn,
        ca.ParentAccountId,
        pa.AccountNameAr AS ParentAccountName,
        ca.PersonalInfoId,
        CASE WHEN @IncludePersonalInfo = 1 THEN pi.FullNameAr ELSE NULL END AS PersonalName,
        CASE WHEN @IncludePersonalInfo = 1 THEN pi.Email ELSE NULL END AS PersonalEmail,
        CASE WHEN @IncludePersonalInfo = 1 THEN pi.Phone ELSE NULL END AS PersonalPhone,
        ca.AccountLevel,
        ca.AccountPath,
        ca.IsParent,
        ca.IsActive,
        ca.AllowPosting,
        ca.AllowDirectEntry,
        ca.Description,
        ca.Notes,
        ca.CurrencyId,
        ca.CurrencyCode,
        cur.CurrencyNameAr AS CurrencyName,
        cur.Symbol AS CurrencySymbol,
        ca.OpeningBalance,
        ca.CurrentBalance,
        ca.DebitBalance,
        ca.CreditBalance,
        ca.Nature,
        ca.BalanceType,
        ca.TaxNumber,
        ca.CommercialRegister,
        ca.BankAccount,
        ca.IBAN,
        ca.RequiresCostCenter,
        ca.RequiresProject,
        ca.SortOrder,
        ca.CreatedDate,
        ca.CreatedBy,
        ca.ModifiedDate,
        ca.ModifiedBy,
        ca.LastTransactionDate,
        -- حساب عدد الحسابات الفرعية
        (SELECT COUNT(*) FROM ChartOfAccounts sub WHERE sub.ParentAccountId = ca.AccountId) AS SubAccountsCount
    FROM ChartOfAccounts ca
    INNER JOIN AccountTypes at ON ca.AccountTypeId = at.AccountTypeId
    INNER JOIN AccountGroups ag ON ca.AccountGroupId = ag.AccountGroupId
    LEFT JOIN ChartOfAccounts pa ON ca.ParentAccountId = pa.AccountId
    LEFT JOIN PersonalInformation pi ON ca.PersonalInfoId = pi.PersonalInfoId
    LEFT JOIN Currencies cur ON ca.CurrencyId = cur.CurrencyId
    WHERE 
        (@IncludeInactive = 1 OR ca.IsActive = 1)
        AND (@AccountTypeId IS NULL OR ca.AccountTypeId = @AccountTypeId)
        AND (@AccountGroupId IS NULL OR ca.AccountGroupId = @AccountGroupId)
        AND (@ParentAccountId IS NULL OR ca.ParentAccountId = @ParentAccountId)
        AND (@SearchTerm IS NULL OR 
             ca.AccountNameAr LIKE '%' + @SearchTerm + '%' OR
             ca.AccountNameEn LIKE '%' + @SearchTerm + '%' OR
             ca.AccountCode LIKE '%' + @SearchTerm + '%' OR
             ca.Description LIKE '%' + @SearchTerm + '%')
    ORDER BY 
        ca.AccountLevel,
        ca.AccountPath,
        ca.SortOrder,
        ca.AccountCode;
END
GO

-- =============================================
-- 2. إجراء إضافة أو تحديث حساب
-- Add or Update Account
-- =============================================
CREATE OR ALTER PROCEDURE sp_SaveAccount
    @AccountId INT = 0,
    @AccountCode NVARCHAR(20) = NULL,
    @AccountNameAr NVARCHAR(200),
    @AccountNameEn NVARCHAR(200) = NULL,
    @AccountTypeId INT,
    @AccountGroupId INT,
    @ParentAccountId INT = NULL,
    @PersonalInfoId INT = NULL,
    @Description NVARCHAR(1000) = NULL,
    @Notes NVARCHAR(2000) = NULL,
    @CurrencyId INT = 1,
    @OpeningBalance DECIMAL(18,4) = 0,
    @Nature NVARCHAR(10) = 'مدين',
    @BalanceType NVARCHAR(10) = 'مدين',
    @IsActive BIT = 1,
    @AllowPosting BIT = 1,
    @AllowDirectEntry BIT = 1,
    @TaxNumber NVARCHAR(50) = NULL,
    @CommercialRegister NVARCHAR(50) = NULL,
    @BankAccount NVARCHAR(50) = NULL,
    @IBAN NVARCHAR(34) = NULL,
    @RequiresCostCenter BIT = 0,
    @RequiresProject BIT = 0,
    @SortOrder INT = 0,
    @UserId INT,
    @ResultAccountId INT OUTPUT,
    @ResultMessage NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @AccountLevel INT = 1;
        DECLARE @AccountPath NVARCHAR(500) = '';
        DECLARE @IsParent BIT = 0;
        DECLARE @GeneratedCode NVARCHAR(20);
        
        -- حساب المستوى والمسار إذا كان هناك حساب أب
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT 
                @AccountLevel = AccountLevel + 1,
                @AccountPath = ISNULL(AccountPath, AccountCode) + '.'
            FROM ChartOfAccounts 
            WHERE AccountId = @ParentAccountId;
            
            -- تحديث الحساب الأب ليصبح حساب أب
            UPDATE ChartOfAccounts 
            SET IsParent = 1, ModifiedDate = GETDATE(), ModifiedBy = @UserId
            WHERE AccountId = @ParentAccountId;
        END
        
        -- توليد رمز الحساب إذا لم يتم تمريره
        IF @AccountCode IS NULL OR @AccountCode = ''
        BEGIN
            EXEC sp_GenerateAccountCode 
                @ParentAccountId = @ParentAccountId,
                @AccountTypeId = @AccountTypeId,
                @GeneratedCode = @GeneratedCode OUTPUT;
            SET @AccountCode = @GeneratedCode;
        END
        
        -- إكمال المسار
        SET @AccountPath = @AccountPath + @AccountCode;
        
        -- التحقق من عدم تكرار رمز الحساب
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = @AccountCode AND AccountId != @AccountId)
        BEGIN
            SET @ResultMessage = 'رمز الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @AccountId = 0 -- إضافة حساب جديد
        BEGIN
            INSERT INTO ChartOfAccounts (
                AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId,
                ParentAccountId, PersonalInfoId, AccountLevel, AccountPath, IsParent,
                IsActive, AllowPosting, AllowDirectEntry, Description, Notes,
                CurrencyId, OpeningBalance, CurrentBalance, Nature, BalanceType,
                TaxNumber, CommercialRegister, BankAccount, IBAN,
                RequiresCostCenter, RequiresProject, SortOrder,
                CreatedDate, CreatedBy
            )
            VALUES (
                @AccountCode, @AccountNameAr, @AccountNameEn, @AccountTypeId, @AccountGroupId,
                @ParentAccountId, @PersonalInfoId, @AccountLevel, @AccountPath, @IsParent,
                @IsActive, @AllowPosting, @AllowDirectEntry, @Description, @Notes,
                @CurrencyId, @OpeningBalance, @OpeningBalance, @Nature, @BalanceType,
                @TaxNumber, @CommercialRegister, @BankAccount, @IBAN,
                @RequiresCostCenter, @RequiresProject, @SortOrder,
                GETDATE(), @UserId
            );
            
            SET @ResultAccountId = SCOPE_IDENTITY();
            SET @ResultMessage = 'تم إضافة الحساب بنجاح';
            
            -- تسجيل في سجل التدقيق
            INSERT INTO AccountAuditLog (AccountId, OperationType, OperationDate, UserId, ChangeReason)
            VALUES (@ResultAccountId, 'إنشاء', GETDATE(), @UserId, 'إنشاء حساب جديد');
        END
        ELSE -- تحديث حساب موجود
        BEGIN
            UPDATE ChartOfAccounts SET
                AccountNameAr = @AccountNameAr,
                AccountNameEn = @AccountNameEn,
                AccountTypeId = @AccountTypeId,
                AccountGroupId = @AccountGroupId,
                PersonalInfoId = @PersonalInfoId,
                Description = @Description,
                Notes = @Notes,
                CurrencyId = @CurrencyId,
                Nature = @Nature,
                BalanceType = @BalanceType,
                IsActive = @IsActive,
                AllowPosting = @AllowPosting,
                AllowDirectEntry = @AllowDirectEntry,
                TaxNumber = @TaxNumber,
                CommercialRegister = @CommercialRegister,
                BankAccount = @BankAccount,
                IBAN = @IBAN,
                RequiresCostCenter = @RequiresCostCenter,
                RequiresProject = @RequiresProject,
                SortOrder = @SortOrder,
                ModifiedDate = GETDATE(),
                ModifiedBy = @UserId
            WHERE AccountId = @AccountId;
            
            SET @ResultAccountId = @AccountId;
            SET @ResultMessage = 'تم تحديث الحساب بنجاح';
            
            -- تسجيل في سجل التدقيق
            INSERT INTO AccountAuditLog (AccountId, OperationType, OperationDate, UserId, ChangeReason)
            VALUES (@AccountId, 'تعديل', GETDATE(), @UserId, 'تحديث بيانات الحساب');
        END
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @ResultMessage = 'خطأ في حفظ الحساب: ' + ERROR_MESSAGE();
        SET @ResultAccountId = 0;
    END CATCH
END
-- =============================================
-- 3. إجراء توليد رمز الحساب التلقائي
-- Generate Account Code Automatically
-- =============================================
CREATE OR ALTER PROCEDURE sp_GenerateAccountCode
    @ParentAccountId INT = NULL,
    @AccountTypeId INT,
    @GeneratedCode NVARCHAR(20) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ParentCode NVARCHAR(20) = '';
    DECLARE @NextNumber INT = 1;
    DECLARE @CodeFormat NVARCHAR(20);

    IF @ParentAccountId IS NOT NULL
    BEGIN
        -- الحصول على رمز الحساب الأب
        SELECT @ParentCode = AccountCode FROM ChartOfAccounts WHERE AccountId = @ParentAccountId;

        -- البحث عن أعلى رقم في الحسابات الفرعية
        SELECT @NextNumber = ISNULL(MAX(CAST(RIGHT(AccountCode, 3) AS INT)), 0) + 1
        FROM ChartOfAccounts
        WHERE ParentAccountId = @ParentAccountId
        AND AccountCode LIKE @ParentCode + '.%';

        SET @GeneratedCode = @ParentCode + '.' + RIGHT('000' + CAST(@NextNumber AS NVARCHAR), 3);
    END
    ELSE
    BEGIN
        -- حساب رئيسي - استخدام رمز نوع الحساب
        SELECT @CodeFormat = TypeCode FROM AccountTypes WHERE AccountTypeId = @AccountTypeId;

        -- البحث عن أعلى رقم في الحسابات الرئيسية من نفس النوع
        SELECT @NextNumber = ISNULL(MAX(CAST(RIGHT(AccountCode, 2) AS INT)), 0) + 1
        FROM ChartOfAccounts ca
        INNER JOIN AccountTypes at ON ca.AccountTypeId = at.AccountTypeId
        WHERE ca.AccountTypeId = @AccountTypeId
        AND ca.ParentAccountId IS NULL
        AND AccountCode LIKE @CodeFormat + '%';

        SET @GeneratedCode = @CodeFormat + RIGHT('00' + CAST(@NextNumber AS NVARCHAR), 2);
    END
END
GO

-- =============================================
-- 4. إجراء حذف حساب مع التحقق من القيود
-- Delete Account with Constraint Checking
-- =============================================
CREATE OR ALTER PROCEDURE sp_DeleteAccount
    @AccountId INT,
    @UserId INT,
    @ForceDelete BIT = 0,
    @ResultMessage NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;

        -- التحقق من وجود الحساب
        IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @AccountId)
        BEGIN
            SET @ResultMessage = 'الحساب غير موجود';
            ROLLBACK TRANSACTION;
            RETURN;
        END

        -- التحقق من وجود حسابات فرعية
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE ParentAccountId = @AccountId)
        BEGIN
            IF @ForceDelete = 0
            BEGIN
                SET @ResultMessage = 'لا يمكن حذف الحساب لوجود حسابات فرعية';
                ROLLBACK TRANSACTION;
                RETURN;
            END
            ELSE
            BEGIN
                -- حذف الحسابات الفرعية أولاً (يمكن تحسين هذا لاحقاً)
                DELETE FROM ChartOfAccounts WHERE ParentAccountId = @AccountId;
            END
        END

        -- تسجيل في سجل التدقيق قبل الحذف
        INSERT INTO AccountAuditLog (AccountId, OperationType, OperationDate, UserId, ChangeReason)
        VALUES (@AccountId, 'حذف', GETDATE(), @UserId, 'حذف الحساب');

        -- حذف الحساب
        DELETE FROM ChartOfAccounts WHERE AccountId = @AccountId;

        SET @ResultMessage = 'تم حذف الحساب بنجاح';
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @ResultMessage = 'خطأ في حذف الحساب: ' + ERROR_MESSAGE();
    END CATCH
END
GO
