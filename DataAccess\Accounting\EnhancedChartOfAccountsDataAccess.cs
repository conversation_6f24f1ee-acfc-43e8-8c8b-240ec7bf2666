using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Awqaf_Managment.Models;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.DataAccess.Base;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات دليل الحسابات المحسنة والشاملة
    /// Enhanced and Comprehensive Chart of Accounts Data Access Layer
    /// </summary>
    public class EnhancedChartOfAccountsDataAccess : BaseDataAccess
    {
        #region المنشئ - Constructor
        
        /// <summary>
        /// منشئ طبقة الوصول للبيانات
        /// Data Access Layer Constructor
        /// </summary>
        public EnhancedChartOfAccountsDataAccess() : base()
        {
        }

        #endregion

        #region عمليات CRUD الأساسية - Basic CRUD Operations

        /// <summary>
        /// الحصول على جميع الحسابات مع التفاصيل الكاملة
        /// Get All Accounts with Full Details
        /// </summary>
        /// <param name="includePersonalInfo">تضمين البيانات الشخصية</param>
        /// <param name="includeInactive">تضمين الحسابات غير النشطة</param>
        /// <param name="accountTypeId">فلترة حسب نوع الحساب</param>
        /// <param name="accountGroupId">فلترة حسب مجموعة الحساب</param>
        /// <param name="parentAccountId">فلترة حسب الحساب الأب</param>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الحسابات</returns>
        public async Task<List<ChartOfAccount>> GetAllAccountsWithDetailsAsync(
            bool includePersonalInfo = false,
            bool includeInactive = false,
            int? accountTypeId = null,
            int? accountGroupId = null,
            int? parentAccountId = null,
            string searchTerm = null)
        {
            var accounts = new List<ChartOfAccount>();

            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand("sp_GetAllAccountsWithDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@IncludePersonalInfo", includePersonalInfo);
                        command.Parameters.AddWithValue("@IncludeInactive", includeInactive);
                        command.Parameters.AddWithValue("@AccountTypeId", (object)accountTypeId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountGroupId", (object)accountGroupId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ParentAccountId", (object)parentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@SearchTerm", (object)searchTerm ?? DBNull.Value);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                accounts.Add(MapReaderToAccount(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في الحصول على الحسابات: {ex.Message}");
                throw new Exception($"خطأ في الحصول على الحسابات: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// Get Account by ID
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>بيانات الحساب</returns>
        public async Task<ChartOfAccount> GetAccountByIdAsync(int accountId)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand("sp_GetAllAccountsWithDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@IncludePersonalInfo", true);
                        command.Parameters.AddWithValue("@IncludeInactive", true);
                        command.Parameters.AddWithValue("@AccountTypeId", DBNull.Value);
                        command.Parameters.AddWithValue("@AccountGroupId", DBNull.Value);
                        command.Parameters.AddWithValue("@ParentAccountId", DBNull.Value);
                        command.Parameters.AddWithValue("@SearchTerm", DBNull.Value);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var account = MapReaderToAccount(reader);
                                if (account.AccountId == accountId)
                                    return account;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في الحصول على الحساب {accountId}: {ex.Message}");
                throw new Exception($"خطأ في الحصول على الحساب: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// حفظ حساب (إضافة أو تحديث)
        /// Save Account (Add or Update)
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>معرف الحساب المحفوظ</returns>
        public async Task<int> SaveAccountAsync(ChartOfAccount account, int userId)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            using (var command = new SqlCommand("sp_SaveAccount", connection, transaction))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                
                                // إضافة جميع المعاملات
                                AddAccountParameters(command, account, userId);
                                
                                // معاملات الإخراج
                                var resultAccountIdParam = new SqlParameter("@ResultAccountId", SqlDbType.Int)
                                {
                                    Direction = ParameterDirection.Output
                                };
                                command.Parameters.Add(resultAccountIdParam);
                                
                                var resultMessageParam = new SqlParameter("@ResultMessage", SqlDbType.NVarChar, 500)
                                {
                                    Direction = ParameterDirection.Output
                                };
                                command.Parameters.Add(resultMessageParam);

                                await command.ExecuteNonQueryAsync();
                                
                                var resultAccountId = (int)resultAccountIdParam.Value;
                                var resultMessage = resultMessageParam.Value?.ToString();
                                
                                if (resultAccountId > 0)
                                {
                                    transaction.Commit();
                                    return resultAccountId;
                                }
                                else
                                {
                                    transaction.Rollback();
                                    throw new Exception(resultMessage ?? "فشل في حفظ الحساب");
                                }
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في حفظ الحساب: {ex.Message}");
                throw new Exception($"خطأ في حفظ الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف حساب
        /// Delete Account
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="forceDelete">حذف قسري</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<bool> DeleteAccountAsync(int accountId, int userId, bool forceDelete = false)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            using (var command = new SqlCommand("sp_DeleteAccount", connection, transaction))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@AccountId", accountId);
                                command.Parameters.AddWithValue("@UserId", userId);
                                command.Parameters.AddWithValue("@ForceDelete", forceDelete);
                                
                                var resultMessageParam = new SqlParameter("@ResultMessage", SqlDbType.NVarChar, 500)
                                {
                                    Direction = ParameterDirection.Output
                                };
                                command.Parameters.Add(resultMessageParam);

                                await command.ExecuteNonQueryAsync();
                                
                                var resultMessage = resultMessageParam.Value?.ToString();
                                
                                if (resultMessage?.Contains("بنجاح") == true)
                                {
                                    transaction.Commit();
                                    return true;
                                }
                                else
                                {
                                    transaction.Rollback();
                                    throw new Exception(resultMessage ?? "فشل في حذف الحساب");
                                }
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في حذف الحساب {accountId}: {ex.Message}");
                throw new Exception($"خطأ في حذف الحساب: {ex.Message}", ex);
            }
        }

        #endregion

        #region طرق مساعدة - Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن حساب
        /// Map Data Reader to Account Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الحساب</returns>
        private ChartOfAccount MapReaderToAccount(SqlDataReader reader)
        {
            return new ChartOfAccount
            {
                AccountId = reader.GetInt32("AccountId"),
                AccountCode = reader.GetString("AccountCode"),
                AccountNameAr = reader.GetString("AccountNameAr"),
                AccountNameEn = reader.IsDBNull("AccountNameEn") ? null : reader.GetString("AccountNameEn"),
                AccountTypeId = reader.GetInt32("AccountTypeId"),
                AccountTypeName = reader.IsDBNull("AccountTypeName") ? null : reader.GetString("AccountTypeName"),
                AccountGroupId = reader.GetInt32("AccountGroupId"),
                AccountGroupName = reader.IsDBNull("AccountGroupName") ? null : reader.GetString("AccountGroupName"),
                ParentAccountId = reader.IsDBNull("ParentAccountId") ? (int?)null : reader.GetInt32("ParentAccountId"),
                ParentAccountName = reader.IsDBNull("ParentAccountName") ? null : reader.GetString("ParentAccountName"),
                PersonalInfoId = reader.IsDBNull("PersonalInfoId") ? (int?)null : reader.GetInt32("PersonalInfoId"),
                PersonalName = reader.IsDBNull("PersonalName") ? null : reader.GetString("PersonalName"),
                AccountLevel = reader.GetInt32("AccountLevel"),
                AccountPath = reader.IsDBNull("AccountPath") ? null : reader.GetString("AccountPath"),
                IsParent = reader.GetBoolean("IsParent"),
                IsActive = reader.GetBoolean("IsActive"),
                AllowPosting = reader.GetBoolean("AllowPosting"),
                AllowDirectEntry = reader.IsDBNull("AllowDirectEntry") ? true : reader.GetBoolean("AllowDirectEntry"),
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                CurrencyId = reader.IsDBNull("CurrencyId") ? (int?)null : reader.GetInt32("CurrencyId"),
                CurrencyCode = reader.IsDBNull("CurrencyCode") ? "SAR" : reader.GetString("CurrencyCode"),
                CurrencyName = reader.IsDBNull("CurrencyName") ? null : reader.GetString("CurrencyName"),
                CurrencySymbol = reader.IsDBNull("CurrencySymbol") ? null : reader.GetString("CurrencySymbol"),
                OpeningBalance = reader.IsDBNull("OpeningBalance") ? 0 : reader.GetDecimal("OpeningBalance"),
                CurrentBalance = reader.IsDBNull("CurrentBalance") ? 0 : reader.GetDecimal("CurrentBalance"),
                DebitBalance = reader.IsDBNull("DebitBalance") ? 0 : reader.GetDecimal("DebitBalance"),
                CreditBalance = reader.IsDBNull("CreditBalance") ? 0 : reader.GetDecimal("CreditBalance"),
                Nature = reader.IsDBNull("Nature") ? "مدين" : reader.GetString("Nature"),
                BalanceType = reader.IsDBNull("BalanceType") ? "مدين" : reader.GetString("BalanceType"),
                TaxNumber = reader.IsDBNull("TaxNumber") ? null : reader.GetString("TaxNumber"),
                CommercialRegister = reader.IsDBNull("CommercialRegister") ? null : reader.GetString("CommercialRegister"),
                BankAccount = reader.IsDBNull("BankAccount") ? null : reader.GetString("BankAccount"),
                IBAN = reader.IsDBNull("IBAN") ? null : reader.GetString("IBAN"),
                RequiresCostCenter = reader.IsDBNull("RequiresCostCenter") ? false : reader.GetBoolean("RequiresCostCenter"),
                RequiresProject = reader.IsDBNull("RequiresProject") ? false : reader.GetBoolean("RequiresProject"),
                SortOrder = reader.IsDBNull("SortOrder") ? 0 : reader.GetInt32("SortOrder"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                CreatedBy = reader.IsDBNull("CreatedBy") ? (int?)null : reader.GetInt32("CreatedBy"),
                ModifiedDate = reader.IsDBNull("ModifiedDate") ? (DateTime?)null : reader.GetDateTime("ModifiedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? (int?)null : reader.GetInt32("ModifiedBy"),
                LastTransactionDate = reader.IsDBNull("LastTransactionDate") ? (DateTime?)null : reader.GetDateTime("LastTransactionDate"),
                SubAccountsCount = reader.IsDBNull("SubAccountsCount") ? 0 : reader.GetInt32("SubAccountsCount")
            };
        }

        /// <summary>
        /// إضافة معاملات الحساب للأمر
        /// Add Account Parameters to Command
        /// </summary>
        /// <param name="command">الأمر</param>
        /// <param name="account">بيانات الحساب</param>
        /// <param name="userId">معرف المستخدم</param>
        private void AddAccountParameters(SqlCommand command, ChartOfAccount account, int userId)
        {
            command.Parameters.AddWithValue("@AccountId", account.AccountId);
            command.Parameters.AddWithValue("@AccountCode", (object)account.AccountCode ?? DBNull.Value);
            command.Parameters.AddWithValue("@AccountNameAr", account.AccountNameAr);
            command.Parameters.AddWithValue("@AccountNameEn", (object)account.AccountNameEn ?? DBNull.Value);
            command.Parameters.AddWithValue("@AccountTypeId", account.AccountTypeId);
            command.Parameters.AddWithValue("@AccountGroupId", account.AccountGroupId);
            command.Parameters.AddWithValue("@ParentAccountId", (object)account.ParentAccountId ?? DBNull.Value);
            command.Parameters.AddWithValue("@PersonalInfoId", (object)account.PersonalInfoId ?? DBNull.Value);
            command.Parameters.AddWithValue("@Description", (object)account.Description ?? DBNull.Value);
            command.Parameters.AddWithValue("@Notes", (object)account.Notes ?? DBNull.Value);
            command.Parameters.AddWithValue("@CurrencyId", (object)account.CurrencyId ?? 1);
            command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
            command.Parameters.AddWithValue("@Nature", (object)account.Nature ?? "مدين");
            command.Parameters.AddWithValue("@BalanceType", (object)account.BalanceType ?? "مدين");
            command.Parameters.AddWithValue("@IsActive", account.IsActive);
            command.Parameters.AddWithValue("@AllowPosting", account.AllowPosting);
            command.Parameters.AddWithValue("@AllowDirectEntry", account.AllowDirectEntry);
            command.Parameters.AddWithValue("@TaxNumber", (object)account.TaxNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@CommercialRegister", (object)account.CommercialRegister ?? DBNull.Value);
            command.Parameters.AddWithValue("@BankAccount", (object)account.BankAccount ?? DBNull.Value);
            command.Parameters.AddWithValue("@IBAN", (object)account.IBAN ?? DBNull.Value);
            command.Parameters.AddWithValue("@RequiresCostCenter", account.RequiresCostCenter);
            command.Parameters.AddWithValue("@RequiresProject", account.RequiresProject);
            command.Parameters.AddWithValue("@SortOrder", account.SortOrder);
            command.Parameters.AddWithValue("@UserId", userId);
        }

        /// <summary>
        /// تسجيل الأخطاء
        /// Log Errors
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        private void LogError(string message)
        {
            // يمكن تحسين هذا لاحقاً لاستخدام نظام تسجيل متقدم
            System.Diagnostics.Debug.WriteLine($"[ChartOfAccountsDataAccess] {DateTime.Now}: {message}");
        }

        #endregion

        #region البحث المتقدم - Advanced Search

        /// <summary>
        /// البحث المتقدم في الحسابات
        /// Advanced Account Search
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<ChartOfAccount>> SearchAccountsAsync(AccountSearchCriteria searchCriteria)
        {
            var accounts = new List<ChartOfAccount>();

            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("sp_SearchAccounts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // إضافة معايير البحث
                        command.Parameters.AddWithValue("@SearchTerm", (object)searchCriteria.SearchTerm ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountTypeId", (object)searchCriteria.AccountTypeId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountGroupId", (object)searchCriteria.AccountGroupId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", (object)searchCriteria.IsActive ?? DBNull.Value);
                        command.Parameters.AddWithValue("@HasPersonalInfo", (object)searchCriteria.HasPersonalInfo ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CurrencyId", (object)searchCriteria.CurrencyId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@MinBalance", (object)searchCriteria.MinBalance ?? DBNull.Value);
                        command.Parameters.AddWithValue("@MaxBalance", (object)searchCriteria.MaxBalance ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedDateFrom", (object)searchCriteria.CreatedDateFrom ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedDateTo", (object)searchCriteria.CreatedDateTo ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PageNumber", searchCriteria.PageNumber);
                        command.Parameters.AddWithValue("@PageSize", searchCriteria.PageSize);
                        command.Parameters.AddWithValue("@SortBy", searchCriteria.SortBy ?? "AccountCode");
                        command.Parameters.AddWithValue("@SortDirection", searchCriteria.SortDirection ?? "ASC");

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var account = MapReaderToAccount(reader);
                                // إضافة العدد الإجمالي للسجلات
                                if (reader.FieldCount > 30) // التحقق من وجود حقل TotalRecords
                                {
                                    searchCriteria.TotalRecords = reader.IsDBNull("TotalRecords") ? 0 : reader.GetInt32("TotalRecords");
                                }
                                accounts.Add(account);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في البحث المتقدم: {ex.Message}");
                throw new Exception($"خطأ في البحث المتقدم: {ex.Message}", ex);
            }

            return accounts;
        }

        #endregion

        #region توليد رمز الحساب - Generate Account Code

        /// <summary>
        /// توليد رمز حساب تلقائي
        /// Generate Automatic Account Code
        /// </summary>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <returns>رمز الحساب المولد</returns>
        public async Task<string> GenerateAccountCodeAsync(int? parentAccountId, int accountTypeId)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("sp_GenerateAccountCode", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ParentAccountId", (object)parentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountTypeId", accountTypeId);

                        var generatedCodeParam = new SqlParameter("@GeneratedCode", SqlDbType.NVarChar, 20)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(generatedCodeParam);

                        await command.ExecuteNonQueryAsync();

                        return generatedCodeParam.Value?.ToString() ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في توليد رمز الحساب: {ex.Message}");
                throw new Exception($"خطأ في توليد رمز الحساب: {ex.Message}", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// معايير البحث في الحسابات
    /// Account Search Criteria
    /// </summary>
    public class AccountSearchCriteria
    {
        public string SearchTerm { get; set; }
        public int? AccountTypeId { get; set; }
        public int? AccountGroupId { get; set; }
        public bool? IsActive { get; set; }
        public bool? HasPersonalInfo { get; set; }
        public int? CurrencyId { get; set; }
        public decimal? MinBalance { get; set; }
        public decimal? MaxBalance { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortBy { get; set; } = "AccountCode";
        public string SortDirection { get; set; } = "ASC";
        public int TotalRecords { get; set; } = 0;
    }
}
