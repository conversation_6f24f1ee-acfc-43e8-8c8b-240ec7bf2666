-- =============================================
-- نظام إدارة الأوقاف - البيانات الأولية المتقدمة للدليل المحاسبي
-- Awqaf Management System - Advanced Chart of Accounts Initial Data
-- =============================================
-- التاريخ: 2025-07-05
-- الإصدار: 2.0 المتقدم
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. إدراج أنواع الحسابات
-- Insert Account Types
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountTypes)
BEGIN
    INSERT INTO AccountTypes (TypeCode, TypeNameAr, TypeNameEn, Description, NormalBalance, SortOrder) VALUES
    ('AST', 'الأصول', 'Assets', 'جميع الأصول المملوكة للمؤسسة', 'مدين', 1),
    ('LIB', 'الخصوم', 'Liabilities', 'جميع الالتزامات والديون على المؤسسة', 'دائن', 2),
    ('EQT', 'حقوق الملكية', 'Equity', 'حقوق أصحاب المؤسسة', 'دائن', 3),
    ('REV', 'الإيرادات', 'Revenue', 'جميع الإيرادات والدخل', 'دائن', 4),
    ('EXP', 'المصروفات', 'Expenses', 'جميع المصروفات والتكاليف', 'مدين', 5);
    
    PRINT 'تم إدراج أنواع الحسابات بنجاح';
END

-- =============================================
-- 2. إدراج مجموعات الحسابات
-- Insert Account Groups
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountGroups)
BEGIN
    -- مجموعات الأصول
    INSERT INTO AccountGroups (GroupCode, GroupNameAr, GroupNameEn, AccountTypeId, Description, SortOrder) VALUES
    ('CA', 'الأصول المتداولة', 'Current Assets', 1, 'الأصول التي يمكن تحويلها إلى نقد خلال سنة', 1),
    ('FA', 'الأصول الثابتة', 'Fixed Assets', 1, 'الأصول طويلة الأجل', 2),
    ('IA', 'الأصول غير الملموسة', 'Intangible Assets', 1, 'الأصول غير المادية', 3),
    
    -- مجموعات الخصوم
    ('CL', 'الخصوم المتداولة', 'Current Liabilities', 2, 'الالتزامات قصيرة الأجل', 4),
    ('LTL', 'الخصوم طويلة الأجل', 'Long-term Liabilities', 2, 'الالتزامات طويلة الأجل', 5),
    
    -- مجموعات حقوق الملكية
    ('CE', 'رأس المال', 'Capital Equity', 3, 'رأس المال والاحتياطيات', 6),
    ('RE', 'الأرباح المحتجزة', 'Retained Earnings', 3, 'الأرباح المتراكمة', 7),
    
    -- مجموعات الإيرادات
    ('OR', 'الإيرادات التشغيلية', 'Operating Revenue', 4, 'الإيرادات من النشاط الأساسي', 8),
    ('NOR', 'الإيرادات غير التشغيلية', 'Non-Operating Revenue', 4, 'الإيرادات من الأنشطة الأخرى', 9),
    
    -- مجموعات المصروفات
    ('OE', 'المصروفات التشغيلية', 'Operating Expenses', 5, 'المصروفات من النشاط الأساسي', 10),
    ('AE', 'المصروفات الإدارية', 'Administrative Expenses', 5, 'المصروفات الإدارية والعمومية', 11),
    ('FE', 'المصروفات المالية', 'Financial Expenses', 5, 'الفوائد والرسوم المالية', 12);
    
    PRINT 'تم إدراج مجموعات الحسابات بنجاح';
END

-- =============================================
-- 3. إدراج العملات
-- Insert Currencies
-- =============================================
IF NOT EXISTS (SELECT 1 FROM Currencies)
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate, IsBaseCurrency) VALUES
    ('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.0, 1),
    ('USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 0),
    ('EUR', 'يورو', 'Euro', '€', 4.10, 0),
    ('GBP', 'جنيه إسترليني', 'British Pound', '£', 4.65, 0),
    ('AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ', 1.02, 0),
    ('KWD', 'دينار كويتي', 'Kuwaiti Dinar', 'د.ك', 12.25, 0),
    ('QAR', 'ريال قطري', 'Qatari Riyal', 'ر.ق', 1.03, 0),
    ('BHD', 'دينار بحريني', 'Bahraini Dinar', 'د.ب', 9.95, 0),
    ('OMR', 'ريال عماني', 'Omani Rial', 'ر.ع', 9.75, 0),
    ('JOD', 'دينار أردني', 'Jordanian Dinar', 'د.أ', 5.30, 0);
    
    PRINT 'تم إدراج العملات بنجاح';
END

-- =============================================
-- 4. إدراج دليل الحسابات الأساسي
-- Insert Basic Chart of Accounts
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts)
BEGIN
    -- الحسابات الرئيسية للأصول
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, Nature, BalanceType, SortOrder) VALUES
    ('AST01', 'الأصول المتداولة', 'Current Assets', 1, 1, 1, 1, 'مدين', 'مدين', 1),
    ('AST02', 'الأصول الثابتة', 'Fixed Assets', 1, 2, 1, 1, 'مدين', 'مدين', 2),
    
    -- الحسابات الفرعية للأصول المتداولة
    ('AST01.001', 'النقدية في الصندوق', 'Cash in Hand', 1, 1, 2, 0, 'مدين', 'مدين', 3),
    ('AST01.002', 'النقدية في البنك - الحساب الجاري', 'Cash in Bank - Current Account', 1, 1, 2, 0, 'مدين', 'مدين', 4),
    ('AST01.003', 'حسابات المدينين', 'Accounts Receivable', 1, 1, 2, 0, 'مدين', 'مدين', 5),
    ('AST01.004', 'المخزون', 'Inventory', 1, 1, 2, 0, 'مدين', 'مدين', 6),
    ('AST01.005', 'المصروفات المدفوعة مقدماً', 'Prepaid Expenses', 1, 1, 2, 0, 'مدين', 'مدين', 7),
    
    -- الحسابات الفرعية للأصول الثابتة
    ('AST02.001', 'الأراضي', 'Land', 1, 2, 2, 0, 'مدين', 'مدين', 8),
    ('AST02.002', 'المباني', 'Buildings', 1, 2, 2, 0, 'مدين', 'مدين', 9),
    ('AST02.003', 'الأثاث والمعدات', 'Furniture & Equipment', 1, 2, 2, 0, 'مدين', 'مدين', 10),
    ('AST02.004', 'السيارات', 'Vehicles', 1, 2, 2, 0, 'مدين', 'مدين', 11),
    ('AST02.005', 'مجمع إهلاك المباني', 'Accumulated Depreciation - Buildings', 1, 2, 2, 0, 'دائن', 'دائن', 12),
    
    -- الحسابات الرئيسية للخصوم
    ('LIB01', 'الخصوم المتداولة', 'Current Liabilities', 2, 4, 1, 1, 'دائن', 'دائن', 13),
    ('LIB02', 'الخصوم طويلة الأجل', 'Long-term Liabilities', 2, 5, 1, 1, 'دائن', 'دائن', 14),
    
    -- الحسابات الفرعية للخصوم المتداولة
    ('LIB01.001', 'حسابات الدائنين', 'Accounts Payable', 2, 4, 2, 0, 'دائن', 'دائن', 15),
    ('LIB01.002', 'الرواتب المستحقة', 'Accrued Salaries', 2, 4, 2, 0, 'دائن', 'دائن', 16),
    ('LIB01.003', 'الضرائب المستحقة', 'Accrued Taxes', 2, 4, 2, 0, 'دائن', 'دائن', 17),
    
    -- حقوق الملكية
    ('EQT01', 'رأس المال', 'Capital', 3, 6, 1, 0, 'دائن', 'دائن', 18),
    ('EQT02', 'الأرباح المحتجزة', 'Retained Earnings', 3, 7, 1, 0, 'دائن', 'دائن', 19),
    
    -- الإيرادات
    ('REV01', 'إيرادات الإيجارات', 'Rental Revenue', 4, 8, 1, 0, 'دائن', 'دائن', 20),
    ('REV02', 'إيرادات الاستثمارات', 'Investment Revenue', 4, 9, 1, 0, 'دائن', 'دائن', 21),
    ('REV03', 'إيرادات أخرى', 'Other Revenue', 4, 9, 1, 0, 'دائن', 'دائن', 22),
    
    -- المصروفات
    ('EXP01', 'رواتب الموظفين', 'Employee Salaries', 5, 11, 1, 0, 'مدين', 'مدين', 23),
    ('EXP02', 'مصروفات الصيانة', 'Maintenance Expenses', 5, 10, 1, 0, 'مدين', 'مدين', 24),
    ('EXP03', 'مصروفات الكهرباء والماء', 'Utilities Expenses', 5, 10, 1, 0, 'مدين', 'مدين', 25),
    ('EXP04', 'مصروفات إدارية', 'Administrative Expenses', 5, 11, 1, 0, 'مدين', 'مدين', 26),
    ('EXP05', 'مصروفات مالية', 'Financial Expenses', 5, 12, 1, 0, 'مدين', 'مدين', 27);
    
    -- تحديث المسارات الهرمية
    UPDATE ChartOfAccounts SET AccountPath = AccountCode WHERE AccountLevel = 1;
    UPDATE ChartOfAccounts SET AccountPath = 
        (SELECT p.AccountPath FROM ChartOfAccounts p WHERE p.AccountCode = LEFT(ChartOfAccounts.AccountCode, CHARINDEX('.', ChartOfAccounts.AccountCode) - 1)) 
        + '.' + AccountCode 
    WHERE AccountLevel = 2;
    
    -- تحديث الحسابات الأب
    UPDATE ChartOfAccounts SET ParentAccountId = 
        (SELECT AccountId FROM ChartOfAccounts p WHERE p.AccountCode = 'AST01')
    WHERE AccountCode IN ('AST01.001', 'AST01.002', 'AST01.003', 'AST01.004', 'AST01.005');
    
    UPDATE ChartOfAccounts SET ParentAccountId = 
        (SELECT AccountId FROM ChartOfAccounts p WHERE p.AccountCode = 'AST02')
    WHERE AccountCode IN ('AST02.001', 'AST02.002', 'AST02.003', 'AST02.004', 'AST02.005');
    
    UPDATE ChartOfAccounts SET ParentAccountId = 
        (SELECT AccountId FROM ChartOfAccounts p WHERE p.AccountCode = 'LIB01')
    WHERE AccountCode IN ('LIB01.001', 'LIB01.002', 'LIB01.003');
    
    PRINT 'تم إدراج دليل الحسابات الأساسي بنجاح';
END

-- =============================================
-- 5. إدراج قوالب الحسابات
-- Insert Account Templates
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountTemplates)
BEGIN
    INSERT INTO AccountTemplates (TemplateName, Description, IndustryType, IsDefault) VALUES
    ('قالب الأوقاف الأساسي', 'قالب دليل حسابات أساسي لإدارة الأوقاف', 'أوقاف', 1),
    ('قالب الشركات التجارية', 'قالب دليل حسابات للشركات التجارية', 'تجاري', 0),
    ('قالب المؤسسات الخيرية', 'قالب دليل حسابات للمؤسسات الخيرية', 'خيري', 0),
    ('قالب العقارات', 'قالب دليل حسابات لشركات العقارات', 'عقاري', 0);
    
    PRINT 'تم إدراج قوالب الحسابات بنجاح';
END

PRINT '=== تم إكمال إدراج جميع البيانات الأولية بنجاح ===';
GO
