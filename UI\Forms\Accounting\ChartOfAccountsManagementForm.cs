using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Models;
using Awqaf_Managment.DataAccess;
using Awqaf_Managment.Services;
using Awqaf_Managment.UI.Controls;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج إدارة دليل الحسابات الرئيسي
    /// Main Chart of Accounts Management Form
    /// </summary>
    public partial class ChartOfAccountsManagementForm : Form
    {
        #region الحقول الخاصة - Private Fields
        
        private readonly ChartOfAccountsDataAccess _accountsDataAccess;
        private readonly PersonalInformationDataAccess _personalInfoDataAccess;
        private readonly AccountTypeDataAccess _accountTypeDataAccess;
        private readonly AccountGroupDataAccess _accountGroupDataAccess;
        private readonly CurrencyDataAccess _currencyDataAccess;
        private readonly ValidationService _validationService;
        private readonly BusinessRulesService _businessRulesService;

        private List<ChartOfAccount> _allAccounts;
        private List<AccountType> _accountTypes;
        private List<AccountGroup> _accountGroups;
        private List<Currency> _currencies;
        private List<PersonalInformation> _personalInfoList;

        private ChartOfAccount _currentAccount;
        private bool _isEditMode = false;
        private bool _isDataLoading = false;

        #endregion

        #region المنشئ - Constructor
        
        /// <summary>
        /// منشئ النموذج
        /// Form Constructor
        /// </summary>
        public ChartOfAccountsManagementForm()
        {
            InitializeComponent();
            
            // تهيئة طبقات الوصول للبيانات
            _accountsDataAccess = new ChartOfAccountsDataAccess();
            _personalInfoDataAccess = new PersonalInformationDataAccess();
            _accountTypeDataAccess = new AccountTypeDataAccess();
            _accountGroupDataAccess = new AccountGroupDataAccess();
            _currencyDataAccess = new CurrencyDataAccess();
            
            // تهيئة الخدمات
            _validationService = new ValidationService();
            _businessRulesService = new BusinessRulesService();

            // تهيئة القوائم
            _allAccounts = new List<ChartOfAccount>();
            _accountTypes = new List<AccountType>();
            _accountGroups = new List<AccountGroup>();
            _currencies = new List<Currency>();
            _personalInfoList = new List<PersonalInformation>();

            // إعداد النموذج
            SetupForm();
        }

        #endregion

        #region إعداد النموذج - Form Setup
        
        /// <summary>
        /// إعداد النموذج الأولي
        /// Initial Form Setup
        /// </summary>
        private void SetupForm()
        {
            // إعداد الألوان والخطوط
            ApplyModernStyling();
            
            // إعداد التلميحات
            SetupToolTips();
            
            // إعداد القوائم المنسدلة
            SetupComboBoxes();
            
            // إعداد التحقق من الأخطاء
            SetupValidation();
            
            // تعطيل الأزرار في البداية
            SetButtonsState(false);
        }

        /// <summary>
        /// تطبيق التصميم العصري
        /// Apply Modern Styling
        /// </summary>
        private void ApplyModernStyling()
        {
            // ألوان النموذج
            this.BackColor = Color.FromArgb(248, 249, 250);
            
            // ألوان شريط الأدوات
            toolStripMain.BackColor = Color.FromArgb(52, 152, 219);
            toolStripMain.ForeColor = Color.White;
            
            // ألوان شريط الحالة
            statusStripMain.BackColor = Color.FromArgb(236, 240, 241);
            statusStripMain.ForeColor = Color.FromArgb(44, 62, 80);
            
            // ألوان المجموعات
            groupBoxAccountsTree.ForeColor = Color.FromArgb(44, 62, 80);
            groupBoxAccountDetails.ForeColor = Color.FromArgb(44, 62, 80);
            
            // خط النموذج
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
        }

        /// <summary>
        /// إعداد التلميحات
        /// Setup ToolTips
        /// </summary>
        private void SetupToolTips()
        {
            toolTip.SetToolTip(textBoxAccountCode, "رمز الحساب الفريد");
            toolTip.SetToolTip(textBoxAccountNameAr, "اسم الحساب باللغة العربية (مطلوب)");
            toolTip.SetToolTip(textBoxAccountNameEn, "اسم الحساب باللغة الإنجليزية (اختياري)");
            toolTip.SetToolTip(comboBoxAccountType, "نوع الحساب (أصول، خصوم، إيرادات، مصروفات)");
            toolTip.SetToolTip(comboBoxAccountGroup, "مجموعة الحساب الفرعية");
            toolTip.SetToolTip(comboBoxParentAccount, "الحساب الأب في الهيكل الهرمي");
            toolTip.SetToolTip(numericUpDownOpeningBalance, "الرصيد الافتتاحي للحساب");
            toolTip.SetToolTip(comboBoxNature, "طبيعة الحساب (مدين أو دائن)");
            toolTip.SetToolTip(buttonGenerateCode, "توليد رمز الحساب تلقائياً");
        }

        /// <summary>
        /// إعداد القوائم المنسدلة
        /// Setup ComboBoxes
        /// </summary>
        private void SetupComboBoxes()
        {
            // قائمة طبيعة الحساب
            comboBoxNature.Items.Clear();
            comboBoxNature.Items.AddRange(new[] { "مدين", "دائن" });
            comboBoxNature.SelectedIndex = 0;

            // إعداد خصائص القوائم
            comboBoxAccountType.DisplayMember = "TypeNameAr";
            comboBoxAccountType.ValueMember = "AccountTypeId";
            
            comboBoxAccountGroup.DisplayMember = "GroupNameAr";
            comboBoxAccountGroup.ValueMember = "AccountGroupId";
            
            comboBoxParentAccount.DisplayMember = "FullAccountText";
            comboBoxParentAccount.ValueMember = "AccountId";
            
            comboBoxCurrency.DisplayMember = "CurrencyNameAr";
            comboBoxCurrency.ValueMember = "CurrencyId";
        }

        /// <summary>
        /// إعداد التحقق من الأخطاء
        /// Setup Validation
        /// </summary>
        private void SetupValidation()
        {
            errorProvider.ContainerControl = this;
            errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
            errorProvider.Icon = SystemIcons.Warning;
        }

        /// <summary>
        /// تعيين حالة الأزرار
        /// Set Buttons State
        /// </summary>
        /// <param name="hasSelectedAccount">هل يوجد حساب محدد</param>
        private void SetButtonsState(bool hasSelectedAccount)
        {
            toolStripButtonEdit.Enabled = hasSelectedAccount && !_isEditMode;
            toolStripButtonDelete.Enabled = hasSelectedAccount && !_isEditMode;
            toolStripButtonSave.Enabled = _isEditMode;
            toolStripButtonCancel.Enabled = _isEditMode;
            
            // تعطيل/تفعيل الحقول
            SetFieldsEnabled(_isEditMode);
        }

        /// <summary>
        /// تعيين حالة الحقول
        /// Set Fields Enabled State
        /// </summary>
        /// <param name="enabled">هل الحقول مفعلة</param>
        private void SetFieldsEnabled(bool enabled)
        {
            // الحقول الأساسية
            textBoxAccountCode.Enabled = enabled;
            textBoxAccountNameAr.Enabled = enabled;
            textBoxAccountNameEn.Enabled = enabled;
            comboBoxAccountType.Enabled = enabled;
            comboBoxAccountGroup.Enabled = enabled;
            comboBoxParentAccount.Enabled = enabled;
            comboBoxCurrency.Enabled = enabled;
            numericUpDownOpeningBalance.Enabled = enabled;
            comboBoxNature.Enabled = enabled;
            checkBoxIsActive.Enabled = enabled;
            checkBoxAllowPosting.Enabled = enabled;
            checkBoxAllowDirectEntry.Enabled = enabled;
            buttonGenerateCode.Enabled = enabled;

            // الحقول الشخصية
            textBoxFullNameAr.Enabled = enabled;
            textBoxFullNameEn.Enabled = enabled;
            textBoxNationalId.Enabled = enabled;
            textBoxEmail.Enabled = enabled;
            textBoxPhone.Enabled = enabled;
            textBoxMobile.Enabled = enabled;
            textBoxAddress.Enabled = enabled;
            textBoxCity.Enabled = enabled;
            buttonSelectPersonalInfo.Enabled = enabled;
            buttonClearPersonalInfo.Enabled = enabled;

            // الحقول المتقدمة
            textBoxDescription.Enabled = enabled;
            textBoxNotes.Enabled = enabled;
            textBoxTaxNumber.Enabled = enabled;
            textBoxCommercialRegister.Enabled = enabled;
            textBoxBankAccount.Enabled = enabled;
            textBoxIBAN.Enabled = enabled;
            checkBoxRequiresCostCenter.Enabled = enabled;
            checkBoxRequiresProject.Enabled = enabled;
            numericUpDownSortOrder.Enabled = enabled;
        }

        #endregion

        #region تحميل البيانات - Data Loading
        
        /// <summary>
        /// تحميل جميع البيانات المطلوبة
        /// Load All Required Data
        /// </summary>
        private async void LoadAllData()
        {
            try
            {
                _isDataLoading = true;
                SetStatusMessage("جاري تحميل البيانات...");
                ShowProgress(true);

                // تحميل البيانات الأساسية
                await LoadAccountTypes();
                await LoadAccountGroups();
                await LoadCurrencies();
                await LoadPersonalInformation();
                await LoadAccounts();

                SetStatusMessage("تم تحميل البيانات بنجاح");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تحميل البيانات: {ex.Message}");
                SetStatusMessage("خطأ في تحميل البيانات");
            }
            finally
            {
                _isDataLoading = false;
                ShowProgress(false);
            }
        }

        /// <summary>
        /// تحميل أنواع الحسابات
        /// Load Account Types
        /// </summary>
        private async System.Threading.Tasks.Task LoadAccountTypes()
        {
            _accountTypes = await System.Threading.Tasks.Task.Run(() => _accountTypeDataAccess.GetAllAccountTypes());
            comboBoxAccountType.DataSource = _accountTypes.ToList();
        }

        /// <summary>
        /// تحميل مجموعات الحسابات
        /// Load Account Groups
        /// </summary>
        private async System.Threading.Tasks.Task LoadAccountGroups()
        {
            _accountGroups = await System.Threading.Tasks.Task.Run(() => _accountGroupDataAccess.GetAllAccountGroups());
            comboBoxAccountGroup.DataSource = _accountGroups.ToList();
        }

        /// <summary>
        /// تحميل العملات
        /// Load Currencies
        /// </summary>
        private async System.Threading.Tasks.Task LoadCurrencies()
        {
            _currencies = await System.Threading.Tasks.Task.Run(() => _currencyDataAccess.GetAllCurrencies());
            comboBoxCurrency.DataSource = _currencies.ToList();
            
            // تحديد العملة الافتراضية (الريال السعودي)
            var defaultCurrency = _currencies.FirstOrDefault(c => c.CurrencyCode == "SAR");
            if (defaultCurrency != null)
            {
                comboBoxCurrency.SelectedValue = defaultCurrency.CurrencyId;
            }
        }

        /// <summary>
        /// تحميل البيانات الشخصية
        /// Load Personal Information
        /// </summary>
        private async System.Threading.Tasks.Task LoadPersonalInformation()
        {
            _personalInfoList = await System.Threading.Tasks.Task.Run(() => _personalInfoDataAccess.GetAllPersonalInformation());
        }

        /// <summary>
        /// تحميل الحسابات
        /// Load Accounts
        /// </summary>
        private async System.Threading.Tasks.Task LoadAccounts()
        {
            _allAccounts = await System.Threading.Tasks.Task.Run(() => _accountsDataAccess.GetAllAccounts());
            
            // تحديث شجرة الحسابات
            accountsTreeView.AllAccounts = _allAccounts;
            
            // تحديث قائمة الحسابات الأب
            UpdateParentAccountsList();
        }

        /// <summary>
        /// تحديث قائمة الحسابات الأب
        /// Update Parent Accounts List
        /// </summary>
        private void UpdateParentAccountsList()
        {
            var parentAccounts = _allAccounts.Where(a => a.IsParent || a.AccountLevel == 1).ToList();
            parentAccounts.Insert(0, new ChartOfAccount { AccountId = 0, AccountCode = "", AccountNameAr = "-- لا يوجد --" });
            comboBoxParentAccount.DataSource = parentAccounts;
        }

        #endregion

        #region معالجات الأحداث - Event Handlers

        /// <summary>
        /// حدث تحميل النموذج
        /// Form Load Event
        /// </summary>
        private void ChartOfAccountsManagementForm_Load(object sender, EventArgs e)
        {
            LoadAllData();
        }

        /// <summary>
        /// حدث النقر على زر جديد
        /// New Button Click Event
        /// </summary>
        private void ToolStripButtonNew_Click(object sender, EventArgs e)
        {
            StartNewAccount();
        }

        /// <summary>
        /// حدث النقر على زر تعديل
        /// Edit Button Click Event
        /// </summary>
        private void ToolStripButtonEdit_Click(object sender, EventArgs e)
        {
            if (_currentAccount != null)
            {
                StartEditAccount();
            }
        }

        /// <summary>
        /// حدث النقر على زر حفظ
        /// Save Button Click Event
        /// </summary>
        private void ToolStripButtonSave_Click(object sender, EventArgs e)
        {
            SaveAccount();
        }

        /// <summary>
        /// حدث النقر على زر حذف
        /// Delete Button Click Event
        /// </summary>
        private void ToolStripButtonDelete_Click(object sender, EventArgs e)
        {
            DeleteAccount();
        }

        /// <summary>
        /// حدث النقر على زر إلغاء
        /// Cancel Button Click Event
        /// </summary>
        private void ToolStripButtonCancel_Click(object sender, EventArgs e)
        {
            CancelEdit();
        }

        /// <summary>
        /// حدث النقر على زر تحديث
        /// Refresh Button Click Event
        /// </summary>
        private void ToolStripButtonRefresh_Click(object sender, EventArgs e)
        {
            LoadAllData();
        }

        /// <summary>
        /// حدث النقر على زر تصدير
        /// Export Button Click Event
        /// </summary>
        private void ToolStripButtonExport_Click(object sender, EventArgs e)
        {
            ExportAccounts();
        }

        /// <summary>
        /// حدث النقر على زر طباعة
        /// Print Button Click Event
        /// </summary>
        private void ToolStripButtonPrint_Click(object sender, EventArgs e)
        {
            PrintAccounts();
        }

        /// <summary>
        /// حدث تحديد حساب من الشجرة
        /// Account Selected from Tree Event
        /// </summary>
        private void AccountsTreeView_AccountSelected(object sender, AccountSelectedEventArgs e)
        {
            if (!_isDataLoading && e.Account != null)
            {
                LoadAccountToForm(e.Account);
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على حساب
        /// Account Double Clicked Event
        /// </summary>
        private void AccountsTreeView_AccountDoubleClicked(object sender, AccountSelectedEventArgs e)
        {
            if (!_isEditMode && e.Account != null)
            {
                StartEditAccount();
            }
        }

        /// <summary>
        /// حدث النقر بالزر الأيمن على حساب
        /// Account Right Clicked Event
        /// </summary>
        private void AccountsTreeView_AccountRightClicked(object sender, AccountSelectedEventArgs e)
        {
            // يمكن إضافة قائمة سياقية هنا
        }

        /// <summary>
        /// حدث تغيير نوع الحساب
        /// Account Type Changed Event
        /// </summary>
        private void ComboBoxAccountType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!_isDataLoading && comboBoxAccountType.SelectedValue != null)
            {
                UpdateAccountGroupsByType();
                UpdateAccountNatureByType();
            }
        }

        /// <summary>
        /// حدث تغيير رمز الحساب
        /// Account Code Changed Event
        /// </summary>
        private void TextBoxAccountCode_TextChanged(object sender, EventArgs e)
        {
            ValidateAccountCode();
        }

        /// <summary>
        /// حدث تغيير اسم الحساب
        /// Account Name Changed Event
        /// </summary>
        private void TextBoxAccountNameAr_TextChanged(object sender, EventArgs e)
        {
            ValidateAccountName();
        }

        /// <summary>
        /// حدث النقر على زر توليد الرمز
        /// Generate Code Button Click Event
        /// </summary>
        private void ButtonGenerateCode_Click(object sender, EventArgs e)
        {
            GenerateAccountCode();
        }

        #endregion

        #region العمليات الأساسية - Basic Operations

        /// <summary>
        /// بدء إنشاء حساب جديد
        /// Start New Account
        /// </summary>
        private void StartNewAccount()
        {
            _currentAccount = new ChartOfAccount();
            _isEditMode = true;

            ClearForm();
            SetDefaultValues();
            SetButtonsState(false);

            textBoxAccountNameAr.Focus();
            SetStatusMessage("وضع إنشاء حساب جديد");
        }

        /// <summary>
        /// بدء تعديل حساب
        /// Start Edit Account
        /// </summary>
        private void StartEditAccount()
        {
            if (_currentAccount == null) return;

            _isEditMode = true;
            SetButtonsState(true);

            textBoxAccountNameAr.Focus();
            SetStatusMessage($"وضع تعديل الحساب: {_currentAccount.AccountCode}");
        }

        /// <summary>
        /// حفظ الحساب
        /// Save Account
        /// </summary>
        private void SaveAccount()
        {
            try
            {
                if (!ValidateForm()) return;

                // تحديث بيانات الحساب من النموذج
                UpdateAccountFromForm();

                // التحقق من القواعد المحاسبية
                var businessRulesErrors = _businessRulesService.ValidateAccountBusinessRules(_currentAccount);
                if (businessRulesErrors.Any())
                {
                    ShowErrorMessage("أخطاء في القواعد المحاسبية:\n" + string.Join("\n", businessRulesErrors));
                    return;
                }

                // حفظ الحساب
                var accountId = _accountsDataAccess.SaveAccount(_currentAccount, GetCurrentUserId());

                if (accountId > 0)
                {
                    _currentAccount.AccountId = accountId;
                    ShowSuccessMessage("تم حفظ الحساب بنجاح");

                    _isEditMode = false;
                    SetButtonsState(true);

                    // إعادة تحميل البيانات
                    LoadAllData();

                    SetStatusMessage("تم حفظ الحساب بنجاح");
                }
                else
                {
                    ShowErrorMessage("فشل في حفظ الحساب");
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في حفظ الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف الحساب
        /// Delete Account
        /// </summary>
        private void DeleteAccount()
        {
            if (_currentAccount == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الحساب '{_currentAccount.AccountNameAr}'؟\n\nتحذير: هذه العملية لا يمكن التراجع عنها!",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button2,
                MessageBoxOptions.RtlReading);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var success = _accountsDataAccess.DeleteAccount(_currentAccount.AccountId, GetCurrentUserId());

                    if (success)
                    {
                        ShowSuccessMessage("تم حذف الحساب بنجاح");
                        ClearForm();
                        LoadAllData();
                        SetStatusMessage("تم حذف الحساب بنجاح");
                    }
                    else
                    {
                        ShowErrorMessage("فشل في حذف الحساب");
                    }
                }
                catch (Exception ex)
                {
                    ShowErrorMessage($"خطأ في حذف الحساب: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// إلغاء التعديل
        /// Cancel Edit
        /// </summary>
        private void CancelEdit()
        {
            _isEditMode = false;

            if (_currentAccount != null && _currentAccount.AccountId > 0)
            {
                // إعادة تحميل بيانات الحساب
                LoadAccountToForm(_currentAccount);
                SetButtonsState(true);
            }
            else
            {
                // مسح النموذج
                ClearForm();
                SetButtonsState(false);
            }

            SetStatusMessage("تم إلغاء التعديل");
        }

        #endregion

        #region الطرق المساعدة - Helper Methods

        /// <summary>
        /// تحميل بيانات الحساب إلى النموذج
        /// Load Account Data to Form
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        private void LoadAccountToForm(ChartOfAccount account)
        {
            if (account == null) return;

            _isDataLoading = true;
            _currentAccount = account;

            try
            {
                // المعلومات الأساسية
                textBoxAccountCode.Text = account.AccountCode;
                textBoxAccountNameAr.Text = account.AccountNameAr;
                textBoxAccountNameEn.Text = account.AccountNameEn ?? "";

                if (account.AccountTypeId > 0)
                    comboBoxAccountType.SelectedValue = account.AccountTypeId;

                if (account.AccountGroupId > 0)
                    comboBoxAccountGroup.SelectedValue = account.AccountGroupId;

                if (account.ParentAccountId.HasValue)
                    comboBoxParentAccount.SelectedValue = account.ParentAccountId.Value;
                else
                    comboBoxParentAccount.SelectedValue = 0;

                if (account.CurrencyId.HasValue)
                    comboBoxCurrency.SelectedValue = account.CurrencyId.Value;

                numericUpDownOpeningBalance.Value = account.OpeningBalance;
                comboBoxNature.Text = account.Nature ?? "مدين";

                checkBoxIsActive.Checked = account.IsActive;
                checkBoxAllowPosting.Checked = account.AllowPosting;
                checkBoxAllowDirectEntry.Checked = account.AllowDirectEntry;

                // المعلومات المتقدمة
                textBoxDescription.Text = account.Description ?? "";
                textBoxNotes.Text = account.Notes ?? "";
                textBoxTaxNumber.Text = account.TaxNumber ?? "";
                textBoxCommercialRegister.Text = account.CommercialRegister ?? "";
                textBoxBankAccount.Text = account.BankAccount ?? "";
                textBoxIBAN.Text = account.IBAN ?? "";
                checkBoxRequiresCostCenter.Checked = account.RequiresCostCenter;
                checkBoxRequiresProject.Checked = account.RequiresProject;
                numericUpDownSortOrder.Value = account.SortOrder;

                // البيانات الشخصية
                LoadPersonalInfoToForm(account.PersonalInfoId);

                SetButtonsState(true);
            }
            finally
            {
                _isDataLoading = false;
            }
        }

        /// <summary>
        /// تحديث بيانات الحساب من النموذج
        /// Update Account Data from Form
        /// </summary>
        private void UpdateAccountFromForm()
        {
            if (_currentAccount == null) return;

            _currentAccount.AccountCode = textBoxAccountCode.Text.Trim();
            _currentAccount.AccountNameAr = textBoxAccountNameAr.Text.Trim();
            _currentAccount.AccountNameEn = textBoxAccountNameEn.Text.Trim();
            _currentAccount.AccountTypeId = (int)(comboBoxAccountType.SelectedValue ?? 0);
            _currentAccount.AccountGroupId = (int)(comboBoxAccountGroup.SelectedValue ?? 0);

            var parentId = (int)(comboBoxParentAccount.SelectedValue ?? 0);
            _currentAccount.ParentAccountId = parentId > 0 ? parentId : (int?)null;

            _currentAccount.CurrencyId = (int)(comboBoxCurrency.SelectedValue ?? 1);
            _currentAccount.OpeningBalance = numericUpDownOpeningBalance.Value;
            _currentAccount.Nature = comboBoxNature.Text;
            _currentAccount.IsActive = checkBoxIsActive.Checked;
            _currentAccount.AllowPosting = checkBoxAllowPosting.Checked;
            _currentAccount.AllowDirectEntry = checkBoxAllowDirectEntry.Checked;
            _currentAccount.Description = textBoxDescription.Text.Trim();
            _currentAccount.Notes = textBoxNotes.Text.Trim();
            _currentAccount.TaxNumber = textBoxTaxNumber.Text.Trim();
            _currentAccount.CommercialRegister = textBoxCommercialRegister.Text.Trim();
            _currentAccount.BankAccount = textBoxBankAccount.Text.Trim();
            _currentAccount.IBAN = textBoxIBAN.Text.Trim();
            _currentAccount.RequiresCostCenter = checkBoxRequiresCostCenter.Checked;
            _currentAccount.RequiresProject = checkBoxRequiresProject.Checked;
            _currentAccount.SortOrder = (int)numericUpDownSortOrder.Value;

            // حساب المستوى والمسار
            CalculateAccountLevel();
        }

        /// <summary>
        /// مسح النموذج
        /// Clear Form
        /// </summary>
        private void ClearForm()
        {
            _isDataLoading = true;

            try
            {
                textBoxAccountCode.Clear();
                textBoxAccountNameAr.Clear();
                textBoxAccountNameEn.Clear();
                comboBoxAccountType.SelectedIndex = -1;
                comboBoxAccountGroup.SelectedIndex = -1;
                comboBoxParentAccount.SelectedValue = 0;
                comboBoxCurrency.SelectedValue = _currencies.FirstOrDefault(c => c.CurrencyCode == "SAR")?.CurrencyId ?? 1;
                numericUpDownOpeningBalance.Value = 0;
                comboBoxNature.SelectedIndex = 0;
                checkBoxIsActive.Checked = true;
                checkBoxAllowPosting.Checked = true;
                checkBoxAllowDirectEntry.Checked = true;
                textBoxDescription.Clear();
                textBoxNotes.Clear();
                textBoxTaxNumber.Clear();
                textBoxCommercialRegister.Clear();
                textBoxBankAccount.Clear();
                textBoxIBAN.Clear();
                checkBoxRequiresCostCenter.Checked = false;
                checkBoxRequiresProject.Checked = false;
                numericUpDownSortOrder.Value = 0;

                ClearPersonalInfoForm();
                ClearValidationErrors();
            }
            finally
            {
                _isDataLoading = false;
            }
        }

        /// <summary>
        /// تعيين القيم الافتراضية
        /// Set Default Values
        /// </summary>
        private void SetDefaultValues()
        {
            checkBoxIsActive.Checked = true;
            checkBoxAllowPosting.Checked = true;
            checkBoxAllowDirectEntry.Checked = true;
            comboBoxNature.SelectedIndex = 0;

            var defaultCurrency = _currencies.FirstOrDefault(c => c.CurrencyCode == "SAR");
            if (defaultCurrency != null)
            {
                comboBoxCurrency.SelectedValue = defaultCurrency.CurrencyId;
            }
        }

        /// <summary>
        /// التحقق من صحة النموذج
        /// Validate Form
        /// </summary>
        /// <returns>هل النموذج صحيح</returns>
        private bool ValidateForm()
        {
            ClearValidationErrors();
            bool isValid = true;

            // التحقق من رمز الحساب
            if (string.IsNullOrWhiteSpace(textBoxAccountCode.Text))
            {
                SetValidationError(textBoxAccountCode, "رمز الحساب مطلوب");
                isValid = false;
            }

            // التحقق من اسم الحساب
            if (string.IsNullOrWhiteSpace(textBoxAccountNameAr.Text))
            {
                SetValidationError(textBoxAccountNameAr, "اسم الحساب بالعربية مطلوب");
                isValid = false;
            }

            // التحقق من نوع الحساب
            if (comboBoxAccountType.SelectedValue == null)
            {
                SetValidationError(comboBoxAccountType, "نوع الحساب مطلوب");
                isValid = false;
            }

            // التحقق من مجموعة الحساب
            if (comboBoxAccountGroup.SelectedValue == null)
            {
                SetValidationError(comboBoxAccountGroup, "مجموعة الحساب مطلوبة");
                isValid = false;
            }

            return isValid;
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// Show Error Message
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// عرض رسالة نجاح
        /// Show Success Message
        /// </summary>
        /// <param name="message">رسالة النجاح</param>
        private void ShowSuccessMessage(string message)
        {
            MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// تعيين رسالة الحالة
        /// Set Status Message
        /// </summary>
        /// <param name="message">الرسالة</param>
        private void SetStatusMessage(string message)
        {
            toolStripStatusLabelMain.Text = message;
        }

        /// <summary>
        /// إظهار/إخفاء شريط التقدم
        /// Show/Hide Progress Bar
        /// </summary>
        /// <param name="show">إظهار أم إخفاء</param>
        private void ShowProgress(bool show)
        {
            toolStripProgressBar.Visible = show;
            if (show)
            {
                toolStripProgressBar.Style = ProgressBarStyle.Marquee;
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي
        /// Get Current User ID
        /// </summary>
        /// <returns>معرف المستخدم</returns>
        private int GetCurrentUserId()
        {
            // يجب ربطها بنظام المصادقة
            return 1; // مؤقت
        }

        // طرق مساعدة إضافية سيتم إضافتها لاحقاً...
        private void LoadPersonalInfoToForm(int? personalInfoId) { }
        private void ClearPersonalInfoForm() { }
        private void UpdateAccountGroupsByType() { }
        private void UpdateAccountNatureByType() { }
        private void ValidateAccountCode() { }
        private void ValidateAccountName() { }
        private void GenerateAccountCode() { }
        private void CalculateAccountLevel() { }
        private void ClearValidationErrors() { }
        private void SetValidationError(Control control, string message) { }
        private void ExportAccounts() { }
        private void PrintAccounts() { }

        #endregion
    }
}
