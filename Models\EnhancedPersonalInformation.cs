using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج البيانات الشخصية المحسن والشامل
    /// Enhanced and Comprehensive Personal Information Model
    /// </summary>
    public class EnhancedPersonalInformation
    {
        #region المعرف الأساسي - Primary Identifier
        
        /// <summary>
        /// معرف البيانات الشخصية
        /// Personal Information ID
        /// </summary>
        public int PersonalInfoId { get; set; }

        #endregion

        #region الأسماء - Names
        
        /// <summary>
        /// الاسم الكامل بالعربية
        /// Full Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل بالعربية مطلوب")]
        [StringLength(200, ErrorMessage = "الاسم الكامل يجب ألا يزيد عن 200 حرف")]
        public string FullNameAr { get; set; }

        /// <summary>
        /// الاسم الكامل بالإنجليزية
        /// Full Name in English
        /// </summary>
        [StringLength(200, ErrorMessage = "الاسم الكامل بالإنجليزية يجب ألا يزيد عن 200 حرف")]
        public string FullNameEn { get; set; }

        /// <summary>
        /// الاسم الأول بالعربية
        /// First Name in Arabic
        /// </summary>
        [StringLength(100, ErrorMessage = "الاسم الأول يجب ألا يزيد عن 100 حرف")]
        public string FirstNameAr { get; set; }

        /// <summary>
        /// اسم العائلة بالعربية
        /// Last Name in Arabic
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم العائلة يجب ألا يزيد عن 100 حرف")]
        public string LastNameAr { get; set; }

        /// <summary>
        /// اسم الأب بالعربية
        /// Father Name in Arabic
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الأب يجب ألا يزيد عن 100 حرف")]
        public string FatherNameAr { get; set; }

        /// <summary>
        /// اسم الجد بالعربية
        /// Grandfather Name in Arabic
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الجد يجب ألا يزيد عن 100 حرف")]
        public string GrandFatherNameAr { get; set; }

        #endregion

        #region معلومات الهوية - Identity Information
        
        /// <summary>
        /// رقم الهوية الوطنية
        /// National ID Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهوية يجب ألا يزيد عن 20 حرف")]
        public string NationalId { get; set; }

        /// <summary>
        /// رقم جواز السفر
        /// Passport Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم جواز السفر يجب ألا يزيد عن 20 حرف")]
        public string PassportNumber { get; set; }

        /// <summary>
        /// نوع الهوية
        /// ID Type
        /// </summary>
        [StringLength(20, ErrorMessage = "نوع الهوية يجب ألا يزيد عن 20 حرف")]
        public string IdType { get; set; } = "هوية وطنية";

        /// <summary>
        /// تاريخ انتهاء الهوية
        /// ID Expiry Date
        /// </summary>
        public DateTime? IdExpiryDate { get; set; }

        #endregion

        #region معلومات الاتصال - Contact Information
        
        /// <summary>
        /// البريد الإلكتروني
        /// Email Address
        /// </summary>
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب ألا يزيد عن 100 حرف")]
        public string Email { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// Phone Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب ألا يزيد عن 20 حرف")]
        public string Phone { get; set; }

        /// <summary>
        /// رقم الجوال
        /// Mobile Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الجوال يجب ألا يزيد عن 20 حرف")]
        public string Mobile { get; set; }

        /// <summary>
        /// رقم الفاكس
        /// Fax Number
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الفاكس يجب ألا يزيد عن 20 حرف")]
        public string Fax { get; set; }

        /// <summary>
        /// الموقع الإلكتروني
        /// Website
        /// </summary>
        [Url(ErrorMessage = "الموقع الإلكتروني غير صحيح")]
        [StringLength(200, ErrorMessage = "الموقع الإلكتروني يجب ألا يزيد عن 200 حرف")]
        public string Website { get; set; }

        #endregion

        #region العنوان - Address Information
        
        /// <summary>
        /// العنوان بالعربية
        /// Address in Arabic
        /// </summary>
        [StringLength(500, ErrorMessage = "العنوان يجب ألا يزيد عن 500 حرف")]
        public string AddressAr { get; set; }

        /// <summary>
        /// العنوان بالإنجليزية
        /// Address in English
        /// </summary>
        [StringLength(500, ErrorMessage = "العنوان بالإنجليزية يجب ألا يزيد عن 500 حرف")]
        public string AddressEn { get; set; }

        /// <summary>
        /// المدينة
        /// City
        /// </summary>
        [StringLength(100, ErrorMessage = "المدينة يجب ألا تزيد عن 100 حرف")]
        public string City { get; set; }

        /// <summary>
        /// المنطقة
        /// Region
        /// </summary>
        [StringLength(100, ErrorMessage = "المنطقة يجب ألا تزيد عن 100 حرف")]
        public string Region { get; set; }

        /// <summary>
        /// الدولة
        /// Country
        /// </summary>
        [StringLength(100, ErrorMessage = "الدولة يجب ألا تزيد عن 100 حرف")]
        public string Country { get; set; } = "المملكة العربية السعودية";

        /// <summary>
        /// الرمز البريدي
        /// Postal Code
        /// </summary>
        [StringLength(10, ErrorMessage = "الرمز البريدي يجب ألا يزيد عن 10 أحرف")]
        public string PostalCode { get; set; }

        /// <summary>
        /// صندوق البريد
        /// PO Box
        /// </summary>
        [StringLength(20, ErrorMessage = "صندوق البريد يجب ألا يزيد عن 20 حرف")]
        public string POBox { get; set; }

        #endregion

        #region المعلومات المالية - Financial Information
        
        /// <summary>
        /// الرقم الضريبي
        /// Tax Number
        /// </summary>
        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب ألا يزيد عن 50 حرف")]
        public string TaxNumber { get; set; }

        /// <summary>
        /// رقم السجل التجاري
        /// Commercial Register Number
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم السجل التجاري يجب ألا يزيد عن 50 حرف")]
        public string CommercialRegister { get; set; }

        /// <summary>
        /// تاريخ السجل التجاري
        /// Commercial Register Date
        /// </summary>
        public DateTime? CommercialRegisterDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء السجل التجاري
        /// Commercial Register Expiry
        /// </summary>
        public DateTime? CommercialRegisterExpiry { get; set; }

        #endregion

        #region المعلومات البنكية - Banking Information
        
        /// <summary>
        /// اسم البنك
        /// Bank Name
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم البنك يجب ألا يزيد عن 100 حرف")]
        public string BankName { get; set; }

        /// <summary>
        /// رقم الحساب البنكي
        /// Bank Account Number
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم الحساب البنكي يجب ألا يزيد عن 50 حرف")]
        public string BankAccountNumber { get; set; }

        /// <summary>
        /// رقم الآيبان
        /// IBAN Number
        /// </summary>
        [StringLength(34, ErrorMessage = "رقم الآيبان يجب ألا يزيد عن 34 حرف")]
        public string IBAN { get; set; }

        /// <summary>
        /// رمز السويفت
        /// Swift Code
        /// </summary>
        [StringLength(20, ErrorMessage = "رمز السويفت يجب ألا يزيد عن 20 حرف")]
        public string SwiftCode { get; set; }

        #endregion

        #region المعلومات الإضافية - Additional Information
        
        /// <summary>
        /// نوع الشخص (فرد، شركة، مؤسسة)
        /// Person Type
        /// </summary>
        [StringLength(20, ErrorMessage = "نوع الشخص يجب ألا يزيد عن 20 حرف")]
        public string PersonType { get; set; } = "فرد";

        /// <summary>
        /// الجنس
        /// Gender
        /// </summary>
        [StringLength(10, ErrorMessage = "الجنس يجب ألا يزيد عن 10 أحرف")]
        public string Gender { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// Birth Date
        /// </summary>
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// الجنسية
        /// Nationality
        /// </summary>
        [StringLength(50, ErrorMessage = "الجنسية يجب ألا تزيد عن 50 حرف")]
        public string Nationality { get; set; }

        /// <summary>
        /// الحالة الاجتماعية
        /// Marital Status
        /// </summary>
        [StringLength(20, ErrorMessage = "الحالة الاجتماعية يجب ألا تزيد عن 20 حرف")]
        public string MaritalStatus { get; set; }

        #endregion

        #region الملاحظات والتعليمات - Notes and Instructions

        /// <summary>
        /// ملاحظات عامة
        /// General Notes
        /// </summary>
        [StringLength(2000, ErrorMessage = "الملاحظات يجب ألا تزيد عن 2000 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// تعليمات خاصة
        /// Special Instructions
        /// </summary>
        [StringLength(1000, ErrorMessage = "التعليمات الخاصة يجب ألا تزيد عن 1000 حرف")]
        public string SpecialInstructions { get; set; }

        #endregion

        #region حالة السجل - Record Status

        /// <summary>
        /// هل السجل نشط
        /// Is Record Active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل البيانات محققة
        /// Is Data Verified
        /// </summary>
        public bool IsVerified { get; set; } = false;

        #endregion

        #region معلومات التدقيق - Audit Information

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// معرف منشئ السجل
        /// Created By User ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// اسم منشئ السجل
        /// Created By User Name
        /// </summary>
        public string CreatedByName { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف معدل السجل
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }

        /// <summary>
        /// اسم معدل السجل
        /// Modified By User Name
        /// </summary>
        public string ModifiedByName { get; set; }

        #endregion

        #region خصائص محسوبة - Calculated Properties

        /// <summary>
        /// العمر المحسوب
        /// Calculated Age
        /// </summary>
        public int? Age
        {
            get
            {
                if (!BirthDate.HasValue) return null;
                var today = DateTime.Today;
                var age = today.Year - BirthDate.Value.Year;
                if (BirthDate.Value.Date > today.AddYears(-age)) age--;
                return age;
            }
        }

        /// <summary>
        /// النص الكامل للاسم مع التفاصيل
        /// Full Name with Details
        /// </summary>
        public string FullNameWithDetails
        {
            get
            {
                var parts = new List<string>();
                if (!string.IsNullOrEmpty(FirstNameAr)) parts.Add(FirstNameAr);
                if (!string.IsNullOrEmpty(FatherNameAr)) parts.Add(FatherNameAr);
                if (!string.IsNullOrEmpty(GrandFatherNameAr)) parts.Add(GrandFatherNameAr);
                if (!string.IsNullOrEmpty(LastNameAr)) parts.Add(LastNameAr);

                return parts.Count > 0 ? string.Join(" ", parts) : FullNameAr;
            }
        }

        /// <summary>
        /// معلومات الاتصال الأساسية
        /// Primary Contact Info
        /// </summary>
        public string PrimaryContactInfo
        {
            get
            {
                if (!string.IsNullOrEmpty(Mobile)) return Mobile;
                if (!string.IsNullOrEmpty(Phone)) return Phone;
                if (!string.IsNullOrEmpty(Email)) return Email;
                return "غير محدد";
            }
        }

        /// <summary>
        /// حالة السجل كنص
        /// Record Status as Text
        /// </summary>
        public string StatusText => IsActive ? "نشط" : "غير نشط";

        /// <summary>
        /// حالة التحقق كنص
        /// Verification Status as Text
        /// </summary>
        public string VerificationText => IsVerified ? "محقق" : "غير محقق";

        #endregion

        #region التحقق من صحة البيانات - Data Validation

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        /// <returns>قائمة بالأخطاء إن وجدت</returns>
        public List<string> ValidateData()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(FullNameAr))
                errors.Add("الاسم الكامل بالعربية مطلوب");

            if (!string.IsNullOrEmpty(Email) && !IsValidEmail(Email))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (!string.IsNullOrEmpty(Website) && !IsValidUrl(Website))
                errors.Add("الموقع الإلكتروني غير صحيح");

            if (BirthDate.HasValue && BirthDate.Value > DateTime.Today)
                errors.Add("تاريخ الميلاد لا يمكن أن يكون في المستقبل");

            if (IdExpiryDate.HasValue && IdExpiryDate.Value < DateTime.Today)
                errors.Add("تاريخ انتهاء الهوية منتهي الصلاحية");

            if (CommercialRegisterExpiry.HasValue && CommercialRegisterExpiry.Value < DateTime.Today)
                errors.Add("تاريخ انتهاء السجل التجاري منتهي الصلاحية");

            return errors;
        }

        /// <summary>
        /// هل البيانات صحيحة
        /// Is Data Valid
        /// </summary>
        public bool IsValid => ValidateData().Count == 0;

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// Validate Email Address
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الرابط
        /// Validate URL
        /// </summary>
        private bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out Uri result) &&
                   (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }

        #endregion
    }
}
