using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج أنواع الحسابات
    /// Account Types Model
    /// </summary>
    public class AccountType
    {
        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        public int AccountTypeId { get; set; }

        /// <summary>
        /// رمز نوع الحساب
        /// Type Code
        /// </summary>
        [Required(ErrorMessage = "رمز نوع الحساب مطلوب")]
        [StringLength(10, ErrorMessage = "رمز نوع الحساب يجب ألا يزيد عن 10 أحرف")]
        public string TypeCode { get; set; }

        /// <summary>
        /// اسم نوع الحساب بالإنجليزية
        /// Type Name in English
        /// </summary>
        [Required(ErrorMessage = "اسم نوع الحساب بالإنجليزية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم نوع الحساب يجب ألا يزيد عن 100 حرف")]
        public string TypeName { get; set; }

        /// <summary>
        /// اسم نوع الحساب بالعربية
        /// Type Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "اسم نوع الحساب بالعربية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم نوع الحساب يجب ألا يزيد عن 100 حرف")]
        public string TypeNameAr { get; set; }

        /// <summary>
        /// وصف نوع الحساب بالإنجليزية
        /// Type Description in English
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف نوع الحساب يجب ألا يزيد عن 500 حرف")]
        public string TypeDescription { get; set; }

        /// <summary>
        /// وصف نوع الحساب بالعربية
        /// Type Description in Arabic
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف نوع الحساب يجب ألا يزيد عن 500 حرف")]
        public string TypeDescriptionAr { get; set; }

        /// <summary>
        /// طبيعة الرصيد (مدين/دائن)
        /// Normal Balance (Debit/Credit)
        /// </summary>
        [Required(ErrorMessage = "طبيعة الرصيد مطلوبة")]
        [RegularExpression("^(Debit|Credit)$", ErrorMessage = "طبيعة الرصيد يجب أن تكون Debit أو Credit")]
        public string NormalBalance { get; set; }

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// Display Order
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// منشئ السجل
        /// Created By
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل السجل
        /// Modified By
        /// </summary>
        [StringLength(100)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// الحصول على اسم نوع الحساب حسب اللغة
        /// Get type name based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>اسم نوع الحساب</returns>
        public string GetDisplayName(bool isArabic = true)
        {
            return isArabic ? TypeNameAr : TypeName;
        }

        /// <summary>
        /// الحصول على وصف نوع الحساب حسب اللغة
        /// Get type description based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>وصف نوع الحساب</returns>
        public string GetDisplayDescription(bool isArabic = true)
        {
            return isArabic ? TypeDescriptionAr : TypeDescription;
        }

        /// <summary>
        /// الحصول على طبيعة الرصيد بالعربية
        /// Get normal balance in Arabic
        /// </summary>
        /// <returns>طبيعة الرصيد بالعربية</returns>
        public string GetNormalBalanceArabic()
        {
            return NormalBalance switch
            {
                "Debit" => "مدين",
                "Credit" => "دائن",
                _ => NormalBalance
            };
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (string.IsNullOrWhiteSpace(TypeCode))
                return "رمز نوع الحساب مطلوب";

            if (string.IsNullOrWhiteSpace(TypeName))
                return "اسم نوع الحساب بالإنجليزية مطلوب";

            if (string.IsNullOrWhiteSpace(TypeNameAr))
                return "اسم نوع الحساب بالعربية مطلوب";

            if (string.IsNullOrWhiteSpace(NormalBalance))
                return "طبيعة الرصيد مطلوبة";

            if (NormalBalance != "Debit" && NormalBalance != "Credit")
                return "طبيعة الرصيد يجب أن تكون Debit أو Credit";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// نسخ البيانات من نموذج آخر
        /// Copy data from another model
        /// </summary>
        /// <param name="source">النموذج المصدر</param>
        public void CopyFrom(AccountType source)
        {
            if (source == null) return;

            TypeCode = source.TypeCode;
            TypeName = source.TypeName;
            TypeNameAr = source.TypeNameAr;
            TypeDescription = source.TypeDescription;
            TypeDescriptionAr = source.TypeDescriptionAr;
            NormalBalance = source.NormalBalance;
            IsActive = source.IsActive;
            DisplayOrder = source.DisplayOrder;
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return $"{TypeCode} - {TypeNameAr} ({TypeName})";
        }
    }
}
