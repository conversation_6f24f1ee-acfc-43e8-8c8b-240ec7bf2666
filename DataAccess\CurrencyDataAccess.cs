using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.DataAccess.Base;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بالعملات
    /// Currencies Data Access Layer
    /// </summary>
    public class CurrencyDataAccess : BaseDataAccess
    {
        #region Currency CRUD Operations

        /// <summary>
        /// الحصول على جميع العملات
        /// Get All Currencies
        /// </summary>
        /// <returns>قائمة العملات</returns>
        public List<Currency> GetAllCurrencies()
        {
            try
            {
                var currencies = new List<Currency>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAllCurrencies"))
                {
                    while (reader.Read())
                    {
                        currencies.Add(MapCurrencyFromReader(reader));
                    }
                }

                return currencies;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على العملات");
                throw new Exception(HandleException(ex, "الحصول على العملات"));
            }
        }

        /// <summary>
        /// الحصول على العملات النشطة
        /// Get Active Currencies
        /// </summary>
        /// <returns>قائمة العملات النشطة</returns>
        public List<Currency> GetActiveCurrencies()
        {
            try
            {
                var currencies = new List<Currency>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetActiveCurrencies"))
                {
                    while (reader.Read())
                    {
                        currencies.Add(MapCurrencyFromReader(reader));
                    }
                }

                return currencies;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على العملات النشطة");
                throw new Exception(HandleException(ex, "الحصول على العملات النشطة"));
            }
        }

        /// <summary>
        /// الحصول على عملة بالكود
        /// Get Currency by Code
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>بيانات العملة</returns>
        public Currency GetCurrencyByCode(string currencyCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currencyCode, SqlDbType.NVarChar, 3)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetCurrencyByCode", parameters))
                {
                    if (reader.Read())
                    {
                        return MapCurrencyFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على العملة بالكود");
                throw new Exception(HandleException(ex, "الحصول على العملة بالكود"));
            }
        }

        /// <summary>
        /// حفظ عملة جديدة أو تحديث موجودة
        /// Save New Currency or Update Existing
        /// </summary>
        /// <param name="currency">بيانات العملة</param>
        /// <returns>كود العملة المحفوظة</returns>
        public string SaveCurrency(Currency currency)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currency.CurrencyCode, SqlDbType.NVarChar, 3),
                    CreateParameter("@CurrencyName", currency.CurrencyName, SqlDbType.NVarChar, 100),
                    CreateParameter("@CurrencyNameAr", currency.CurrencyNameAr, SqlDbType.NVarChar, 100),
                    CreateParameter("@CurrencySymbol", currency.CurrencySymbol, SqlDbType.NVarChar, 10),
                    CreateParameter("@ExchangeRate", currency.ExchangeRate),
                    CreateParameter("@DecimalPlaces", currency.DecimalPlaces),
                    CreateParameter("@IsBaseCurrency", currency.IsBaseCurrency),
                    CreateParameter("@IsActive", currency.IsActive),
                    CreateParameter("@CreatedBy", currency.CreatedBy, SqlDbType.NVarChar, 100)
                };

                ExecuteStoredProcedure("SP_SaveCurrency", parameters);
                
                return currency.CurrencyCode;
            }
            catch (Exception ex)
            {
                LogError(ex, "حفظ العملة");
                throw new Exception(HandleException(ex, "حفظ العملة"));
            }
        }

        /// <summary>
        /// حذف عملة
        /// Delete Currency
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>هل تم الحذف بنجاح</returns>
        public bool DeleteCurrency(string currencyCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currencyCode, SqlDbType.NVarChar, 3)
                };

                int result = ExecuteStoredProcedure("SP_DeleteCurrency", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "حذف العملة");
                throw new Exception(HandleException(ex, "حذف العملة"));
            }
        }

        #endregion

        #region Currency Exchange Rates

        /// <summary>
        /// تحديث سعر صرف العملة
        /// Update Currency Exchange Rate
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <param name="newExchangeRate">سعر الصرف الجديد</param>
        /// <param name="updatedBy">المستخدم الذي قام بالتحديث</param>
        /// <returns>هل تم التحديث بنجاح</returns>
        public bool UpdateExchangeRate(string currencyCode, decimal newExchangeRate, string updatedBy)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currencyCode, SqlDbType.NVarChar, 3),
                    CreateParameter("@NewExchangeRate", newExchangeRate),
                    CreateParameter("@UpdatedBy", updatedBy, SqlDbType.NVarChar, 100)
                };

                int result = ExecuteStoredProcedure("SP_UpdateCurrencyExchangeRate", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "تحديث سعر صرف العملة");
                throw new Exception(HandleException(ex, "تحديث سعر صرف العملة"));
            }
        }

        /// <summary>
        /// الحصول على العملة الأساسية
        /// Get Base Currency
        /// </summary>
        /// <returns>بيانات العملة الأساسية</returns>
        public Currency GetBaseCurrency()
        {
            try
            {
                using (var reader = ExecuteStoredProcedureReader("SP_GetBaseCurrency"))
                {
                    if (reader.Read())
                    {
                        return MapCurrencyFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على العملة الأساسية");
                throw new Exception(HandleException(ex, "الحصول على العملة الأساسية"));
            }
        }

        /// <summary>
        /// تعيين عملة كعملة أساسية
        /// Set Currency as Base Currency
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <param name="updatedBy">المستخدم الذي قام بالتحديث</param>
        /// <returns>هل تم التحديث بنجاح</returns>
        public bool SetBaseCurrency(string currencyCode, string updatedBy)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currencyCode, SqlDbType.NVarChar, 3),
                    CreateParameter("@UpdatedBy", updatedBy, SqlDbType.NVarChar, 100)
                };

                int result = ExecuteStoredProcedure("SP_SetBaseCurrency", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "تعيين العملة الأساسية");
                throw new Exception(HandleException(ex, "تعيين العملة الأساسية"));
            }
        }

        #endregion

        #region Currency Validation

        /// <summary>
        /// التحقق من وجود كود العملة
        /// Check if Currency Code Exists
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>هل الكود موجود</returns>
        public bool IsCurrencyCodeExists(string currencyCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currencyCode, SqlDbType.NVarChar, 3)
                };

                var result = ExecuteStoredProcedureScalar("SP_CheckCurrencyCodeExists", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من كود العملة");
                throw new Exception(HandleException(ex, "التحقق من كود العملة"));
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف العملة
        /// Check if Currency Can Be Deleted
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>هل يمكن الحذف</returns>
        public bool CanDeleteCurrency(string currencyCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@CurrencyCode", currencyCode, SqlDbType.NVarChar, 3)
                };

                var result = ExecuteStoredProcedureScalar("SP_CanDeleteCurrency", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من إمكانية حذف العملة");
                throw new Exception(HandleException(ex, "التحقق من إمكانية حذف العملة"));
            }
        }

        #endregion

        #region Currency Conversion

        /// <summary>
        /// تحويل مبلغ من عملة إلى أخرى
        /// Convert Amount from One Currency to Another
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrency">العملة المصدر</param>
        /// <param name="toCurrency">العملة الهدف</param>
        /// <returns>المبلغ المحول</returns>
        public decimal ConvertCurrency(decimal amount, string fromCurrency, string toCurrency)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@Amount", amount),
                    CreateParameter("@FromCurrency", fromCurrency, SqlDbType.NVarChar, 3),
                    CreateParameter("@ToCurrency", toCurrency, SqlDbType.NVarChar, 3),
                    CreateOutputParameter("@ConvertedAmount", SqlDbType.Decimal)
                };

                ExecuteStoredProcedure("SP_ConvertCurrency", parameters);
                
                return ConvertFromDb<decimal>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "تحويل العملة");
                throw new Exception(HandleException(ex, "تحويل العملة"));
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن العملة
        /// Map Data Reader to Currency Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن العملة</returns>
        private Currency MapCurrencyFromReader(SqlDataReader reader)
        {
            return new Currency
            {
                CurrencyCode = ConvertFromDb<string>(reader["CurrencyCode"]),
                CurrencyName = ConvertFromDb<string>(reader["CurrencyName"]),
                CurrencyNameAr = ConvertFromDb<string>(reader["CurrencyNameAr"]),
                CurrencySymbol = ConvertFromDb<string>(reader["CurrencySymbol"]),
                ExchangeRate = ConvertFromDb<decimal>(reader["ExchangeRate"]),
                DecimalPlaces = ConvertFromDb<int>(reader["DecimalPlaces"]),
                IsBaseCurrency = ConvertFromDb<bool>(reader["IsBaseCurrency"]),
                IsActive = ConvertFromDb<bool>(reader["IsActive"]),
                CreatedDate = ConvertFromDb<DateTime>(reader["CreatedDate"]),
                CreatedBy = ConvertFromDb<string>(reader["CreatedBy"]),
                ModifiedDate = ConvertFromDb<DateTime?>(reader["ModifiedDate"]),
                ModifiedBy = ConvertFromDb<string>(reader["ModifiedBy"])
            };
        }

        #endregion
    }
}
