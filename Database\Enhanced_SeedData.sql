-- =============================================
-- البيانات الأساسية المحسنة لنظام إدارة الدليل المحاسبي
-- Enhanced Seed Data for Chart of Accounts
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. إدراج أنواع الحسابات الأساسية
-- Insert Basic Account Types
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountTypes)
BEGIN
    INSERT INTO AccountTypes (TypeCode, TypeNameAr, TypeNameEn, TypeDescription, NormalBalance, SortOrder)
    VALUES 
    ('1', N'الأصول', 'Assets', N'جميع الأصول الثابتة والمتداولة', 'Debit', 1),
    ('2', N'الخصوم', 'Liabilities', N'جميع الالتزامات والديون', 'Credit', 2),
    ('3', N'حقوق الملكية', 'Equity', N'رأس المال والأرباح المحتجزة', 'Credit', 3),
    ('4', N'الإيرادات', 'Revenue', N'جميع الإيرادات والدخل', 'Credit', 4),
    ('5', N'المصروفات', 'Expenses', N'جميع المصروفات والتكاليف', 'Debit', 5),
    ('6', N'الحسابات النظامية', 'System Accounts', N'حسابات نظامية خاصة', 'Debit', 6);
    
    PRINT '✅ تم إدراج أنواع الحسابات الأساسية';
END
GO

-- =============================================
-- 2. إدراج تصنيفات الحسابات
-- Insert Account Groups
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountGroups)
BEGIN
    -- مجموعات الأصول
    INSERT INTO AccountGroups (GroupCode, GroupNameAr, GroupNameEn, GroupDescription, AccountTypeId, SortOrder)
    VALUES 
    ('01', N'الأصول المتداولة', 'Current Assets', N'النقدية والأصول قصيرة الأجل', 1, 1),
    ('02', N'الأصول الثابتة', 'Fixed Assets', N'الأصول طويلة الأجل', 1, 2),
    ('03', N'الاستثمارات', 'Investments', N'الاستثمارات المالية', 1, 3);
    
    -- مجموعات الخصوم
    INSERT INTO AccountGroups (GroupCode, GroupNameAr, GroupNameEn, GroupDescription, AccountTypeId, SortOrder)
    VALUES 
    ('01', N'الخصوم المتداولة', 'Current Liabilities', N'الالتزامات قصيرة الأجل', 2, 1),
    ('02', N'الخصوم طويلة الأجل', 'Long-term Liabilities', N'الالتزامات طويلة الأجل', 2, 2);
    
    -- مجموعات حقوق الملكية
    INSERT INTO AccountGroups (GroupCode, GroupNameAr, GroupNameEn, GroupDescription, AccountTypeId, SortOrder)
    VALUES 
    ('01', N'رأس المال', 'Capital', N'رأس المال المدفوع', 3, 1),
    ('02', N'الأرباح المحتجزة', 'Retained Earnings', N'الأرباح غير الموزعة', 3, 2);
    
    -- مجموعات الإيرادات
    INSERT INTO AccountGroups (GroupCode, GroupNameAr, GroupNameEn, GroupDescription, AccountTypeId, SortOrder)
    VALUES 
    ('01', N'إيرادات التشغيل', 'Operating Revenue', N'الإيرادات من النشاط الأساسي', 4, 1),
    ('02', N'إيرادات أخرى', 'Other Revenue', N'الإيرادات من أنشطة أخرى', 4, 2);
    
    -- مجموعات المصروفات
    INSERT INTO AccountGroups (GroupCode, GroupNameAr, GroupNameEn, GroupDescription, AccountTypeId, SortOrder)
    VALUES 
    ('01', N'مصروفات التشغيل', 'Operating Expenses', N'المصروفات التشغيلية', 5, 1),
    ('02', N'مصروفات إدارية', 'Administrative Expenses', N'المصروفات الإدارية والعمومية', 5, 2),
    ('03', N'مصروفات أخرى', 'Other Expenses', N'مصروفات متنوعة', 5, 3);
    
    PRINT '✅ تم إدراج تصنيفات الحسابات';
END
GO

-- =============================================
-- 3. إدراج العملات الأساسية
-- Insert Basic Currencies
-- =============================================
IF NOT EXISTS (SELECT 1 FROM Currencies)
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, CurrencySymbol, ExchangeRate, IsBaseCurrency)
    VALUES 
    ('SAR', N'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.0, 1),
    ('USD', N'دولار أمريكي', 'US Dollar', '$', 3.75, 0),
    ('EUR', N'يورو', 'Euro', '€', 4.10, 0),
    ('GBP', N'جنيه إسترليني', 'British Pound', '£', 4.65, 0),
    ('AED', N'درهم إماراتي', 'UAE Dirham', 'د.إ', 1.02, 0),
    ('KWD', N'دينار كويتي', 'Kuwaiti Dinar', 'د.ك', 12.25, 0),
    ('QAR', N'ريال قطري', 'Qatari Riyal', 'ر.ق', 1.03, 0),
    ('BHD', N'دينار بحريني', 'Bahraini Dinar', 'د.ب', 9.95, 0),
    ('OMR', N'ريال عماني', 'Omani Rial', 'ر.ع', 9.75, 0),
    ('JOD', N'دينار أردني', 'Jordanian Dinar', 'د.أ', 5.30, 0);
    
    PRINT '✅ تم إدراج العملات الأساسية';
END
GO

-- =============================================
-- 4. إدراج حسابات نموذجية
-- Insert Sample Accounts
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts)
BEGIN
    DECLARE @AssetsTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = '1');
    DECLARE @LiabilitiesTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = '2');
    DECLARE @EquityTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = '3');
    DECLARE @RevenueTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = '4');
    DECLARE @ExpensesTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = '5');
    
    DECLARE @CurrentAssetsGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = '01' AND AccountTypeId = @AssetsTypeId);
    DECLARE @FixedAssetsGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = '02' AND AccountTypeId = @AssetsTypeId);
    DECLARE @CurrentLiabilitiesGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = '01' AND AccountTypeId = @LiabilitiesTypeId);
    DECLARE @CapitalGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = '01' AND AccountTypeId = @EquityTypeId);
    DECLARE @OperatingRevenueGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = '01' AND AccountTypeId = @RevenueTypeId);
    DECLARE @OperatingExpensesGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = '01' AND AccountTypeId = @ExpensesTypeId);
    
    -- الأصول المتداولة
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, AllowPosting, OpeningBalance, CurrentBalance)
    VALUES 
    ('1.01.001', N'النقدية في الصندوق', 'Cash in Hand', @AssetsTypeId, @CurrentAssetsGroupId, 1, 0, 1, 50000.00, 50000.00),
    ('1.01.002', N'البنك الأهلي - الحساب الجاري', 'National Bank - Current Account', @AssetsTypeId, @CurrentAssetsGroupId, 1, 0, 1, 250000.00, 250000.00),
    ('1.01.003', N'بنك الراجحي - حساب التوفير', 'Al Rajhi Bank - Savings Account', @AssetsTypeId, @CurrentAssetsGroupId, 1, 0, 1, 150000.00, 150000.00),
    ('1.01.004', N'العملاء والمدينون', 'Accounts Receivable', @AssetsTypeId, @CurrentAssetsGroupId, 1, 1, 0, 75000.00, 75000.00);
    
    -- الأصول الثابتة
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, AllowPosting, OpeningBalance, CurrentBalance)
    VALUES 
    ('1.02.001', N'الأراضي والمباني', 'Land and Buildings', @AssetsTypeId, @FixedAssetsGroupId, 1, 0, 1, 1500000.00, 1500000.00),
    ('1.02.002', N'الأثاث والمعدات', 'Furniture and Equipment', @AssetsTypeId, @FixedAssetsGroupId, 1, 0, 1, 85000.00, 85000.00),
    ('1.02.003', N'السيارات', 'Vehicles', @AssetsTypeId, @FixedAssetsGroupId, 1, 0, 1, 120000.00, 120000.00);
    
    -- الخصوم المتداولة
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, AllowPosting, OpeningBalance, CurrentBalance)
    VALUES 
    ('2.01.001', N'الموردون والدائنون', 'Accounts Payable', @LiabilitiesTypeId, @CurrentLiabilitiesGroupId, 1, 1, 0, 45000.00, 45000.00),
    ('2.01.002', N'المصروفات المستحقة', 'Accrued Expenses', @LiabilitiesTypeId, @CurrentLiabilitiesGroupId, 1, 0, 1, 15000.00, 15000.00);
    
    -- حقوق الملكية
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, AllowPosting, OpeningBalance, CurrentBalance)
    VALUES 
    ('3.01.001', N'رأس المال', 'Capital', @EquityTypeId, @CapitalGroupId, 1, 0, 1, 2000000.00, 2000000.00),
    ('3.02.001', N'الأرباح المحتجزة', 'Retained Earnings', @EquityTypeId, @CapitalGroupId, 1, 0, 1, 180000.00, 180000.00);
    
    -- الإيرادات
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, AllowPosting, OpeningBalance, CurrentBalance)
    VALUES 
    ('4.01.001', N'إيرادات الخدمات', 'Service Revenue', @RevenueTypeId, @OperatingRevenueGroupId, 1, 0, 1, 0.00, 0.00),
    ('4.01.002', N'إيرادات الاستثمار', 'Investment Income', @RevenueTypeId, @OperatingRevenueGroupId, 1, 0, 1, 0.00, 0.00);
    
    -- المصروفات
    INSERT INTO ChartOfAccounts (AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, AccountLevel, IsParent, AllowPosting, OpeningBalance, CurrentBalance)
    VALUES 
    ('5.01.001', N'رواتب الموظفين', 'Employee Salaries', @ExpensesTypeId, @OperatingExpensesGroupId, 1, 0, 1, 0.00, 0.00),
    ('5.01.002', N'إيجار المكتب', 'Office Rent', @ExpensesTypeId, @OperatingExpensesGroupId, 1, 0, 1, 0.00, 0.00),
    ('5.01.003', N'مصروفات الكهرباء والماء', 'Utilities Expenses', @ExpensesTypeId, @OperatingExpensesGroupId, 1, 0, 1, 0.00, 0.00),
    ('5.01.004', N'مصروفات الاتصالات', 'Communication Expenses', @ExpensesTypeId, @OperatingExpensesGroupId, 1, 0, 1, 0.00, 0.00);
    
    PRINT '✅ تم إدراج الحسابات النموذجية';
END
GO

-- =============================================
-- 5. إدراج بيانات شخصية نموذجية
-- Insert Sample Personal Information
-- =============================================
IF NOT EXISTS (SELECT 1 FROM PersonalInformation)
BEGIN
    INSERT INTO PersonalInformation (
        FullNameAr, FullNameEn, Email, PhoneNumber, MobileNumber, 
        Address, City, Country, NationalId, Occupation, Notes
    )
    VALUES 
    (N'أحمد محمد العلي', 'Ahmed Mohammed Al-Ali', '<EMAIL>', '011-4567890', '0501234567', 
     N'شارع الملك فهد، حي العليا', N'الرياض', N'المملكة العربية السعودية', '1234567890', N'مهندس', N'عميل مهم'),
    
    (N'فاطمة سالم الزهراني', 'Fatima Salem Al-Zahrani', '<EMAIL>', '011-7654321', '0559876543', 
     N'طريق الأمير محمد بن عبدالعزيز', N'الرياض', N'المملكة العربية السعودية', '9876543210', N'طبيبة', N'عميلة دائمة'),
    
    (N'خالد عبدالله النجار', 'Khalid Abdullah Al-Najjar', '<EMAIL>', '011-1122334', '**********', 
     N'شارع التحلية، حي السلامة', N'جدة', N'المملكة العربية السعودية', '**********', N'تاجر', N'شريك تجاري');
    
    PRINT '✅ تم إدراج البيانات الشخصية النموذجية';
END
GO

-- =============================================
-- 6. ربط بعض الحسابات بالبيانات الشخصية
-- Link Some Accounts with Personal Information
-- =============================================
UPDATE ChartOfAccounts 
SET PersonalInfoId = 1 
WHERE AccountCode = '1.01.004'; -- ربط حساب العملاء بأحمد العلي

UPDATE ChartOfAccounts 
SET PersonalInfoId = 2 
WHERE AccountCode = '2.01.001'; -- ربط حساب الموردين بفاطمة الزهراني

PRINT '✅ تم ربط الحسابات بالبيانات الشخصية';
GO

PRINT '🎉 تم إنشاء جميع البيانات الأساسية بنجاح!';
PRINT '📊 النظام جاهز للاستخدام مع بيانات نموذجية شاملة';
GO
