using System;

namespace Awqaf_Managment.Services
{
    /// <summary>
    /// نموذج نتيجة العمليات في طبقة الخدمات
    /// Service Operation Result Model
    /// </summary>
    /// <typeparam name="T">نوع البيانات المرجعة</typeparam>
    public class ServiceResult<T>
    {
        #region Properties

        /// <summary>
        /// هل العملية نجحت
        /// Is Operation Successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// البيانات المرجعة
        /// Returned Data
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// رسالة النجاح أو الخطأ
        /// Success or Error Message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// رسالة الخطأ التفصيلية
        /// Detailed Error Message
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// كود الخطأ
        /// Error Code
        /// </summary>
        public string ErrorCode { get; set; }

        /// <summary>
        /// تفاصيل الاستثناء
        /// Exception Details
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// الوقت الذي تمت فيه العملية
        /// Operation Timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public ServiceResult()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// منشئ مع تحديد حالة النجاح
        /// Constructor with Success State
        /// </summary>
        /// <param name="isSuccess">هل العملية نجحت</param>
        public ServiceResult(bool isSuccess) : this()
        {
            IsSuccess = isSuccess;
        }

        /// <summary>
        /// منشئ مع تحديد حالة النجاح والبيانات
        /// Constructor with Success State and Data
        /// </summary>
        /// <param name="isSuccess">هل العملية نجحت</param>
        /// <param name="data">البيانات</param>
        public ServiceResult(bool isSuccess, T data) : this(isSuccess)
        {
            Data = data;
        }

        /// <summary>
        /// منشئ مع تحديد حالة النجاح والبيانات والرسالة
        /// Constructor with Success State, Data and Message
        /// </summary>
        /// <param name="isSuccess">هل العملية نجحت</param>
        /// <param name="data">البيانات</param>
        /// <param name="message">الرسالة</param>
        public ServiceResult(bool isSuccess, T data, string message) : this(isSuccess, data)
        {
            Message = message;
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// إنشاء نتيجة نجاح
        /// Create Success Result
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="message">رسالة النجاح</param>
        /// <returns>نتيجة النجاح</returns>
        public static ServiceResult<T> Success(T data, string message = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = data,
                Message = message ?? "تمت العملية بنجاح",
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء نتيجة نجاح بدون بيانات
        /// Create Success Result without Data
        /// </summary>
        /// <param name="message">رسالة النجاح</param>
        /// <returns>نتيجة النجاح</returns>
        public static ServiceResult<T> Success(string message = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = default(T),
                Message = message ?? "تمت العملية بنجاح",
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء نتيجة فشل
        /// Create Failure Result
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ</param>
        /// <param name="errorCode">كود الخطأ</param>
        /// <param name="exception">تفاصيل الاستثناء</param>
        /// <returns>نتيجة الفشل</returns>
        public static ServiceResult<T> Failure(string errorMessage, string errorCode = null, Exception exception = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                Data = default(T),
                ErrorMessage = errorMessage,
                Message = errorMessage,
                ErrorCode = errorCode,
                Exception = exception,
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء نتيجة فشل من استثناء
        /// Create Failure Result from Exception
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <param name="errorCode">كود الخطأ</param>
        /// <returns>نتيجة الفشل</returns>
        public static ServiceResult<T> Failure(Exception exception, string errorCode = null)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                Data = default(T),
                ErrorMessage = exception?.Message ?? "حدث خطأ غير متوقع",
                Message = exception?.Message ?? "حدث خطأ غير متوقع",
                ErrorCode = errorCode,
                Exception = exception,
                Timestamp = DateTime.Now
            };
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل النتيجة إلى نوع آخر
        /// Convert Result to Another Type
        /// </summary>
        /// <typeparam name="TNew">النوع الجديد</typeparam>
        /// <param name="newData">البيانات الجديدة</param>
        /// <returns>النتيجة المحولة</returns>
        public ServiceResult<TNew> ConvertTo<TNew>(TNew newData = default(TNew))
        {
            return new ServiceResult<TNew>
            {
                IsSuccess = this.IsSuccess,
                Data = newData,
                Message = this.Message,
                ErrorMessage = this.ErrorMessage,
                ErrorCode = this.ErrorCode,
                Exception = this.Exception,
                Timestamp = this.Timestamp
            };
        }

        /// <summary>
        /// تحويل النتيجة إلى نوع آخر مع دالة تحويل
        /// Convert Result to Another Type with Converter Function
        /// </summary>
        /// <typeparam name="TNew">النوع الجديد</typeparam>
        /// <param name="converter">دالة التحويل</param>
        /// <returns>النتيجة المحولة</returns>
        public ServiceResult<TNew> ConvertTo<TNew>(Func<T, TNew> converter)
        {
            if (!IsSuccess || converter == null)
            {
                return new ServiceResult<TNew>
                {
                    IsSuccess = this.IsSuccess,
                    Data = default(TNew),
                    Message = this.Message,
                    ErrorMessage = this.ErrorMessage,
                    ErrorCode = this.ErrorCode,
                    Exception = this.Exception,
                    Timestamp = this.Timestamp
                };
            }

            try
            {
                var convertedData = converter(Data);
                return new ServiceResult<TNew>
                {
                    IsSuccess = true,
                    Data = convertedData,
                    Message = this.Message,
                    ErrorMessage = this.ErrorMessage,
                    ErrorCode = this.ErrorCode,
                    Exception = this.Exception,
                    Timestamp = this.Timestamp
                };
            }
            catch (Exception ex)
            {
                return ServiceResult<TNew>.Failure(ex, "CONVERSION_ERROR");
            }
        }

        /// <summary>
        /// الحصول على رسالة مناسبة للعرض للمستخدم
        /// Get User-Friendly Display Message
        /// </summary>
        /// <returns>الرسالة</returns>
        public string GetDisplayMessage()
        {
            if (IsSuccess)
            {
                return Message ?? "تمت العملية بنجاح";
            }
            else
            {
                return ErrorMessage ?? Message ?? "حدث خطأ غير متوقع";
            }
        }

        /// <summary>
        /// تحويل النتيجة إلى نص
        /// Convert Result to String
        /// </summary>
        /// <returns>النص</returns>
        public override string ToString()
        {
            if (IsSuccess)
            {
                return $"Success: {Message ?? "تمت العملية بنجاح"}";
            }
            else
            {
                var errorInfo = ErrorMessage ?? Message ?? "حدث خطأ غير متوقع";
                if (!string.IsNullOrEmpty(ErrorCode))
                {
                    errorInfo = $"[{ErrorCode}] {errorInfo}";
                }
                return $"Failure: {errorInfo}";
            }
        }

        #endregion
    }

    /// <summary>
    /// نموذج نتيجة العمليات بدون بيانات مرجعة
    /// Service Operation Result Model without Return Data
    /// </summary>
    public class ServiceResult : ServiceResult<object>
    {
        #region Constructors

        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public ServiceResult() : base()
        {
        }

        /// <summary>
        /// منشئ مع تحديد حالة النجاح
        /// Constructor with Success State
        /// </summary>
        /// <param name="isSuccess">هل العملية نجحت</param>
        public ServiceResult(bool isSuccess) : base(isSuccess)
        {
        }

        /// <summary>
        /// منشئ مع تحديد حالة النجاح والرسالة
        /// Constructor with Success State and Message
        /// </summary>
        /// <param name="isSuccess">هل العملية نجحت</param>
        /// <param name="message">الرسالة</param>
        public ServiceResult(bool isSuccess, string message) : base(isSuccess, null, message)
        {
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// إنشاء نتيجة نجاح
        /// Create Success Result
        /// </summary>
        /// <param name="message">رسالة النجاح</param>
        /// <returns>نتيجة النجاح</returns>
        public new static ServiceResult Success(string message = null)
        {
            return new ServiceResult
            {
                IsSuccess = true,
                Message = message ?? "تمت العملية بنجاح",
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء نتيجة فشل
        /// Create Failure Result
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ</param>
        /// <param name="errorCode">كود الخطأ</param>
        /// <param name="exception">تفاصيل الاستثناء</param>
        /// <returns>نتيجة الفشل</returns>
        public new static ServiceResult Failure(string errorMessage, string errorCode = null, Exception exception = null)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Message = errorMessage,
                ErrorCode = errorCode,
                Exception = exception,
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء نتيجة فشل من استثناء
        /// Create Failure Result from Exception
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <param name="errorCode">كود الخطأ</param>
        /// <returns>نتيجة الفشل</returns>
        public new static ServiceResult Failure(Exception exception, string errorCode = null)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                ErrorMessage = exception?.Message ?? "حدث خطأ غير متوقع",
                Message = exception?.Message ?? "حدث خطأ غير متوقع",
                ErrorCode = errorCode,
                Exception = exception,
                Timestamp = DateTime.Now
            };
        }

        #endregion
    }
}
