@echo off
chcp 65001 > nul
echo.
echo ========================================
echo 🚀 بناء وتشغيل نظام إدارة الأوقاف
echo 🚀 Building and Running Awqaf Management System
echo ========================================
echo.

echo 📁 التحقق من الملفات المطلوبة...
echo 📁 Checking required files...

if not exist "UI\Forms\TestMainForm.cs" (
    echo ❌ ملف TestMainForm.cs مفقود
    echo ❌ TestMainForm.cs file is missing
    pause
    exit /b 1
)

if not exist "UI\Forms\Accounting\ChartOfAccountsManagementForm.cs" (
    echo ❌ ملف ChartOfAccountsManagementForm.cs مفقود
    echo ❌ ChartOfAccountsManagementForm.cs file is missing
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo ✅ All required files found

echo.
echo 🔨 بدء عملية التجميع...
echo 🔨 Starting compilation...

REM إنشاء مجلد الإخراج
if not exist "bin\Debug" mkdir "bin\Debug"

REM تجميع النظام
csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Data.dll ^
    /reference:System.ComponentModel.DataAnnotations.dll ^
    /reference:System.Data.SqlClient.dll ^
    /reference:System.Configuration.dll ^
    /out:"bin\Debug\AwqafManagement.exe" ^
    /debug+ ^
    /define:DEBUG ^
    "TestSimpleRun.cs" ^
    "DatabaseConfig.cs" ^
    "UI\Forms\TestMainForm.cs" ^
    "UI\Forms\Accounting\ChartOfAccountsManagementForm.cs" ^
    "UI\Forms\Accounting\ChartOfAccountsManagementForm.Designer.cs" ^
    "UI\Controls\EnhancedAccountsTreeView.cs" ^
    "UI\Controls\EnhancedAccountsTreeView.Designer.cs" ^
    "UI\Controls\AccountSelectedEventArgs.cs" ^
    "Models\ChartOfAccount.cs" ^
    "Models\AccountType.cs" ^
    "Models\AccountGroup.cs" ^
    "Models\Currency.cs" ^
    "Models\PersonalInformation.cs" ^
    "DataAccess\Base\BaseDataAccess.cs" ^
    "DataAccess\DatabaseConnection.cs" ^
    "DataAccess\ChartOfAccountsDataAccess.cs" ^
    "DataAccess\AccountTypeDataAccess.cs" ^
    "DataAccess\AccountGroupDataAccess.cs" ^
    "DataAccess\CurrencyDataAccess.cs" ^
    "DataAccess\PersonalInformationDataAccess.cs" ^
    "Services\ServiceResult.cs" ^
    "Services\ValidationService.cs" ^
    "Services\BusinessRulesService.cs"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم التجميع بنجاح!
    echo ✅ Compilation successful!
    echo.
    echo 📋 الملفات المُجمعة:
    echo 📋 Compiled files:
    echo   - TestMainForm (النموذج الاختباري)
    echo   - ChartOfAccountsManagementForm (نموذج إدارة الحسابات)
    echo   - EnhancedAccountsTreeView (شجرة الحسابات المحسنة)
    echo   - Data Access Classes (طبقات الوصول للبيانات)
    echo   - Business Services (خدمات الأعمال)
    echo   - Models (النماذج)
    echo.
    echo 🚀 تشغيل النظام...
    echo 🚀 Running system...
    echo.
    start "" "bin\Debug\AwqafManagement.exe"
) else (
    echo.
    echo ❌ فشل في التجميع
    echo ❌ Compilation failed
    echo.
    echo 🔍 الأخطاء المحتملة:
    echo 🔍 Possible errors:
    echo   - ملفات مفقودة (Missing files)
    echo   - أخطاء في الكود (Code errors)
    echo   - مراجع مفقودة (Missing references)
    echo.
    echo تحقق من الأخطاء أعلاه
    echo Check errors above
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
