@echo off
echo تشغيل نموذج الدليل المحاسبي المتقدم...
echo Running Enhanced Chart of Accounts Form...

cd /d "%~dp0"

echo تجميع المشروع...
echo Compiling project...

csc /target:winexe /reference:System.dll /reference:System.Windows.Forms.dll /reference:System.Drawing.dll /reference:System.Data.dll /reference:System.ComponentModel.DataAnnotations.dll /reference:System.Data.SqlClient.dll /out:EnhancedChartOfAccounts.exe TestEnhancedChartOfAccounts.cs Models\Accounting\*.cs DataAccess\Accounting\Enhanced_ChartOfAccountsDataAccess.cs DataAccess\Accounting\PersonalInformationDataAccess.cs DataAccess\DatabaseConnection.cs Services\Accounting\Enhanced_ChartOfAccountsService.cs Services\Accounting\PersonalInformationService.cs Forms\Accounting\Enhanced_ChartOfAccountsForm.cs Forms\Accounting\Enhanced_ChartOfAccountsForm.Designer.cs

if %ERRORLEVEL% EQU 0 (
    echo تم التجميع بنجاح، تشغيل التطبيق...
    echo Compilation successful, running application...
    EnhancedChartOfAccounts.exe
) else (
    echo فشل في التجميع
    echo Compilation failed
    pause
)
