@echo off
echo ========================================
echo تشغيل سكريبت حذف جداول النظام المحاسبي
echo Running Delete Accounting Tables Script
echo ========================================

cd /d "%~dp0"

echo.
echo تحذير: سيتم حذف جميع جداول النظام المحاسبي من قاعدة البيانات!
echo WARNING: This will delete all accounting system tables from the database!
echo.
echo الجداول التي سيتم حذفها:
echo Tables to be deleted:
echo - ChartOfAccounts (دليل الحسابات)
echo - JournalEntries (القيود اليومية)
echo - JournalEntryDetails (تفاصيل القيود اليومية)
echo - AccountTypes (أنواع الحسابات)
echo - AccountGroups (مجموعات الحسابات)
echo - Currencies (العملات)
echo - PersonalInformation (المعلومات الشخصية)
echo - AccountAuditLog (سجل مراجعة الحسابات)
echo - JournalTypes (أنواع القيود)
echo.
echo الإجراءات المخزنة التي سيتم حذفها:
echo Stored procedures to be deleted:
echo - SP_SearchAccounts
echo - SP_GetAccountsHierarchy
echo - SP_GenerateAccountCode
echo - SP_SaveAccount
echo - SP_SaveJournalEntry
echo.
echo المشغلات التي سيتم حذفها:
echo Triggers to be deleted:
echo - TR_ChartOfAccounts_Audit
echo.

set /p confirm="هل أنت متأكد من المتابعة؟ (y/N) Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo تم إلغاء العملية
    echo Operation cancelled
    goto :end
)

echo.
echo تشغيل سكريبت SQL...
echo Running SQL script...

REM تشغيل سكريبت SQL باستخدام sqlcmd
sqlcmd -S NAJEEB -d AwqafManagement -E -i "Database\Scripts\Delete_Accounting_Tables.sql"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تشغيل السكريبت بنجاح!
    echo ✅ Script executed successfully!
    echo.
    echo تم حذف جميع جداول النظام المحاسبي من قاعدة البيانات
    echo All accounting system tables have been deleted from the database
) else (
    echo.
    echo ❌ فشل في تشغيل السكريبت
    echo ❌ Script execution failed
    echo.
    echo تحقق من:
    echo Check:
    echo 1. اتصال قاعدة البيانات
    echo 1. Database connection
    echo 2. صلاحيات المستخدم
    echo 2. User permissions
    echo 3. وجود قاعدة البيانات AwqafManagement
    echo 3. AwqafManagement database exists
)

:end
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
