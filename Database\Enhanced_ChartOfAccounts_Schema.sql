-- =============================================
-- نظام إدارة الدليل المحاسبي المتطور
-- Enhanced Chart of Accounts Management System
-- تاريخ الإنشاء: 2025-07-03
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. جدول أنواع الحسابات المحسن
-- Enhanced Account Types Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountTypes')
BEGIN
    CREATE TABLE AccountTypes (
        AccountTypeId INT IDENTITY(1,1) PRIMARY KEY,
        TypeCode NVARCHAR(10) NOT NULL UNIQUE,
        TypeNameAr NVARCHAR(100) NOT NULL,
        TypeNameEn NVARCHAR(100) NOT NULL,
        TypeDescription NVARCHAR(500),
        NormalBalance NVARCHAR(10) CHECK (NormalBalance IN ('Debit', 'Credit')),
        IsActive BIT DEFAULT 1,
        SortOrder INT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(100) DEFAULT SYSTEM_USER,
        ModifiedDate DATETIME2,
        ModifiedBy NVARCHAR(100)
    );
    
    CREATE INDEX IX_AccountTypes_TypeCode ON AccountTypes(TypeCode);
    CREATE INDEX IX_AccountTypes_IsActive ON AccountTypes(IsActive);
END
GO

-- =============================================
-- 2. جدول تصنيفات الحسابات المحسن
-- Enhanced Account Groups Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountGroups')
BEGIN
    CREATE TABLE AccountGroups (
        AccountGroupId INT IDENTITY(1,1) PRIMARY KEY,
        GroupCode NVARCHAR(10) NOT NULL,
        GroupNameAr NVARCHAR(100) NOT NULL,
        GroupNameEn NVARCHAR(100) NOT NULL,
        GroupDescription NVARCHAR(500),
        AccountTypeId INT NOT NULL,
        ParentGroupId INT NULL,
        GroupLevel INT DEFAULT 1,
        IsActive BIT DEFAULT 1,
        SortOrder INT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(100) DEFAULT SYSTEM_USER,
        ModifiedDate DATETIME2,
        ModifiedBy NVARCHAR(100),
        
        CONSTRAINT FK_AccountGroups_AccountTypes 
            FOREIGN KEY (AccountTypeId) REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_AccountGroups_ParentGroup 
            FOREIGN KEY (ParentGroupId) REFERENCES AccountGroups(AccountGroupId)
    );
    
    CREATE INDEX IX_AccountGroups_AccountTypeId ON AccountGroups(AccountTypeId);
    CREATE INDEX IX_AccountGroups_ParentGroupId ON AccountGroups(ParentGroupId);
    CREATE INDEX IX_AccountGroups_IsActive ON AccountGroups(IsActive);
    CREATE UNIQUE INDEX IX_AccountGroups_GroupCode_TypeId ON AccountGroups(GroupCode, AccountTypeId);
END
GO

-- =============================================
-- 3. جدول العملات المحسن
-- Enhanced Currencies Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Currencies')
BEGIN
    CREATE TABLE Currencies (
        CurrencyId INT IDENTITY(1,1) PRIMARY KEY,
        CurrencyCode NVARCHAR(3) NOT NULL UNIQUE,
        CurrencyNameAr NVARCHAR(50) NOT NULL,
        CurrencyNameEn NVARCHAR(50) NOT NULL,
        CurrencySymbol NVARCHAR(5),
        ExchangeRate DECIMAL(18,6) DEFAULT 1.0,
        IsBaseCurrency BIT DEFAULT 0,
        IsActive BIT DEFAULT 1,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(100) DEFAULT SYSTEM_USER,
        ModifiedDate DATETIME2,
        ModifiedBy NVARCHAR(100)
    );
    
    CREATE INDEX IX_Currencies_CurrencyCode ON Currencies(CurrencyCode);
    CREATE INDEX IX_Currencies_IsActive ON Currencies(IsActive);
END
GO

-- =============================================
-- 4. جدول البيانات الشخصية الجديد
-- Personal Information Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PersonalInformation')
BEGIN
    CREATE TABLE PersonalInformation (
        PersonalInfoId INT IDENTITY(1,1) PRIMARY KEY,
        FullNameAr NVARCHAR(200) NOT NULL,
        FullNameEn NVARCHAR(200),
        Email NVARCHAR(100),
        PhoneNumber NVARCHAR(20),
        MobileNumber NVARCHAR(20),
        Address NVARCHAR(500),
        City NVARCHAR(100),
        Country NVARCHAR(100),
        PostalCode NVARCHAR(20),
        NationalId NVARCHAR(50),
        PassportNumber NVARCHAR(50),
        DateOfBirth DATE,
        Gender NVARCHAR(10) CHECK (Gender IN ('Male', 'Female')),
        Nationality NVARCHAR(100),
        Occupation NVARCHAR(100),
        CompanyName NVARCHAR(200),
        TaxNumber NVARCHAR(50),
        BankAccountNumber NVARCHAR(50),
        BankName NVARCHAR(100),
        Notes NVARCHAR(1000),
        IsActive BIT DEFAULT 1,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(100) DEFAULT SYSTEM_USER,
        ModifiedDate DATETIME2,
        ModifiedBy NVARCHAR(100)
    );
    
    CREATE INDEX IX_PersonalInfo_FullNameAr ON PersonalInformation(FullNameAr);
    CREATE INDEX IX_PersonalInfo_Email ON PersonalInformation(Email);
    CREATE INDEX IX_PersonalInfo_NationalId ON PersonalInformation(NationalId);
    CREATE INDEX IX_PersonalInfo_IsActive ON PersonalInformation(IsActive);
END
GO

-- =============================================
-- 5. جدول دليل الحسابات المحسن والشامل
-- Enhanced and Comprehensive Chart of Accounts Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ChartOfAccounts')
BEGIN
    CREATE TABLE ChartOfAccounts (
        -- المعرفات الأساسية
        AccountId INT IDENTITY(1,1) PRIMARY KEY,
        AccountCode NVARCHAR(20) NOT NULL UNIQUE,

        -- أسماء الحساب
        AccountNameAr NVARCHAR(200) NOT NULL,
        AccountNameEn NVARCHAR(200),

        -- التصنيفات
        AccountTypeId INT NOT NULL,
        AccountGroupId INT NOT NULL,
        ParentAccountId INT NULL,
        PersonalInfoId INT NULL,

        -- الهيكل الهرمي
        AccountLevel INT DEFAULT 1,
        AccountPath NVARCHAR(500), -- المسار الهرمي الكامل
        IsParent BIT DEFAULT 0,

        -- حالة الحساب
        IsActive BIT DEFAULT 1,
        AllowPosting BIT DEFAULT 1,
        AllowDirectEntry BIT DEFAULT 1, -- السماح بالإدخال المباشر

        -- الوصف والملاحظات
        Description NVARCHAR(1000),
        Notes NVARCHAR(2000), -- ملاحظات إضافية

        -- العملة والأرصدة
        CurrencyCode NVARCHAR(3) DEFAULT 'SAR',
        OpeningBalance DECIMAL(18,4) DEFAULT 0,
        CurrentBalance DECIMAL(18,4) DEFAULT 0,
        DebitBalance DECIMAL(18,4) DEFAULT 0,
        CreditBalance DECIMAL(18,4) DEFAULT 0,

        -- طبيعة الحساب
        Nature NVARCHAR(10) DEFAULT 'مدين', -- مدين أو دائن
        BalanceType NVARCHAR(10) DEFAULT 'مدين', -- نوع الرصيد

        -- معلومات التدقيق
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT,
        LastTransactionDate DATETIME2,

        -- معلومات إضافية
        TaxNumber NVARCHAR(50), -- الرقم الضريبي
        CommercialRegister NVARCHAR(50), -- السجل التجاري
        BankAccount NVARCHAR(50), -- رقم الحساب البنكي
        IBAN NVARCHAR(34), -- رقم الآيبان

        -- إعدادات متقدمة
        RequiresCostCenter BIT DEFAULT 0, -- يتطلب مركز تكلفة
        RequiresProject BIT DEFAULT 0, -- يتطلب مشروع
        AutoGenerateCode BIT DEFAULT 1, -- توليد الرمز تلقائياً

        -- قيود الفهارس والعلاقات
        CONSTRAINT FK_ChartOfAccounts_AccountType FOREIGN KEY (AccountTypeId)
            REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_ChartOfAccounts_AccountGroup FOREIGN KEY (AccountGroupId)
            REFERENCES AccountGroups(AccountGroupId),
        CONSTRAINT FK_ChartOfAccounts_Parent FOREIGN KEY (ParentAccountId)
            REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_ChartOfAccounts_PersonalInfo FOREIGN KEY (PersonalInfoId)
            REFERENCES PersonalInformation(PersonalInfoId),
        CONSTRAINT FK_ChartOfAccounts_CreatedBy FOREIGN KEY (CreatedBy)
            REFERENCES Users(UserId),
        CONSTRAINT FK_ChartOfAccounts_ModifiedBy FOREIGN KEY (ModifiedBy)
            REFERENCES Users(UserId)
    );

    -- إنشاء الفهارس لتحسين الأداء
    CREATE INDEX IX_ChartOfAccounts_AccountCode ON ChartOfAccounts(AccountCode);
    CREATE INDEX IX_ChartOfAccounts_AccountNameAr ON ChartOfAccounts(AccountNameAr);
    CREATE INDEX IX_ChartOfAccounts_AccountType ON ChartOfAccounts(AccountTypeId);
    CREATE INDEX IX_ChartOfAccounts_AccountGroup ON ChartOfAccounts(AccountGroupId);
    CREATE INDEX IX_ChartOfAccounts_Parent ON ChartOfAccounts(ParentAccountId);
    CREATE INDEX IX_ChartOfAccounts_Level ON ChartOfAccounts(AccountLevel);
    CREATE INDEX IX_ChartOfAccounts_Active ON ChartOfAccounts(IsActive);
    CREATE INDEX IX_ChartOfAccounts_Path ON ChartOfAccounts(AccountPath);

    PRINT 'تم إنشاء جدول دليل الحسابات المحسن بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول دليل الحسابات موجود مسبقاً';
END
        CurrentBalance DECIMAL(18,4) DEFAULT 0,
        DebitBalance DECIMAL(18,4) DEFAULT 0,
        CreditBalance DECIMAL(18,4) DEFAULT 0,
        LastTransactionDate DATETIME2,
        SortOrder INT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(100) DEFAULT SYSTEM_USER,
        ModifiedDate DATETIME2,
        ModifiedBy NVARCHAR(100),
        
        CONSTRAINT FK_ChartOfAccounts_AccountTypes 
            FOREIGN KEY (AccountTypeId) REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_ChartOfAccounts_AccountGroups 
            FOREIGN KEY (AccountGroupId) REFERENCES AccountGroups(AccountGroupId),
        CONSTRAINT FK_ChartOfAccounts_ParentAccount 
            FOREIGN KEY (ParentAccountId) REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_ChartOfAccounts_PersonalInfo 
            FOREIGN KEY (PersonalInfoId) REFERENCES PersonalInformation(PersonalInfoId),
        CONSTRAINT FK_ChartOfAccounts_Currency 
            FOREIGN KEY (CurrencyCode) REFERENCES Currencies(CurrencyCode)
    );
    
    CREATE INDEX IX_ChartOfAccounts_AccountCode ON ChartOfAccounts(AccountCode);
    CREATE INDEX IX_ChartOfAccounts_AccountTypeId ON ChartOfAccounts(AccountTypeId);
    CREATE INDEX IX_ChartOfAccounts_AccountGroupId ON ChartOfAccounts(AccountGroupId);
    CREATE INDEX IX_ChartOfAccounts_ParentAccountId ON ChartOfAccounts(ParentAccountId);
    CREATE INDEX IX_ChartOfAccounts_PersonalInfoId ON ChartOfAccounts(PersonalInfoId);
    CREATE INDEX IX_ChartOfAccounts_IsActive ON ChartOfAccounts(IsActive);
    CREATE INDEX IX_ChartOfAccounts_AccountNameAr ON ChartOfAccounts(AccountNameAr);
END
GO

-- =============================================
-- 6. جدول سجل التعديلات الجديد
-- Audit Log Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountAuditLog')
BEGIN
    CREATE TABLE AccountAuditLog (
        AuditId INT IDENTITY(1,1) PRIMARY KEY,
        AccountId INT NOT NULL,
        OperationType NVARCHAR(20) NOT NULL CHECK (OperationType IN ('INSERT', 'UPDATE', 'DELETE')),
        OldValues NVARCHAR(MAX),
        NewValues NVARCHAR(MAX),
        ChangedFields NVARCHAR(500),
        ChangeReason NVARCHAR(500),
        UserId NVARCHAR(100) NOT NULL,
        UserName NVARCHAR(200),
        ChangeDate DATETIME2 DEFAULT GETDATE(),
        IPAddress NVARCHAR(50),
        ComputerName NVARCHAR(100)
    );
    
    CREATE INDEX IX_AccountAuditLog_AccountId ON AccountAuditLog(AccountId);
    CREATE INDEX IX_AccountAuditLog_ChangeDate ON AccountAuditLog(ChangeDate);
    CREATE INDEX IX_AccountAuditLog_UserId ON AccountAuditLog(UserId);
END
GO

-- =============================================
-- 7. المحفزات (Triggers) للتدقيق التلقائي
-- Automatic Audit Triggers
-- =============================================

-- محفز تدقيق الإدراج
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_ChartOfAccounts_Insert_Audit')
    DROP TRIGGER TR_ChartOfAccounts_Insert_Audit;
GO

CREATE TRIGGER TR_ChartOfAccounts_Insert_Audit
ON ChartOfAccounts
AFTER INSERT
AS
BEGIN
    SET NOCOUNT ON;

    INSERT INTO AccountAuditLog (
        AccountId, OperationType, NewValues, ChangedFields,
        UserId, UserName, ChangeDate
    )
    SELECT
        i.AccountId,
        'INSERT',
        CONCAT('AccountCode:', i.AccountCode, '; AccountNameAr:', i.AccountNameAr,
               '; AccountTypeId:', i.AccountTypeId, '; AccountGroupId:', i.AccountGroupId),
        'جميع الحقول (إدراج جديد)',
        ISNULL(i.CreatedBy, SYSTEM_USER),
        ISNULL(i.CreatedBy, SYSTEM_USER),
        GETDATE()
    FROM inserted i;
END
GO

-- محفز تدقيق التحديث
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_ChartOfAccounts_Update_Audit')
    DROP TRIGGER TR_ChartOfAccounts_Update_Audit;
GO

CREATE TRIGGER TR_ChartOfAccounts_Update_Audit
ON ChartOfAccounts
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;

    INSERT INTO AccountAuditLog (
        AccountId, OperationType, OldValues, NewValues, ChangedFields,
        UserId, UserName, ChangeDate
    )
    SELECT
        i.AccountId,
        'UPDATE',
        CONCAT('AccountCode:', d.AccountCode, '; AccountNameAr:', d.AccountNameAr,
               '; IsActive:', d.IsActive),
        CONCAT('AccountCode:', i.AccountCode, '; AccountNameAr:', i.AccountNameAr,
               '; IsActive:', i.IsActive),
        CASE
            WHEN d.AccountCode != i.AccountCode THEN 'AccountCode, '
            ELSE ''
        END +
        CASE
            WHEN d.AccountNameAr != i.AccountNameAr THEN 'AccountNameAr, '
            ELSE ''
        END +
        CASE
            WHEN d.IsActive != i.IsActive THEN 'IsActive, '
            ELSE ''
        END,
        ISNULL(i.ModifiedBy, SYSTEM_USER),
        ISNULL(i.ModifiedBy, SYSTEM_USER),
        GETDATE()
    FROM inserted i
    INNER JOIN deleted d ON i.AccountId = d.AccountId;
END
GO

PRINT '✅ تم إنشاء جداول قاعدة البيانات المحسنة بنجاح!';
