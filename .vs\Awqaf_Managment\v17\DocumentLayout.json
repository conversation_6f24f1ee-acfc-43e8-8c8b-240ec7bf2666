{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\dataaccess\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:dataaccess\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\dataaccess\\chartofaccountsdataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:dataaccess\\chartofaccountsdataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\controls\\enhancedaccountstreeview.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\controls\\enhancedaccountstreeview.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\dataaccess\\accounting\\personalinformationdataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:dataaccess\\accounting\\personalinformationdataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\dataaccess\\accounting\\enhancedchartofaccountsdataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:dataaccess\\accounting\\enhancedchartofaccountsdataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\dataaccess\\base\\basedataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:dataaccess\\base\\basedataaccess.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\forms\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\forms\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\forms\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\forms\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\forms\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\forms\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\forms\\accounting\\chartofaccountsmanagementform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\forms\\accounting\\chartofaccountsmanagementform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\forms\\accounting\\currencymanagementform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\forms\\accounting\\currencymanagementform.cs||{A6C744A8-0E4A-4FC6-886A-************}|Form"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\forms\\accounting\\chartofaccountsmanagementform.designer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\forms\\accounting\\chartofaccountsmanagementform.designer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\controls\\enhancedaccountstreeview.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\controls\\enhancedaccountstreeview.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|c:\\users\\<USER>\\desktop\\awqaf_managment\\ui\\controls\\enhancedaccountstreeview.resx||{********-B8B3-4D2B-99A3-067027C180C1}", "RelativeMoniker": "D:0:0:{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}|Awqaf_Managment.csproj|solutionrelative:ui\\controls\\enhancedaccountstreeview.resx||{********-B8B3-4D2B-99A3-067027C180C1}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "DatabaseConnection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\DatabaseConnection.cs", "RelativeDocumentMoniker": "DataAccess\\DatabaseConnection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\DatabaseConnection.cs", "RelativeToolTip": "DataAccess\\DatabaseConnection.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:33:37.686Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ChartOfAccountsDataAccess.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\ChartOfAccountsDataAccess.cs", "RelativeDocumentMoniker": "DataAccess\\ChartOfAccountsDataAccess.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\ChartOfAccountsDataAccess.cs", "RelativeToolTip": "DataAccess\\ChartOfAccountsDataAccess.cs", "ViewState": "AgIAABUBAAAAAAAAAAAAwBQBAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:33:18.893Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "PersonalInformationDataAccess.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\Accounting\\PersonalInformationDataAccess.cs", "RelativeDocumentMoniker": "DataAccess\\Accounting\\PersonalInformationDataAccess.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\Accounting\\PersonalInformationDataAccess.cs", "RelativeToolTip": "DataAccess\\Accounting\\PersonalInformationDataAccess.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:32:56.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "EnhancedChartOfAccountsDataAccess.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\Accounting\\EnhancedChartOfAccountsDataAccess.cs", "RelativeDocumentMoniker": "DataAccess\\Accounting\\EnhancedChartOfAccountsDataAccess.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\Accounting\\EnhancedChartOfAccountsDataAccess.cs", "RelativeToolTip": "DataAccess\\Accounting\\EnhancedChartOfAccountsDataAccess.cs", "ViewState": "AgIAABEAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:32:50.414Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "BaseDataAccess.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\Base\\BaseDataAccess.cs", "RelativeDocumentMoniker": "DataAccess\\Base\\BaseDataAccess.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\DataAccess\\Base\\BaseDataAccess.cs", "RelativeToolTip": "DataAccess\\Base\\BaseDataAccess.cs", "ViewState": "AgIAAMQAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:32:31.877Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "LoginForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\LoginForm.cs", "RelativeDocumentMoniker": "UI\\Forms\\LoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\LoginForm.cs", "RelativeToolTip": "UI\\Forms\\LoginForm.cs", "ViewState": "AgIAACkAAAAAAAAAAAAcwDUAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:32:01.814Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "EnhancedAccountsTreeView.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Controls\\EnhancedAccountsTreeView.cs", "RelativeDocumentMoniker": "UI\\Controls\\EnhancedAccountsTreeView.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Controls\\EnhancedAccountsTreeView.cs [Design]", "RelativeToolTip": "UI\\Controls\\EnhancedAccountsTreeView.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:30:03.549Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "LoginForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\LoginForm.cs", "RelativeDocumentMoniker": "UI\\Forms\\LoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\LoginForm.cs [Design]", "RelativeToolTip": "UI\\Forms\\LoginForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:32:00.647Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 8, "Title": "MainForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\MainForm.cs", "RelativeDocumentMoniker": "UI\\Forms\\MainForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\MainForm.cs [Design]", "RelativeToolTip": "UI\\Forms\\MainForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:31:28.965Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 10, "Title": "CurrencyManagementForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\Accounting\\CurrencyManagementForm.cs", "RelativeDocumentMoniker": "UI\\Forms\\Accounting\\CurrencyManagementForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\Accounting\\CurrencyManagementForm.cs [Design]", "RelativeToolTip": "UI\\Forms\\Accounting\\CurrencyManagementForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:30:25.924Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 11, "Title": "ChartOfAccountsManagementForm.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.Designer.cs", "RelativeDocumentMoniker": "UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.Designer.cs", "RelativeToolTip": "UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.Designer.cs", "ViewState": "AgIAADEBAAAAAAAAAAAtwDwBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:30:18.285Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ChartOfAccountsManagementForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.cs", "RelativeDocumentMoniker": "UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.cs [Design]", "RelativeToolTip": "UI\\Forms\\Accounting\\ChartOfAccountsManagementForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:30:16.098Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 12, "Title": "EnhancedAccountsTreeView.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Controls\\EnhancedAccountsTreeView.cs", "RelativeDocumentMoniker": "UI\\Controls\\EnhancedAccountsTreeView.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Controls\\EnhancedAccountsTreeView.cs", "RelativeToolTip": "UI\\Controls\\EnhancedAccountsTreeView.cs", "ViewState": "AgIAAAMBAAAAAAAAAAAAwAEBAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T23:30:06.223Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "EnhancedAccountsTreeView.resx", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Controls\\EnhancedAccountsTreeView.resx", "RelativeDocumentMoniker": "UI\\Controls\\EnhancedAccountsTreeView.resx", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Awqaf_Managment\\UI\\Controls\\EnhancedAccountsTreeView.resx", "RelativeToolTip": "UI\\Controls\\EnhancedAccountsTreeView.resx", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-07-05T23:29:58.522Z", "EditorCaption": ""}]}]}]}