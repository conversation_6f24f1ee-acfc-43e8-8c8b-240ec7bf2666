using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Awqaf_Managment.Models;
using Awqaf_Managment.Services;

namespace Awqaf_Managment.Services.Accounting
{
    /// <summary>
    /// خدمة التصدير والتقارير المحاسبية
    /// Accounting Export and Reports Service
    /// </summary>
    public class AccountingExportService
    {
        #region التصدير إلى Excel - Export to Excel

        /// <summary>
        /// تصدير دليل الحسابات إلى Excel
        /// Export Chart of Accounts to Excel
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="includeDetails">تضمين التفاصيل الكاملة</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ServiceResult<string>> ExportChartOfAccountsToExcelAsync(
            List<ChartOfAccount> accounts, 
            string filePath = null, 
            bool includeDetails = true)
        {
            try
            {
                // إنشاء مسار الملف إذا لم يتم تمريره
                if (string.IsNullOrEmpty(filePath))
                {
                    var fileName = $"دليل_الحسابات_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                    filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);
                }

                // إنشاء محتوى CSV
                var csvContent = GenerateChartOfAccountsCsv(accounts, includeDetails);

                // كتابة الملف
                await File.WriteAllTextAsync(filePath, csvContent, Encoding.UTF8);

                return ServiceResult<string>.Success(filePath, 
                    $"تم تصدير {accounts.Count} حساب بنجاح إلى: {filePath}");
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في تصدير دليل الحسابات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير تقرير أرصدة الحسابات إلى Excel
        /// Export Account Balances Report to Excel
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="asOfDate">تاريخ التقرير</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ServiceResult<string>> ExportAccountBalancesReportAsync(
            List<ChartOfAccount> accounts, 
            string filePath = null, 
            DateTime? asOfDate = null)
        {
            try
            {
                // إنشاء مسار الملف إذا لم يتم تمريره
                if (string.IsNullOrEmpty(filePath))
                {
                    var fileName = $"تقرير_أرصدة_الحسابات_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                    filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);
                }

                // إنشاء محتوى CSV
                var csvContent = GenerateAccountBalancesCsv(accounts, asOfDate ?? DateTime.Now);

                // كتابة الملف
                await File.WriteAllTextAsync(filePath, csvContent, Encoding.UTF8);

                return ServiceResult<string>.Success(filePath, 
                    $"تم تصدير تقرير أرصدة {accounts.Count} حساب بنجاح إلى: {filePath}");
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في تصدير تقرير الأرصدة: {ex.Message}");
            }
        }

        #endregion

        #region إنتاج التقارير - Generate Reports

        /// <summary>
        /// إنتاج تقرير دليل الحسابات الهرمي
        /// Generate Hierarchical Chart of Accounts Report
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>تقرير نصي</returns>
        public ServiceResult<string> GenerateHierarchicalChartOfAccountsReport(List<ChartOfAccount> accounts)
        {
            try
            {
                var report = new StringBuilder();
                report.AppendLine("=".PadRight(80, '='));
                report.AppendLine("تقرير دليل الحسابات الهرمي");
                report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                report.AppendLine($"عدد الحسابات: {accounts.Count}");
                report.AppendLine("=".PadRight(80, '='));
                report.AppendLine();

                // ترتيب الحسابات حسب الهيكل الهرمي
                var sortedAccounts = accounts
                    .OrderBy(a => a.AccountLevel)
                    .ThenBy(a => a.AccountPath)
                    .ThenBy(a => a.SortOrder)
                    .ThenBy(a => a.AccountCode)
                    .ToList();

                foreach (var account in sortedAccounts)
                {
                    var indent = new string(' ', (account.AccountLevel - 1) * 4);
                    var statusIcon = account.IsActive ? "✓" : "✗";
                    var parentIcon = account.IsParent ? "📁" : "📄";
                    
                    report.AppendLine($"{indent}{parentIcon} {statusIcon} {account.AccountCode} - {account.AccountNameAr}");
                    
                    if (!string.IsNullOrEmpty(account.Description))
                    {
                        report.AppendLine($"{indent}    الوصف: {account.Description}");
                    }
                    
                    if (account.CurrentBalance != 0)
                    {
                        report.AppendLine($"{indent}    الرصيد: {account.CurrentBalance:N2} {account.CurrencyCode}");
                    }
                    
                    report.AppendLine();
                }

                return ServiceResult<string>.Success(report.ToString(), "تم إنتاج التقرير بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في إنتاج التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// إنتاج تقرير ملخص الحسابات حسب النوع
        /// Generate Account Summary by Type Report
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>تقرير ملخص</returns>
        public ServiceResult<string> GenerateAccountSummaryByTypeReport(List<ChartOfAccount> accounts)
        {
            try
            {
                var report = new StringBuilder();
                report.AppendLine("=".PadRight(60, '='));
                report.AppendLine("تقرير ملخص الحسابات حسب النوع");
                report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                report.AppendLine("=".PadRight(60, '='));
                report.AppendLine();

                // تجميع الحسابات حسب النوع
                var accountsByType = accounts
                    .GroupBy(a => new { a.AccountTypeId, a.AccountTypeName })
                    .OrderBy(g => g.Key.AccountTypeId)
                    .ToList();

                decimal totalBalance = 0;

                foreach (var typeGroup in accountsByType)
                {
                    var typeName = typeGroup.Key.AccountTypeName ?? "غير محدد";
                    var typeAccounts = typeGroup.ToList();
                    var typeBalance = typeAccounts.Sum(a => a.CurrentBalance);
                    var activeCount = typeAccounts.Count(a => a.IsActive);
                    var inactiveCount = typeAccounts.Count(a => !a.IsActive);

                    report.AppendLine($"📊 {typeName}");
                    report.AppendLine($"   عدد الحسابات: {typeAccounts.Count} (نشط: {activeCount}, غير نشط: {inactiveCount})");
                    report.AppendLine($"   إجمالي الرصيد: {typeBalance:N2}");
                    report.AppendLine();

                    totalBalance += typeBalance;
                }

                report.AppendLine("-".PadRight(60, '-'));
                report.AppendLine($"إجمالي أرصدة جميع الحسابات: {totalBalance:N2}");
                report.AppendLine($"إجمالي عدد الحسابات: {accounts.Count}");

                return ServiceResult<string>.Success(report.ToString(), "تم إنتاج التقرير بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"خطأ في إنتاج التقرير: {ex.Message}");
            }
        }

        #endregion

        #region طرق مساعدة - Helper Methods

        /// <summary>
        /// إنتاج محتوى CSV لدليل الحسابات
        /// Generate CSV Content for Chart of Accounts
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <param name="includeDetails">تضمين التفاصيل</param>
        /// <returns>محتوى CSV</returns>
        private string GenerateChartOfAccountsCsv(List<ChartOfAccount> accounts, bool includeDetails)
        {
            var csv = new StringBuilder();
            
            // إضافة الترويسة
            if (includeDetails)
            {
                csv.AppendLine("رمز الحساب,اسم الحساب بالعربية,اسم الحساب بالإنجليزية,نوع الحساب,مجموعة الحساب,الحساب الأب,المستوى,هل أب,نشط,يسمح بالترحيل,العملة,الرصيد الافتتاحي,الرصيد الحالي,الطبيعة,الوصف,تاريخ الإنشاء");
            }
            else
            {
                csv.AppendLine("رمز الحساب,اسم الحساب,نوع الحساب,الرصيد الحالي,العملة,نشط");
            }

            // إضافة البيانات
            foreach (var account in accounts.OrderBy(a => a.AccountCode))
            {
                if (includeDetails)
                {
                    csv.AppendLine($"\"{account.AccountCode}\"," +
                                 $"\"{account.AccountNameAr}\"," +
                                 $"\"{account.AccountNameEn ?? ""}\"," +
                                 $"\"{account.AccountTypeName ?? ""}\"," +
                                 $"\"{account.AccountGroupName ?? ""}\"," +
                                 $"\"{account.ParentAccountName ?? ""}\"," +
                                 $"{account.AccountLevel}," +
                                 $"{(account.IsParent ? "نعم" : "لا")}," +
                                 $"{(account.IsActive ? "نشط" : "غير نشط")}," +
                                 $"{(account.AllowPosting ? "نعم" : "لا")}," +
                                 $"\"{account.CurrencyCode}\"," +
                                 $"{account.OpeningBalance}," +
                                 $"{account.CurrentBalance}," +
                                 $"\"{account.Nature}\"," +
                                 $"\"{account.Description ?? ""}\"," +
                                 $"{account.CreatedDate:yyyy/MM/dd}");
                }
                else
                {
                    csv.AppendLine($"\"{account.AccountCode}\"," +
                                 $"\"{account.AccountNameAr}\"," +
                                 $"\"{account.AccountTypeName ?? ""}\"," +
                                 $"{account.CurrentBalance}," +
                                 $"\"{account.CurrencyCode}\"," +
                                 $"{(account.IsActive ? "نشط" : "غير نشط")}");
                }
            }

            return csv.ToString();
        }

        /// <summary>
        /// إنتاج محتوى CSV لتقرير الأرصدة
        /// Generate CSV Content for Balances Report
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <param name="asOfDate">تاريخ التقرير</param>
        /// <returns>محتوى CSV</returns>
        private string GenerateAccountBalancesCsv(List<ChartOfAccount> accounts, DateTime asOfDate)
        {
            var csv = new StringBuilder();
            
            // إضافة معلومات التقرير
            csv.AppendLine($"تقرير أرصدة الحسابات كما في: {asOfDate:yyyy/MM/dd}");
            csv.AppendLine($"تاريخ إنتاج التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            csv.AppendLine($"عدد الحسابات: {accounts.Count}");
            csv.AppendLine();
            
            // إضافة الترويسة
            csv.AppendLine("رمز الحساب,اسم الحساب,نوع الحساب,الطبيعة,الرصيد الافتتاحي,الرصيد الحالي,الرصيد المدين,الرصيد الدائن,العملة,حالة الحساب");

            // إضافة البيانات
            decimal totalDebits = 0, totalCredits = 0;
            
            foreach (var account in accounts.OrderBy(a => a.AccountCode))
            {
                csv.AppendLine($"\"{account.AccountCode}\"," +
                             $"\"{account.AccountNameAr}\"," +
                             $"\"{account.AccountTypeName ?? ""}\"," +
                             $"\"{account.Nature}\"," +
                             $"{account.OpeningBalance}," +
                             $"{account.CurrentBalance}," +
                             $"{account.DebitBalance}," +
                             $"{account.CreditBalance}," +
                             $"\"{account.CurrencyCode}\"," +
                             $"{(account.IsActive ? "نشط" : "غير نشط")}");

                if (account.Nature == "مدين")
                    totalDebits += account.CurrentBalance;
                else
                    totalCredits += account.CurrentBalance;
            }

            // إضافة الإجماليات
            csv.AppendLine();
            csv.AppendLine("الإجماليات:");
            csv.AppendLine($"إجمالي المدين,{totalDebits}");
            csv.AppendLine($"إجمالي الدائن,{totalCredits}");
            csv.AppendLine($"الفرق,{Math.Abs(totalDebits - totalCredits)}");

            return csv.ToString();
        }

        #endregion

        #region تقارير متقدمة - Advanced Reports

        /// <summary>
        /// إنتاج تقرير التحليل المالي
        /// Generate Financial Analysis Report
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>تقرير التحليل المالي</returns>
        public ServiceResult<FinancialAnalysisReport> GenerateFinancialAnalysisReport(List<ChartOfAccount> accounts)
        {
            try
            {
                var report = new FinancialAnalysisReport
                {
                    ReportDate = DateTime.Now,
                    TotalAccounts = accounts.Count,
                    ActiveAccounts = accounts.Count(a => a.IsActive),
                    InactiveAccounts = accounts.Count(a => !a.IsActive),
                    ParentAccounts = accounts.Count(a => a.IsParent),
                    ChildAccounts = accounts.Count(a => !a.IsParent)
                };

                // حساب الأرصدة حسب النوع
                var assetAccounts = accounts.Where(a => a.AccountTypeId == 1).ToList();
                var liabilityAccounts = accounts.Where(a => a.AccountTypeId == 2).ToList();
                var equityAccounts = accounts.Where(a => a.AccountTypeId == 3).ToList();
                var revenueAccounts = accounts.Where(a => a.AccountTypeId == 4).ToList();
                var expenseAccounts = accounts.Where(a => a.AccountTypeId == 5).ToList();

                report.TotalAssets = assetAccounts.Sum(a => a.CurrentBalance);
                report.TotalLiabilities = liabilityAccounts.Sum(a => a.CurrentBalance);
                report.TotalEquity = equityAccounts.Sum(a => a.CurrentBalance);
                report.TotalRevenue = revenueAccounts.Sum(a => a.CurrentBalance);
                report.TotalExpenses = expenseAccounts.Sum(a => a.CurrentBalance);

                // حساب النسب المالية
                report.DebtToEquityRatio = report.TotalEquity != 0 ? report.TotalLiabilities / report.TotalEquity : 0;
                report.NetIncome = report.TotalRevenue - report.TotalExpenses;

                return ServiceResult<FinancialAnalysisReport>.Success(report, "تم إنتاج تقرير التحليل المالي بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<FinancialAnalysisReport>.Failure($"خطأ في إنتاج تقرير التحليل المالي: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// نموذج تقرير التحليل المالي
    /// Financial Analysis Report Model
    /// </summary>
    public class FinancialAnalysisReport
    {
        public DateTime ReportDate { get; set; }
        public int TotalAccounts { get; set; }
        public int ActiveAccounts { get; set; }
        public int InactiveAccounts { get; set; }
        public int ParentAccounts { get; set; }
        public int ChildAccounts { get; set; }
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
        public decimal TotalEquity { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal DebtToEquityRatio { get; set; }
    }
}
