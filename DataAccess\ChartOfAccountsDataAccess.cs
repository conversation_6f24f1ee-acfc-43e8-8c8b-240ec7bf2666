using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.DataAccess.Base;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بدليل الحسابات
    /// Chart of Accounts Data Access Layer
    /// </summary>
    public class ChartOfAccountsDataAccess : BaseDataAccess
    {
        #region Account CRUD Operations

        /// <summary>
        /// حفظ حساب جديد أو تحديث حساب موجود
        /// Save New Account or Update Existing Account
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>معرف الحساب المحفوظ</returns>
        public int SaveAccount(ChartOfAccount account)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountId", account.AccountId),
                    CreateParameter("@AccountCode", account.AccountCode, SqlDbType.NVarChar, 20),
                    CreateParameter("@AccountNameAr", account.AccountNameAr, SqlDbType.NVarChar, 200),
                    CreateParameter("@AccountNameEn", account.AccountNameEn, SqlDbType.NVarChar, 200),
                    CreateParameter("@AccountTypeId", account.AccountTypeId),
                    CreateParameter("@AccountGroupId", account.AccountGroupId),
                    CreateParameter("@ParentAccountId", ConvertToDb(account.ParentAccountId)),
                    CreateParameter("@CurrencyCode", account.CurrencyCode, SqlDbType.NVarChar, 3),
                    CreateParameter("@OpeningBalance", account.OpeningBalance),
                    CreateParameter("@AllowPosting", account.AllowPosting),
                    CreateParameter("@IsActive", account.IsActive),
                    CreateParameter("@Description", ConvertToDb(account.Description), SqlDbType.NVarChar, 500),
                    CreateParameter("@PersonalInfoId", ConvertToDb(account.PersonalInfoId)),
                    CreateParameter("@CreatedBy", account.CreatedBy, SqlDbType.NVarChar, 100),
                    CreateOutputParameter("@NewAccountId", SqlDbType.Int)
                };

                ExecuteStoredProcedure("SP_SaveAccount", parameters);
                
                // الحصول على معرف الحساب الجديد
                return ConvertFromDb<int>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "حفظ الحساب");
                throw new Exception(HandleException(ex, "حفظ الحساب"));
            }
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// Get Account by ID
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>بيانات الحساب</returns>
        public ChartOfAccount GetAccountById(int accountId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountId", accountId)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountById", parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على الحساب");
                throw new Exception(HandleException(ex, "الحصول على الحساب"));
            }
        }

        /// <summary>
        /// الحصول على حساب بالكود
        /// Get Account by Code
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <returns>بيانات الحساب</returns>
        public ChartOfAccount GetAccountByCode(string accountCode)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountCode", accountCode, SqlDbType.NVarChar, 20)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountByCode", parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على الحساب بالكود");
                throw new Exception(HandleException(ex, "الحصول على الحساب بالكود"));
            }
        }

        /// <summary>
        /// حذف حساب
        /// Delete Account
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="deletedBy">المستخدم الذي حذف الحساب</param>
        /// <returns>هل تم الحذف بنجاح</returns>
        public bool DeleteAccount(int accountId, string deletedBy)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountId", accountId),
                    CreateParameter("@DeletedBy", deletedBy, SqlDbType.NVarChar, 100)
                };

                int result = ExecuteStoredProcedure("SP_DeleteAccount", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "حذف الحساب");
                throw new Exception(HandleException(ex, "حذف الحساب"));
            }
        }

        #endregion

        #region Account Search and Listing

        /// <summary>
        /// البحث في الحسابات
        /// Search Accounts
        /// </summary>
        /// <param name="searchModel">نموذج البحث</param>
        /// <returns>نتائج البحث</returns>
        public AccountSearchResult SearchAccounts(AccountSearchModel searchModel)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@SearchText", ConvertToDb(searchModel.SearchText), SqlDbType.NVarChar, 200),
                    CreateParameter("@AccountTypeId", ConvertToDb(searchModel.AccountTypeId)),
                    CreateParameter("@AccountGroupId", ConvertToDb(searchModel.AccountGroupId)),
                    CreateParameter("@ParentAccountId", ConvertToDb(searchModel.ParentAccountId)),
                    CreateParameter("@IsActive", ConvertToDb(searchModel.IsActive)),
                    CreateParameter("@AllowPosting", ConvertToDb(searchModel.AllowPosting)),
                    CreateParameter("@PageNumber", searchModel.PageNumber),
                    CreateParameter("@PageSize", searchModel.PageSize),
                    CreateParameter("@SortBy", ConvertToDb(searchModel.SortBy), SqlDbType.NVarChar, 50),
                    CreateParameter("@SortDirection", ConvertToDb(searchModel.SortDirection), SqlDbType.NVarChar, 10),
                    CreateOutputParameter("@TotalRecords", SqlDbType.Int)
                };

                var accounts = new List<ChartOfAccount>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_SearchAccounts", parameters))
                {
                    while (reader.Read())
                    {
                        accounts.Add(MapAccountFromReader(reader));
                    }
                }

                int totalRecords = ConvertFromDb<int>(parameters[parameters.Length - 1].Value);

                return new AccountSearchResult
                {
                    Accounts = accounts,
                    TotalRecords = totalRecords,
                    PageNumber = searchModel.PageNumber,
                    PageSize = searchModel.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalRecords / searchModel.PageSize)
                };
            }
            catch (Exception ex)
            {
                LogError(ex, "البحث في الحسابات");
                throw new Exception(HandleException(ex, "البحث في الحسابات"));
            }
        }

        /// <summary>
        /// الحصول على الحسابات الهرمية
        /// Get Accounts Hierarchy
        /// </summary>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>قائمة الحسابات الهرمية</returns>
        public List<AccountTreeNode> GetAccountsHierarchy(int? parentAccountId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@ParentAccountId", ConvertToDb(parentAccountId))
                };

                var treeNodes = new List<AccountTreeNode>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetAccountsHierarchy", parameters))
                {
                    while (reader.Read())
                    {
                        treeNodes.Add(MapTreeNodeFromReader(reader));
                    }
                }

                return treeNodes;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على الحسابات الهرمية");
                throw new Exception(HandleException(ex, "الحصول على الحسابات الهرمية"));
            }
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية
        /// Get Child Accounts
        /// </summary>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>قائمة الحسابات الفرعية</returns>
        public List<ChartOfAccount> GetChildAccounts(int parentAccountId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@ParentAccountId", parentAccountId)
                };

                var accounts = new List<ChartOfAccount>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_GetChildAccounts", parameters))
                {
                    while (reader.Read())
                    {
                        accounts.Add(MapAccountFromReader(reader));
                    }
                }

                return accounts;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على الحسابات الفرعية");
                throw new Exception(HandleException(ex, "الحصول على الحسابات الفرعية"));
            }
        }

        #endregion

        #region Account Code Generation

        /// <summary>
        /// توليد كود حساب جديد
        /// Generate New Account Code
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="accountGroupId">معرف مجموعة الحساب</param>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>كود الحساب الجديد</returns>
        public string GenerateAccountCode(int accountTypeId, int accountGroupId, int? parentAccountId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@AccountTypeId", accountTypeId),
                    CreateParameter("@AccountGroupId", accountGroupId),
                    CreateParameter("@ParentAccountId", ConvertToDb(parentAccountId)),
                    CreateOutputParameter("@NewAccountCode", SqlDbType.NVarChar, 20)
                };

                ExecuteStoredProcedure("SP_GenerateAccountCode", parameters);
                
                return ConvertFromDb<string>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "توليد كود الحساب");
                throw new Exception(HandleException(ex, "توليد كود الحساب"));
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن حساب
        /// Map Data Reader to Account Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الحساب</returns>
        private ChartOfAccount MapAccountFromReader(SqlDataReader reader)
        {
            return new ChartOfAccount
            {
                AccountId = ConvertFromDb<int>(reader["AccountId"]),
                AccountCode = ConvertFromDb<string>(reader["AccountCode"]),
                AccountNameAr = ConvertFromDb<string>(reader["AccountNameAr"]),
                AccountNameEn = ConvertFromDb<string>(reader["AccountNameEn"]),
                AccountTypeId = ConvertFromDb<int>(reader["AccountTypeId"]),
                AccountGroupId = ConvertFromDb<int>(reader["AccountGroupId"]),
                ParentAccountId = ConvertFromDb<int?>(reader["ParentAccountId"]),
                AccountLevel = ConvertFromDb<int>(reader["AccountLevel"]),
                AccountPath = ConvertFromDb<string>(reader["AccountPath"]),
                IsParent = ConvertFromDb<bool>(reader["IsParent"]),
                CurrencyCode = ConvertFromDb<string>(reader["CurrencyCode"]),
                OpeningBalance = ConvertFromDb<decimal>(reader["OpeningBalance"]),
                CurrentBalance = ConvertFromDb<decimal>(reader["CurrentBalance"]),
                AllowPosting = ConvertFromDb<bool>(reader["AllowPosting"]),
                IsActive = ConvertFromDb<bool>(reader["IsActive"]),
                Description = ConvertFromDb<string>(reader["Description"]),
                PersonalInfoId = ConvertFromDb<int?>(reader["PersonalInfoId"]),
                CreatedDate = ConvertFromDb<DateTime>(reader["CreatedDate"]),
                CreatedBy = ConvertFromDb<string>(reader["CreatedBy"]),
                ModifiedDate = ConvertFromDb<DateTime?>(reader["ModifiedDate"]),
                ModifiedBy = ConvertFromDb<string>(reader["ModifiedBy"])
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى عقدة شجرة
        /// Map Data Reader to Tree Node
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>عقدة الشجرة</returns>
        private AccountTreeNode MapTreeNodeFromReader(SqlDataReader reader)
        {
            return new AccountTreeNode
            {
                AccountId = ConvertFromDb<int>(reader["AccountId"]),
                AccountCode = ConvertFromDb<string>(reader["AccountCode"]),
                AccountNameAr = ConvertFromDb<string>(reader["AccountNameAr"]),
                AccountNameEn = ConvertFromDb<string>(reader["AccountNameEn"]),
                ParentAccountId = ConvertFromDb<int?>(reader["ParentAccountId"]),
                AccountLevel = ConvertFromDb<int>(reader["AccountLevel"]),
                IsParent = ConvertFromDb<bool>(reader["IsParent"]),
                IsActive = ConvertFromDb<bool>(reader["IsActive"]),
                AllowPosting = ConvertFromDb<bool>(reader["AllowPosting"]),
                CurrentBalance = ConvertFromDb<decimal>(reader["CurrentBalance"]),
                ChildrenCount = ConvertFromDb<int>(reader["ChildrenCount"])
            };
        }

        #endregion
    }
}
