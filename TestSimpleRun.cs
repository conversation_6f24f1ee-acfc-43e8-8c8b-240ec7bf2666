using System;
using System.Windows.Forms;
using Awqaf_Managment.UI.Forms;

namespace Awqaf_Managment
{
    /// <summary>
    /// اختبار تشغيل مبسط للنظام
    /// Simple System Test Run
    /// </summary>
    public class TestSimpleRun
    {
        /// <summary>
        /// نقطة دخول الاختبار
        /// Test Entry Point
        /// </summary>
        [STAThread]
        public static void Main()
        {
            try
            {
                // إعداد التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // تشغيل النموذج الاختباري
                Application.Run(new TestMainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", 
                    "خطأ نظام", MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }
    }
}
