using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نماذج مساعدة لدليل الحسابات
    /// Helper Models for Chart of Accounts
    /// </summary>

    /// <summary>
    /// نموذج البحث في الحسابات
    /// Account Search Model
    /// </summary>
    public class AccountSearchModel
    {
        /// <summary>
        /// نص البحث
        /// Search Text
        /// </summary>
        public string SearchText { get; set; }

        /// <summary>
        /// نوع الحساب للفلترة
        /// Account Type for Filtering
        /// </summary>
        public int? AccountTypeId { get; set; }

        /// <summary>
        /// مجموعة الحساب للفلترة
        /// Account Group for Filtering
        /// </summary>
        public int? AccountGroupId { get; set; }

        /// <summary>
        /// الحساب الأب للفلترة
        /// Parent Account for Filtering
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// فلترة الحسابات النشطة فقط
        /// Filter Active Accounts Only
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// فلترة الحسابات التي تسمح بالترحيل
        /// Filter Accounts that Allow Posting
        /// </summary>
        public bool? AllowPosting { get; set; }

        /// <summary>
        /// فلترة الحسابات الأب
        /// Filter Parent Accounts
        /// </summary>
        public bool? IsParent { get; set; }

        /// <summary>
        /// رقم الصفحة
        /// Page Number
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// حجم الصفحة
        /// Page Size
        /// </summary>
        public int PageSize { get; set; } = 50;

        /// <summary>
        /// حقل الترتيب
        /// Sort Field
        /// </summary>
        public string SortField { get; set; } = "AccountCode";

        /// <summary>
        /// اتجاه الترتيب
        /// Sort Direction
        /// </summary>
        public string SortDirection { get; set; } = "ASC";
    }

    /// <summary>
    /// نموذج نتائج البحث
    /// Search Results Model
    /// </summary>
    public class AccountSearchResult
    {
        /// <summary>
        /// قائمة الحسابات
        /// List of Accounts
        /// </summary>
        public List<ChartOfAccount> Accounts { get; set; } = new List<ChartOfAccount>();

        /// <summary>
        /// العدد الإجمالي للنتائج
        /// Total Count of Results
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// رقم الصفحة الحالية
        /// Current Page Number
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// حجم الصفحة
        /// Page Size
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// العدد الإجمالي للصفحات
        /// Total Number of Pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// هل توجد صفحة سابقة
        /// Has Previous Page
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// هل توجد صفحة تالية
        /// Has Next Page
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    /// <summary>
    /// نموذج عقدة الشجرة الهرمية
    /// Hierarchical Tree Node Model
    /// </summary>
    public class AccountTreeNode
    {
        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رمز الحساب
        /// Account Code
        /// </summary>
        public string AccountCode { get; set; }

        /// <summary>
        /// اسم الحساب
        /// Account Name
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// مستوى الحساب
        /// Account Level
        /// </summary>
        public int AccountLevel { get; set; }

        /// <summary>
        /// هل الحساب أب
        /// Is Parent Account
        /// </summary>
        public bool IsParent { get; set; }

        /// <summary>
        /// هل الحساب نشط
        /// Is Account Active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// الرصيد الحالي
        /// Current Balance
        /// </summary>
        public decimal CurrentBalance { get; set; }

        /// <summary>
        /// رمز العملة
        /// Currency Code
        /// </summary>
        public string CurrencyCode { get; set; }

        /// <summary>
        /// الحسابات الفرعية
        /// Child Accounts
        /// </summary>
        public List<AccountTreeNode> Children { get; set; } = new List<AccountTreeNode>();

        /// <summary>
        /// هل العقدة موسعة
        /// Is Node Expanded
        /// </summary>
        public bool IsExpanded { get; set; } = false;

        /// <summary>
        /// هل العقدة محددة
        /// Is Node Selected
        /// </summary>
        public bool IsSelected { get; set; } = false;
    }

    /// <summary>
    /// نموذج إحصائيات الحسابات
    /// Account Statistics Model
    /// </summary>
    public class AccountStatistics
    {
        /// <summary>
        /// العدد الإجمالي للحسابات
        /// Total Number of Accounts
        /// </summary>
        public int TotalAccounts { get; set; }

        /// <summary>
        /// عدد الحسابات النشطة
        /// Number of Active Accounts
        /// </summary>
        public int ActiveAccounts { get; set; }

        /// <summary>
        /// عدد الحسابات غير النشطة
        /// Number of Inactive Accounts
        /// </summary>
        public int InactiveAccounts { get; set; }

        /// <summary>
        /// عدد الحسابات الأب
        /// Number of Parent Accounts
        /// </summary>
        public int ParentAccounts { get; set; }

        /// <summary>
        /// عدد الحسابات الفرعية
        /// Number of Child Accounts
        /// </summary>
        public int ChildAccounts { get; set; }

        /// <summary>
        /// عدد الحسابات التي تسمح بالترحيل
        /// Number of Accounts that Allow Posting
        /// </summary>
        public int PostingAccounts { get; set; }

        /// <summary>
        /// إحصائيات حسب نوع الحساب
        /// Statistics by Account Type
        /// </summary>
        public Dictionary<string, int> AccountsByType { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// إحصائيات حسب مجموعة الحساب
        /// Statistics by Account Group
        /// </summary>
        public Dictionary<string, int> AccountsByGroup { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// إحصائيات حسب العملة
        /// Statistics by Currency
        /// </summary>
        public Dictionary<string, int> AccountsByCurrency { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// نموذج تصدير الحسابات
    /// Account Export Model
    /// </summary>
    public class AccountExportModel
    {
        /// <summary>
        /// رمز الحساب
        /// Account Code
        /// </summary>
        public string AccountCode { get; set; }

        /// <summary>
        /// اسم الحساب بالعربية
        /// Account Name in Arabic
        /// </summary>
        public string AccountNameAr { get; set; }

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// Account Name in English
        /// </summary>
        public string AccountNameEn { get; set; }

        /// <summary>
        /// نوع الحساب
        /// Account Type
        /// </summary>
        public string AccountType { get; set; }

        /// <summary>
        /// مجموعة الحساب
        /// Account Group
        /// </summary>
        public string AccountGroup { get; set; }

        /// <summary>
        /// الحساب الأب
        /// Parent Account
        /// </summary>
        public string ParentAccount { get; set; }

        /// <summary>
        /// مستوى الحساب
        /// Account Level
        /// </summary>
        public int AccountLevel { get; set; }

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public string IsActive { get; set; }

        /// <summary>
        /// السماح بالترحيل
        /// Allow Posting
        /// </summary>
        public string AllowPosting { get; set; }

        /// <summary>
        /// العملة
        /// Currency
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// Opening Balance
        /// </summary>
        public decimal OpeningBalance { get; set; }

        /// <summary>
        /// الرصيد الحالي
        /// Current Balance
        /// </summary>
        public decimal CurrentBalance { get; set; }

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; }
    }

    /// <summary>
    /// نموذج استيراد الحسابات
    /// Account Import Model
    /// </summary>
    public class AccountImportModel
    {
        /// <summary>
        /// رقم الصف
        /// Row Number
        /// </summary>
        public int RowNumber { get; set; }

        /// <summary>
        /// رمز الحساب
        /// Account Code
        /// </summary>
        public string AccountCode { get; set; }

        /// <summary>
        /// اسم الحساب بالعربية
        /// Account Name in Arabic
        /// </summary>
        public string AccountNameAr { get; set; }

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// Account Name in English
        /// </summary>
        public string AccountNameEn { get; set; }

        /// <summary>
        /// نوع الحساب
        /// Account Type
        /// </summary>
        public string AccountType { get; set; }

        /// <summary>
        /// مجموعة الحساب
        /// Account Group
        /// </summary>
        public string AccountGroup { get; set; }

        /// <summary>
        /// رمز الحساب الأب
        /// Parent Account Code
        /// </summary>
        public string ParentAccountCode { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// Opening Balance
        /// </summary>
        public decimal? OpeningBalance { get; set; }

        /// <summary>
        /// العملة
        /// Currency
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// رسائل الخطأ
        /// Error Messages
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// هل الصف صحيح
        /// Is Row Valid
        /// </summary>
        public bool IsValid => Errors.Count == 0;
    }
}
