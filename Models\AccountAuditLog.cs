using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج سجل تعديلات الحسابات
    /// Account Audit Log Model
    /// </summary>
    public class AccountAuditLog
    {
        /// <summary>
        /// معرف سجل التعديل
        /// Audit Log ID
        /// </summary>
        public int AuditId { get; set; }

        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        [Required(ErrorMessage = "معرف الحساب مطلوب")]
        public int AccountId { get; set; }

        /// <summary>
        /// نوع العملية
        /// Operation Type
        /// </summary>
        [Required(ErrorMessage = "نوع العملية مطلوب")]
        [StringLength(20, ErrorMessage = "نوع العملية يجب ألا يزيد عن 20 حرف")]
        public string OperationType { get; set; }

        /// <summary>
        /// اسم الحقل المعدل
        /// Modified Field Name
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الحقل يجب ألا يزيد عن 100 حرف")]
        public string FieldName { get; set; }

        /// <summary>
        /// القيمة القديمة
        /// Old Value
        /// </summary>
        [StringLength(1000, ErrorMessage = "القيمة القديمة يجب ألا تزيد عن 1000 حرف")]
        public string OldValue { get; set; }

        /// <summary>
        /// القيمة الجديدة
        /// New Value
        /// </summary>
        [StringLength(1000, ErrorMessage = "القيمة الجديدة يجب ألا تزيد عن 1000 حرف")]
        public string NewValue { get; set; }

        /// <summary>
        /// تاريخ ووقت التعديل
        /// Modification Date and Time
        /// </summary>
        [Required(ErrorMessage = "تاريخ التعديل مطلوب")]
        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم الذي قام بالتعديل
        /// User who made the modification
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المستخدم يجب ألا يزيد عن 100 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// عنوان IP للمستخدم
        /// User's IP Address
        /// </summary>
        [StringLength(50, ErrorMessage = "عنوان IP يجب ألا يزيد عن 50 حرف")]
        public string IpAddress { get; set; }

        /// <summary>
        /// اسم الجهاز
        /// Machine Name
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الجهاز يجب ألا يزيد عن 100 حرف")]
        public string MachineName { get; set; }

        /// <summary>
        /// معلومات المتصفح/التطبيق
        /// Browser/Application Information
        /// </summary>
        [StringLength(500, ErrorMessage = "معلومات المتصفح يجب ألا تزيد عن 500 حرف")]
        public string UserAgent { get; set; }

        /// <summary>
        /// معرف الجلسة
        /// Session ID
        /// </summary>
        [StringLength(100, ErrorMessage = "معرف الجلسة يجب ألا يزيد عن 100 حرف")]
        public string SessionId { get; set; }

        /// <summary>
        /// سبب التغيير
        /// Reason for Change
        /// </summary>
        [StringLength(500, ErrorMessage = "سبب التغيير يجب ألا يزيد عن 500 حرف")]
        public string Reason { get; set; }

        // Navigation Properties
        /// <summary>
        /// الحساب المرتبط
        /// Related Account
        /// </summary>
        public ChartOfAccount Account { get; set; }

        /// <summary>
        /// الحصول على وصف نوع العملية بالعربية
        /// Get operation type description in Arabic
        /// </summary>
        /// <returns>وصف نوع العملية</returns>
        public string GetOperationTypeArabic()
        {
            return OperationType switch
            {
                "INSERT" => "إضافة",
                "UPDATE" => "تعديل",
                "DELETE" => "حذف",
                "SELECT" => "استعلام",
                "ACTIVATE" => "تفعيل",
                "DEACTIVATE" => "إلغاء تفعيل",
                _ => OperationType
            };
        }

        /// <summary>
        /// الحصول على وصف الحقل بالعربية
        /// Get field description in Arabic
        /// </summary>
        /// <returns>وصف الحقل</returns>
        public string GetFieldNameArabic()
        {
            return FieldName switch
            {
                "AccountCode" => "رمز الحساب",
                "AccountName" => "اسم الحساب",
                "AccountNameEn" => "اسم الحساب بالإنجليزية",
                "AccountTypeId" => "نوع الحساب",
                "AccountGroupId" => "مجموعة الحساب",
                "ParentAccountId" => "الحساب الأب",
                "IsActive" => "حالة النشاط",
                "AllowPosting" => "السماح بالترحيل",
                "Description" => "الوصف",
                "DescriptionEn" => "الوصف بالإنجليزية",
                "CurrencyId" => "العملة",
                "OpeningBalance" => "الرصيد الافتتاحي",
                "OpeningBalanceDate" => "تاريخ الرصيد الافتتاحي",
                "PersonalInfoId" => "المعلومات الشخصية",
                "SortOrder" => "ترتيب العرض",
                _ => FieldName
            };
        }

        /// <summary>
        /// الحصول على ملخص التغيير
        /// Get change summary
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>ملخص التغيير</returns>
        public string GetChangeSummary(bool isArabic = true)
        {
            var operationType = isArabic ? GetOperationTypeArabic() : OperationType;
            var fieldName = isArabic ? GetFieldNameArabic() : FieldName;

            if (OperationType == "INSERT")
            {
                return isArabic ? $"تم إضافة حساب جديد" : "New account added";
            }
            else if (OperationType == "DELETE")
            {
                return isArabic ? $"تم حذف الحساب" : "Account deleted";
            }
            else if (OperationType == "UPDATE" && !string.IsNullOrWhiteSpace(FieldName))
            {
                if (isArabic)
                    return $"تم تعديل {fieldName} من '{OldValue}' إلى '{NewValue}'";
                else
                    return $"Modified {fieldName} from '{OldValue}' to '{NewValue}'";
            }

            return $"{operationType} - {fieldName}";
        }

        /// <summary>
        /// الحصول على معلومات التعديل الكاملة
        /// Get full modification information
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>معلومات التعديل الكاملة</returns>
        public string GetFullModificationInfo(bool isArabic = true)
        {
            var summary = GetChangeSummary(isArabic);
            var dateStr = ModifiedDate.ToString("yyyy-MM-dd HH:mm:ss");
            
            if (isArabic)
                return $"{summary} بواسطة {ModifiedBy} في {dateStr}";
            else
                return $"{summary} by {ModifiedBy} on {dateStr}";
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (AccountId <= 0)
                return "معرف الحساب مطلوب";

            if (string.IsNullOrWhiteSpace(OperationType))
                return "نوع العملية مطلوب";

            if (string.IsNullOrWhiteSpace(ModifiedBy))
                return "اسم المستخدم مطلوب";

            if (ModifiedDate == default(DateTime))
                return "تاريخ التعديل مطلوب";

            // التحقق من صحة نوع العملية
            var validOperations = new[] { "INSERT", "UPDATE", "DELETE", "SELECT", "ACTIVATE", "DEACTIVATE" };
            if (!Array.Exists(validOperations, op => op == OperationType))
                return "نوع العملية غير صحيح";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// إنشاء سجل تعديل جديد
        /// Create new audit log entry
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="operationType">نوع العملية</param>
        /// <param name="modifiedBy">المستخدم</param>
        /// <param name="fieldName">اسم الحقل</param>
        /// <param name="oldValue">القيمة القديمة</param>
        /// <param name="newValue">القيمة الجديدة</param>
        /// <param name="reason">سبب التغيير</param>
        /// <returns>سجل التعديل الجديد</returns>
        public static AccountAuditLog CreateAuditEntry(
            int accountId,
            string operationType,
            string modifiedBy,
            string fieldName = null,
            string oldValue = null,
            string newValue = null,
            string reason = null)
        {
            return new AccountAuditLog
            {
                AccountId = accountId,
                OperationType = operationType,
                FieldName = fieldName,
                OldValue = oldValue,
                NewValue = newValue,
                ModifiedDate = DateTime.Now,
                ModifiedBy = modifiedBy,
                IpAddress = GetCurrentIpAddress(),
                MachineName = Environment.MachineName,
                SessionId = Guid.NewGuid().ToString(),
                Reason = reason
            };
        }

        /// <summary>
        /// الحصول على عنوان IP الحالي
        /// Get current IP address
        /// </summary>
        /// <returns>عنوان IP</returns>
        private static string GetCurrentIpAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return GetFullModificationInfo(true);
        }
    }
}
