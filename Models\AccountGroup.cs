using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج مجموعات الحسابات
    /// Account Groups Model
    /// </summary>
    public class AccountGroup
    {
        /// <summary>
        /// معرف مجموعة الحساب
        /// Account Group ID
        /// </summary>
        public int AccountGroupId { get; set; }

        /// <summary>
        /// رمز مجموعة الحساب
        /// Group Code
        /// </summary>
        [Required(ErrorMessage = "رمز مجموعة الحساب مطلوب")]
        [StringLength(10, ErrorMessage = "رمز مجموعة الحساب يجب ألا يزيد عن 10 أحرف")]
        public string GroupCode { get; set; }

        /// <summary>
        /// اسم مجموعة الحساب بالإنجليزية
        /// Group Name in English
        /// </summary>
        [Required(ErrorMessage = "اسم مجموعة الحساب بالإنجليزية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم مجموعة الحساب يجب ألا يزيد عن 100 حرف")]
        public string GroupName { get; set; }

        /// <summary>
        /// اسم مجموعة الحساب بالعربية
        /// Group Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "اسم مجموعة الحساب بالعربية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم مجموعة الحساب يجب ألا يزيد عن 100 حرف")]
        public string GroupNameAr { get; set; }

        /// <summary>
        /// وصف مجموعة الحساب بالإنجليزية
        /// Group Description in English
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف مجموعة الحساب يجب ألا يزيد عن 500 حرف")]
        public string GroupDescription { get; set; }

        /// <summary>
        /// وصف مجموعة الحساب بالعربية
        /// Group Description in Arabic
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف مجموعة الحساب يجب ألا يزيد عن 500 حرف")]
        public string GroupDescriptionAr { get; set; }

        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public int AccountTypeId { get; set; }

        /// <summary>
        /// معرف المجموعة الأب
        /// Parent Group ID
        /// </summary>
        public int? ParentGroupId { get; set; }

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// Display Order
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// منشئ السجل
        /// Created By
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل السجل
        /// Modified By
        /// </summary>
        [StringLength(100)]
        public string ModifiedBy { get; set; }

        // Navigation Properties
        /// <summary>
        /// نوع الحساب المرتبط
        /// Related Account Type
        /// </summary>
        public AccountType AccountType { get; set; }

        /// <summary>
        /// المجموعة الأب
        /// Parent Group
        /// </summary>
        public AccountGroup ParentGroup { get; set; }

        /// <summary>
        /// الحصول على اسم مجموعة الحساب حسب اللغة
        /// Get group name based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>اسم مجموعة الحساب</returns>
        public string GetDisplayName(bool isArabic = true)
        {
            return isArabic ? GroupNameAr : GroupName;
        }

        /// <summary>
        /// الحصول على وصف مجموعة الحساب حسب اللغة
        /// Get group description based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>وصف مجموعة الحساب</returns>
        public string GetDisplayDescription(bool isArabic = true)
        {
            return isArabic ? GroupDescriptionAr : GroupDescription;
        }

        /// <summary>
        /// الحصول على المسار الكامل للمجموعة
        /// Get full path of the group
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>المسار الكامل</returns>
        public string GetFullPath(bool isArabic = true)
        {
            var currentName = GetDisplayName(isArabic);
            
            if (ParentGroup != null)
            {
                return $"{ParentGroup.GetFullPath(isArabic)} > {currentName}";
            }
            
            return currentName;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (string.IsNullOrWhiteSpace(GroupCode))
                return "رمز مجموعة الحساب مطلوب";

            if (string.IsNullOrWhiteSpace(GroupName))
                return "اسم مجموعة الحساب بالإنجليزية مطلوب";

            if (string.IsNullOrWhiteSpace(GroupNameAr))
                return "اسم مجموعة الحساب بالعربية مطلوب";

            if (AccountTypeId <= 0)
                return "نوع الحساب مطلوب";

            // التحقق من عدم وجود مرجع دائري
            if (ParentGroupId.HasValue && ParentGroupId.Value == AccountGroupId)
                return "لا يمكن أن تكون المجموعة أب لنفسها";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// نسخ البيانات من نموذج آخر
        /// Copy data from another model
        /// </summary>
        /// <param name="source">النموذج المصدر</param>
        public void CopyFrom(AccountGroup source)
        {
            if (source == null) return;

            GroupCode = source.GroupCode;
            GroupName = source.GroupName;
            GroupNameAr = source.GroupNameAr;
            GroupDescription = source.GroupDescription;
            GroupDescriptionAr = source.GroupDescriptionAr;
            AccountTypeId = source.AccountTypeId;
            ParentGroupId = source.ParentGroupId;
            IsActive = source.IsActive;
            DisplayOrder = source.DisplayOrder;
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return $"{GroupCode} - {GroupNameAr} ({GroupName})";
        }
    }
}
