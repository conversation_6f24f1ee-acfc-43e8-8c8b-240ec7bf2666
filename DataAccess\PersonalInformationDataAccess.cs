using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.DataAccess.Base;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بالمعلومات الشخصية
    /// Personal Information Data Access Layer
    /// </summary>
    public class PersonalInformationDataAccess : BaseDataAccess
    {
        #region Personal Information CRUD Operations

        /// <summary>
        /// حفظ معلومات شخصية جديدة أو تحديث موجودة
        /// Save New Personal Information or Update Existing
        /// </summary>
        /// <param name="personalInfo">بيانات المعلومات الشخصية</param>
        /// <returns>معرف المعلومات الشخصية المحفوظة</returns>
        public int SavePersonalInformation(PersonalInformation personalInfo)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@PersonalInfoId", personalInfo.PersonalInfoId),
                    CreateParameter("@FullNameAr", personalInfo.FullNameAr, SqlDbType.NVarChar, 200),
                    CreateParameter("@FullNameEn", personalInfo.FullNameEn, SqlDbType.NVarChar, 200),
                    CreateParameter("@NationalId", ConvertToDb(personalInfo.NationalId), SqlDbType.NVarChar, 20),
                    CreateParameter("@PassportNumber", ConvertToDb(personalInfo.PassportNumber), SqlDbType.NVarChar, 20),
                    CreateParameter("@DateOfBirth", ConvertToDb(personalInfo.DateOfBirth)),
                    CreateParameter("@Gender", ConvertToDb(personalInfo.Gender), SqlDbType.NVarChar, 10),
                    CreateParameter("@Nationality", ConvertToDb(personalInfo.Nationality), SqlDbType.NVarChar, 50),
                    CreateParameter("@Phone1", ConvertToDb(personalInfo.Phone1), SqlDbType.NVarChar, 20),
                    CreateParameter("@Phone2", ConvertToDb(personalInfo.Phone2), SqlDbType.NVarChar, 20),
                    CreateParameter("@Email", ConvertToDb(personalInfo.Email), SqlDbType.NVarChar, 100),
                    CreateParameter("@Address", ConvertToDb(personalInfo.Address), SqlDbType.NVarChar, 500),
                    CreateParameter("@City", ConvertToDb(personalInfo.City), SqlDbType.NVarChar, 100),
                    CreateParameter("@Country", ConvertToDb(personalInfo.Country), SqlDbType.NVarChar, 100),
                    CreateParameter("@PostalCode", ConvertToDb(personalInfo.PostalCode), SqlDbType.NVarChar, 20),
                    CreateParameter("@Notes", ConvertToDb(personalInfo.Notes), SqlDbType.NVarChar, 1000),
                    CreateParameter("@IsActive", personalInfo.IsActive),
                    CreateParameter("@CreatedBy", personalInfo.CreatedBy, SqlDbType.NVarChar, 100),
                    CreateOutputParameter("@NewPersonalInfoId", SqlDbType.Int)
                };

                ExecuteStoredProcedure("SP_SavePersonalInformation", parameters);
                
                return ConvertFromDb<int>(parameters[parameters.Length - 1].Value);
            }
            catch (Exception ex)
            {
                LogError(ex, "حفظ المعلومات الشخصية");
                throw new Exception(HandleException(ex, "حفظ المعلومات الشخصية"));
            }
        }

        /// <summary>
        /// الحصول على معلومات شخصية بالمعرف
        /// Get Personal Information by ID
        /// </summary>
        /// <param name="personalInfoId">معرف المعلومات الشخصية</param>
        /// <returns>بيانات المعلومات الشخصية</returns>
        public PersonalInformation GetPersonalInformationById(int personalInfoId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@PersonalInfoId", personalInfoId)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetPersonalInformationById", parameters))
                {
                    if (reader.Read())
                    {
                        return MapPersonalInformationFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على المعلومات الشخصية");
                throw new Exception(HandleException(ex, "الحصول على المعلومات الشخصية"));
            }
        }

        /// <summary>
        /// الحصول على معلومات شخصية بالهوية الوطنية
        /// Get Personal Information by National ID
        /// </summary>
        /// <param name="nationalId">رقم الهوية الوطنية</param>
        /// <returns>بيانات المعلومات الشخصية</returns>
        public PersonalInformation GetPersonalInformationByNationalId(string nationalId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@NationalId", nationalId, SqlDbType.NVarChar, 20)
                };

                using (var reader = ExecuteStoredProcedureReader("SP_GetPersonalInformationByNationalId", parameters))
                {
                    if (reader.Read())
                    {
                        return MapPersonalInformationFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "الحصول على المعلومات الشخصية بالهوية");
                throw new Exception(HandleException(ex, "الحصول على المعلومات الشخصية بالهوية"));
            }
        }

        /// <summary>
        /// البحث في المعلومات الشخصية
        /// Search Personal Information
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <param name="isActive">حالة النشاط</param>
        /// <returns>قائمة المعلومات الشخصية</returns>
        public List<PersonalInformation> SearchPersonalInformation(string searchText = null, bool? isActive = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@SearchText", ConvertToDb(searchText), SqlDbType.NVarChar, 200),
                    CreateParameter("@IsActive", ConvertToDb(isActive))
                };

                var personalInfoList = new List<PersonalInformation>();
                
                using (var reader = ExecuteStoredProcedureReader("SP_SearchPersonalInformation", parameters))
                {
                    while (reader.Read())
                    {
                        personalInfoList.Add(MapPersonalInformationFromReader(reader));
                    }
                }

                return personalInfoList;
            }
            catch (Exception ex)
            {
                LogError(ex, "البحث في المعلومات الشخصية");
                throw new Exception(HandleException(ex, "البحث في المعلومات الشخصية"));
            }
        }

        /// <summary>
        /// حذف معلومات شخصية
        /// Delete Personal Information
        /// </summary>
        /// <param name="personalInfoId">معرف المعلومات الشخصية</param>
        /// <returns>هل تم الحذف بنجاح</returns>
        public bool DeletePersonalInformation(int personalInfoId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@PersonalInfoId", personalInfoId)
                };

                int result = ExecuteStoredProcedure("SP_DeletePersonalInformation", parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                LogError(ex, "حذف المعلومات الشخصية");
                throw new Exception(HandleException(ex, "حذف المعلومات الشخصية"));
            }
        }

        #endregion

        #region Personal Information Validation

        /// <summary>
        /// التحقق من وجود رقم الهوية الوطنية
        /// Check if National ID Exists
        /// </summary>
        /// <param name="nationalId">رقم الهوية الوطنية</param>
        /// <param name="excludeId">معرف المعلومات الشخصية المستثناة من التحقق</param>
        /// <returns>هل رقم الهوية موجود</returns>
        public bool IsNationalIdExists(string nationalId, int? excludeId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@NationalId", nationalId, SqlDbType.NVarChar, 20),
                    CreateParameter("@ExcludeId", ConvertToDb(excludeId))
                };

                var result = ExecuteStoredProcedureScalar("SP_CheckNationalIdExists", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من رقم الهوية الوطنية");
                throw new Exception(HandleException(ex, "التحقق من رقم الهوية الوطنية"));
            }
        }

        /// <summary>
        /// التحقق من وجود رقم جواز السفر
        /// Check if Passport Number Exists
        /// </summary>
        /// <param name="passportNumber">رقم جواز السفر</param>
        /// <param name="excludeId">معرف المعلومات الشخصية المستثناة من التحقق</param>
        /// <returns>هل رقم جواز السفر موجود</returns>
        public bool IsPassportNumberExists(string passportNumber, int? excludeId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@PassportNumber", passportNumber, SqlDbType.NVarChar, 20),
                    CreateParameter("@ExcludeId", ConvertToDb(excludeId))
                };

                var result = ExecuteStoredProcedureScalar("SP_CheckPassportNumberExists", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من رقم جواز السفر");
                throw new Exception(HandleException(ex, "التحقق من رقم جواز السفر"));
            }
        }

        /// <summary>
        /// التحقق من وجود البريد الإلكتروني
        /// Check if Email Exists
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <param name="excludeId">معرف المعلومات الشخصية المستثناة من التحقق</param>
        /// <returns>هل البريد الإلكتروني موجود</returns>
        public bool IsEmailExists(string email, int? excludeId = null)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@Email", email, SqlDbType.NVarChar, 100),
                    CreateParameter("@ExcludeId", ConvertToDb(excludeId))
                };

                var result = ExecuteStoredProcedureScalar("SP_CheckEmailExists", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من البريد الإلكتروني");
                throw new Exception(HandleException(ex, "التحقق من البريد الإلكتروني"));
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف المعلومات الشخصية
        /// Check if Personal Information Can Be Deleted
        /// </summary>
        /// <param name="personalInfoId">معرف المعلومات الشخصية</param>
        /// <returns>هل يمكن الحذف</returns>
        public bool CanDeletePersonalInformation(int personalInfoId)
        {
            try
            {
                var parameters = new[]
                {
                    CreateParameter("@PersonalInfoId", personalInfoId)
                };

                var result = ExecuteStoredProcedureScalar("SP_CanDeletePersonalInformation", parameters);
                return ConvertFromDb<bool>(result);
            }
            catch (Exception ex)
            {
                LogError(ex, "التحقق من إمكانية حذف المعلومات الشخصية");
                throw new Exception(HandleException(ex, "التحقق من إمكانية حذف المعلومات الشخصية"));
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن المعلومات الشخصية
        /// Map Data Reader to Personal Information Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن المعلومات الشخصية</returns>
        private PersonalInformation MapPersonalInformationFromReader(SqlDataReader reader)
        {
            return new PersonalInformation
            {
                PersonalInfoId = ConvertFromDb<int>(reader["PersonalInfoId"]),
                FullNameAr = ConvertFromDb<string>(reader["FullNameAr"]),
                FullNameEn = ConvertFromDb<string>(reader["FullNameEn"]),
                NationalId = ConvertFromDb<string>(reader["NationalId"]),
                PassportNumber = ConvertFromDb<string>(reader["PassportNumber"]),
                DateOfBirth = ConvertFromDb<DateTime?>(reader["DateOfBirth"]),
                Gender = ConvertFromDb<string>(reader["Gender"]),
                Nationality = ConvertFromDb<string>(reader["Nationality"]),
                Phone1 = ConvertFromDb<string>(reader["Phone1"]),
                Phone2 = ConvertFromDb<string>(reader["Phone2"]),
                Email = ConvertFromDb<string>(reader["Email"]),
                Address = ConvertFromDb<string>(reader["Address"]),
                City = ConvertFromDb<string>(reader["City"]),
                Country = ConvertFromDb<string>(reader["Country"]),
                PostalCode = ConvertFromDb<string>(reader["PostalCode"]),
                Notes = ConvertFromDb<string>(reader["Notes"]),
                IsActive = ConvertFromDb<bool>(reader["IsActive"]),
                CreatedDate = ConvertFromDb<DateTime>(reader["CreatedDate"]),
                CreatedBy = ConvertFromDb<string>(reader["CreatedBy"]),
                ModifiedDate = ConvertFromDb<DateTime?>(reader["ModifiedDate"]),
                ModifiedBy = ConvertFromDb<string>(reader["ModifiedBy"])
            };
        }

        #endregion
    }
}
