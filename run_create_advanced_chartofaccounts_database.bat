@echo off
chcp 65001 >nul
echo ========================================
echo إنشاء قاعدة البيانات المتطورة للدليل المحاسبي
echo Advanced Chart of Accounts Database Setup
echo ========================================
echo.

set SERVER_NAME=NAJEEB
set DATABASE_NAME=AwqafManagement
set SCRIPTS_PATH=Database\Scripts

echo 🔍 التحقق من وجود ملفات السكريبت...
echo Checking for script files...

if not exist "%SCRIPTS_PATH%\Create_Advanced_ChartOfAccounts_Database.sql" (
    echo ❌ خطأ: ملف إنشاء قاعدة البيانات غير موجود
    echo Error: Database creation script not found
    pause
    exit /b 1
)

if not exist "%SCRIPTS_PATH%\Create_ChartOfAccounts_StoredProcedures.sql" (
    echo ❌ خطأ: ملف الإجراءات المخزنة غير موجود
    echo Error: Stored procedures script not found
    pause
    exit /b 1
)

if not exist "%SCRIPTS_PATH%\Create_ChartOfAccounts_Triggers.sql" (
    echo ❌ خطأ: ملف المشغلات غير موجود
    echo Error: Triggers script not found
    pause
    exit /b 1
)

if not exist "%SCRIPTS_PATH%\Insert_ChartOfAccounts_InitialData.sql" (
    echo ❌ خطأ: ملف البيانات الأولية غير موجود
    echo Error: Initial data script not found
    pause
    exit /b 1
)

echo ✅ جميع ملفات السكريبت موجودة
echo All script files found
echo.

echo ⚠️  تحذير: سيتم إنشاء قاعدة بيانات جديدة للدليل المحاسبي
echo Warning: This will create a new chart of accounts database
echo.
echo هذا سيقوم بـ:
echo This will:
echo - إنشاء جداول قاعدة البيانات الجديدة
echo - Create new database tables
echo - إنشاء الإجراءات المخزنة
echo - Create stored procedures  
echo - إنشاء المشغلات
echo - Create triggers
echo - إدراج البيانات الأولية
echo - Insert initial data
echo.

set /p CONFIRM="هل تريد المتابعة؟ (y/n) / Do you want to continue? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo تم الإلغاء / Cancelled
    pause
    exit /b 0
)

echo.
echo ========================================
echo بدء تنفيذ السكريبتات...
echo Starting script execution...
echo ========================================

echo.
echo 📋 الخطوة 1: إنشاء الجداول الأساسية...
echo Step 1: Creating basic tables...
sqlcmd -S %SERVER_NAME% -d %DATABASE_NAME% -i "%SCRIPTS_PATH%\Create_Advanced_ChartOfAccounts_Database.sql" -o "logs\01_create_tables.log"
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في إنشاء الجداول
    echo Error creating tables
    echo تحقق من ملف السجل: logs\01_create_tables.log
    echo Check log file: logs\01_create_tables.log
    pause
    exit /b 1
)
echo ✅ تم إنشاء الجداول بنجاح
echo Tables created successfully

echo.
echo 📋 الخطوة 2: إنشاء الإجراءات المخزنة...
echo Step 2: Creating stored procedures...
sqlcmd -S %SERVER_NAME% -d %DATABASE_NAME% -i "%SCRIPTS_PATH%\Create_ChartOfAccounts_StoredProcedures.sql" -o "logs\02_create_procedures.log"
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في إنشاء الإجراءات المخزنة
    echo Error creating stored procedures
    echo تحقق من ملف السجل: logs\02_create_procedures.log
    echo Check log file: logs\02_create_procedures.log
    pause
    exit /b 1
)
echo ✅ تم إنشاء الإجراءات المخزنة بنجاح
echo Stored procedures created successfully

echo.
echo 📋 الخطوة 3: إنشاء المشغلات...
echo Step 3: Creating triggers...
sqlcmd -S %SERVER_NAME% -d %DATABASE_NAME% -i "%SCRIPTS_PATH%\Create_ChartOfAccounts_Triggers.sql" -o "logs\03_create_triggers.log"
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في إنشاء المشغلات
    echo Error creating triggers
    echo تحقق من ملف السجل: logs\03_create_triggers.log
    echo Check log file: logs\03_create_triggers.log
    pause
    exit /b 1
)
echo ✅ تم إنشاء المشغلات بنجاح
echo Triggers created successfully

echo.
echo 📋 الخطوة 4: إدراج البيانات الأولية...
echo Step 4: Inserting initial data...
sqlcmd -S %SERVER_NAME% -d %DATABASE_NAME% -i "%SCRIPTS_PATH%\Insert_ChartOfAccounts_InitialData.sql" -o "logs\04_insert_data.log"
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في إدراج البيانات الأولية
    echo Error inserting initial data
    echo تحقق من ملف السجل: logs\04_insert_data.log
    echo Check log file: logs\04_insert_data.log
    pause
    exit /b 1
)
echo ✅ تم إدراج البيانات الأولية بنجاح
echo Initial data inserted successfully

echo.
echo ========================================
echo 🎉 تم إنشاء قاعدة البيانات المتطورة بنجاح!
echo 🎉 Advanced database created successfully!
echo ========================================
echo.

echo تم إنشاء:
echo Created:
echo ✅ %DATABASE_NAME% - قاعدة البيانات
echo ✅ %DATABASE_NAME% - Database
echo ✅ 7 جداول أساسية مع الفهارس
echo ✅ 7 basic tables with indexes
echo ✅ 4 إجراءات مخزنة متطورة
echo ✅ 4 advanced stored procedures
echo ✅ 3 مشغلات للتدقيق والحماية
echo ✅ 3 triggers for auditing and protection
echo ✅ بيانات أولية شاملة
echo ✅ Comprehensive initial data
echo.

echo يمكنك الآن:
echo You can now:
echo - بدء تطوير واجهة المستخدم
echo - Start developing the user interface
echo - استخدام الإجراءات المخزنة في التطبيق
echo - Use stored procedures in the application
echo - الاستفادة من نظام التدقيق التلقائي
echo - Benefit from automatic audit system
echo.

echo ملفات السجل متوفرة في مجلد logs\
echo Log files available in logs\ folder
echo.

pause
