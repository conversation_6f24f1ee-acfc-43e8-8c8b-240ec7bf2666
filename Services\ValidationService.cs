using System;
using System.Text.RegularExpressions;
using System.Globalization;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.Services
{
    /// <summary>
    /// خدمة التحقق من صحة البيانات
    /// Data Validation Service
    /// </summary>
    public class ValidationService
    {
        #region Account Validation

        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// Validate Account Data
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccount(ChartOfAccount account)
        {
            try
            {
                if (account == null)
                {
                    return ServiceResult<bool>.Failure("بيانات الحساب مطلوبة");
                }

                // التحقق من اسم الحساب بالعربية
                if (string.IsNullOrWhiteSpace(account.AccountNameAr))
                {
                    return ServiceResult<bool>.Failure("اسم الحساب بالعربية مطلوب");
                }

                if (account.AccountNameAr.Length > 200)
                {
                    return ServiceResult<bool>.Failure("اسم الحساب بالعربية يجب أن يكون أقل من 200 حرف");
                }

                // التحقق من اسم الحساب بالإنجليزية
                if (string.IsNullOrWhiteSpace(account.AccountName))
                {
                    return ServiceResult<bool>.Failure("اسم الحساب بالإنجليزية مطلوب");
                }

                if (account.AccountName.Length > 200)
                {
                    return ServiceResult<bool>.Failure("اسم الحساب بالإنجليزية يجب أن يكون أقل من 200 حرف");
                }

                // التحقق من كود الحساب إذا كان محدداً
                if (!string.IsNullOrEmpty(account.AccountCode))
                {
                    var codeValidation = ValidateAccountCode(account.AccountCode);
                    if (!codeValidation.IsSuccess)
                    {
                        return codeValidation;
                    }
                }

                // التحقق من نوع الحساب
                if (account.AccountTypeId <= 0)
                {
                    return ServiceResult<bool>.Failure("نوع الحساب مطلوب");
                }

                // التحقق من مجموعة الحساب
                if (account.AccountGroupId <= 0)
                {
                    return ServiceResult<bool>.Failure("مجموعة الحساب مطلوبة");
                }

                // التحقق من كود العملة
                if (string.IsNullOrWhiteSpace(account.CurrencyCode))
                {
                    return ServiceResult<bool>.Failure("كود العملة مطلوب");
                }

                if (!IsValidCurrencyCode(account.CurrencyCode))
                {
                    return ServiceResult<bool>.Failure("كود العملة غير صحيح");
                }

                // التحقق من الرصيد الافتتاحي
                if (account.OpeningBalance < 0)
                {
                    return ServiceResult<bool>.Failure("الرصيد الافتتاحي لا يمكن أن يكون سالباً");
                }

                // التحقق من الوصف إذا كان محدداً
                if (!string.IsNullOrEmpty(account.Description) && account.Description.Length > 500)
                {
                    return ServiceResult<bool>.Failure("الوصف يجب أن يكون أقل من 500 حرف");
                }

                // التحقق من المستخدم المنشئ
                if (account.AccountId == 0 && string.IsNullOrWhiteSpace(account.CreatedBy))
                {
                    return ServiceResult<bool>.Failure("المستخدم المنشئ مطلوب");
                }

                return ServiceResult<bool>.Success(true, "بيانات الحساب صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من بيانات الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة كود الحساب
        /// Validate Account Code
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccountCode(string accountCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(accountCode))
                {
                    return ServiceResult<bool>.Failure("كود الحساب مطلوب");
                }

                if (!IsValidAccountCodeFormat(accountCode))
                {
                    return ServiceResult<bool>.Failure("تنسيق كود الحساب غير صحيح. يجب أن يكون بالتنسيق XX.XX.XXX");
                }

                return ServiceResult<bool>.Success(true, "كود الحساب صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من كود الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من تنسيق كود الحساب
        /// Check Account Code Format
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <returns>هل التنسيق صحيح</returns>
        public bool IsValidAccountCodeFormat(string accountCode)
        {
            if (string.IsNullOrWhiteSpace(accountCode))
                return false;

            // تنسيق كود الحساب: XX.XX.XXX (رقمين.رقمين.ثلاثة أرقام)
            var pattern = @"^\d{2}\.\d{2}\.\d{3}$";
            return Regex.IsMatch(accountCode, pattern);
        }

        #endregion

        #region Account Type Validation

        /// <summary>
        /// التحقق من صحة بيانات نوع الحساب
        /// Validate Account Type Data
        /// </summary>
        /// <param name="accountType">بيانات نوع الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccountType(AccountType accountType)
        {
            try
            {
                if (accountType == null)
                {
                    return ServiceResult<bool>.Failure("بيانات نوع الحساب مطلوبة");
                }

                // التحقق من كود النوع
                if (string.IsNullOrWhiteSpace(accountType.TypeCode))
                {
                    return ServiceResult<bool>.Failure("كود نوع الحساب مطلوب");
                }

                if (accountType.TypeCode.Length > 10)
                {
                    return ServiceResult<bool>.Failure("كود نوع الحساب يجب أن يكون أقل من 10 أحرف");
                }

                // التحقق من اسم النوع بالعربية
                if (string.IsNullOrWhiteSpace(accountType.TypeNameAr))
                {
                    return ServiceResult<bool>.Failure("اسم نوع الحساب بالعربية مطلوب");
                }

                if (accountType.TypeNameAr.Length > 100)
                {
                    return ServiceResult<bool>.Failure("اسم نوع الحساب بالعربية يجب أن يكون أقل من 100 حرف");
                }

                // التحقق من اسم النوع بالإنجليزية
                if (string.IsNullOrWhiteSpace(accountType.TypeName))
                {
                    return ServiceResult<bool>.Failure("اسم نوع الحساب بالإنجليزية مطلوب");
                }

                if (accountType.TypeName.Length > 100)
                {
                    return ServiceResult<bool>.Failure("اسم نوع الحساب بالإنجليزية يجب أن يكون أقل من 100 حرف");
                }

                return ServiceResult<bool>.Success(true, "بيانات نوع الحساب صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من بيانات نوع الحساب: {ex.Message}");
            }
        }

        #endregion

        #region Account Group Validation

        /// <summary>
        /// التحقق من صحة بيانات مجموعة الحساب
        /// Validate Account Group Data
        /// </summary>
        /// <param name="accountGroup">بيانات مجموعة الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccountGroup(AccountGroup accountGroup)
        {
            try
            {
                if (accountGroup == null)
                {
                    return ServiceResult<bool>.Failure("بيانات مجموعة الحساب مطلوبة");
                }

                // التحقق من كود المجموعة
                if (string.IsNullOrWhiteSpace(accountGroup.GroupCode))
                {
                    return ServiceResult<bool>.Failure("كود مجموعة الحساب مطلوب");
                }

                if (accountGroup.GroupCode.Length > 10)
                {
                    return ServiceResult<bool>.Failure("كود مجموعة الحساب يجب أن يكون أقل من 10 أحرف");
                }

                // التحقق من اسم المجموعة بالعربية
                if (string.IsNullOrWhiteSpace(accountGroup.GroupNameAr))
                {
                    return ServiceResult<bool>.Failure("اسم مجموعة الحساب بالعربية مطلوب");
                }

                if (accountGroup.GroupNameAr.Length > 100)
                {
                    return ServiceResult<bool>.Failure("اسم مجموعة الحساب بالعربية يجب أن يكون أقل من 100 حرف");
                }

                // التحقق من اسم المجموعة بالإنجليزية
                if (string.IsNullOrWhiteSpace(accountGroup.GroupName))
                {
                    return ServiceResult<bool>.Failure("اسم مجموعة الحساب بالإنجليزية مطلوب");
                }

                if (accountGroup.GroupName.Length > 100)
                {
                    return ServiceResult<bool>.Failure("اسم مجموعة الحساب بالإنجليزية يجب أن يكون أقل من 100 حرف");
                }

                // التحقق من نوع الحساب
                if (accountGroup.AccountTypeId <= 0)
                {
                    return ServiceResult<bool>.Failure("نوع الحساب مطلوب");
                }

                return ServiceResult<bool>.Success(true, "بيانات مجموعة الحساب صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من بيانات مجموعة الحساب: {ex.Message}");
            }
        }

        #endregion

        #region Currency Validation

        /// <summary>
        /// التحقق من صحة بيانات العملة
        /// Validate Currency Data
        /// </summary>
        /// <param name="currency">بيانات العملة</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateCurrency(Currency currency)
        {
            try
            {
                if (currency == null)
                {
                    return ServiceResult<bool>.Failure("بيانات العملة مطلوبة");
                }

                // التحقق من كود العملة
                if (string.IsNullOrWhiteSpace(currency.CurrencyCode))
                {
                    return ServiceResult<bool>.Failure("كود العملة مطلوب");
                }

                if (!IsValidCurrencyCode(currency.CurrencyCode))
                {
                    return ServiceResult<bool>.Failure("كود العملة يجب أن يكون 3 أحرف");
                }

                // التحقق من اسم العملة بالعربية
                if (string.IsNullOrWhiteSpace(currency.CurrencyNameAr))
                {
                    return ServiceResult<bool>.Failure("اسم العملة بالعربية مطلوب");
                }

                // التحقق من اسم العملة بالإنجليزية
                if (string.IsNullOrWhiteSpace(currency.CurrencyName))
                {
                    return ServiceResult<bool>.Failure("اسم العملة بالإنجليزية مطلوب");
                }

                // التحقق من سعر الصرف
                if (currency.ExchangeRate <= 0)
                {
                    return ServiceResult<bool>.Failure("سعر الصرف يجب أن يكون أكبر من الصفر");
                }

                // التحقق من عدد المنازل العشرية
                if (currency.DecimalPlaces < 0 || currency.DecimalPlaces > 6)
                {
                    return ServiceResult<bool>.Failure("عدد المنازل العشرية يجب أن يكون بين 0 و 6");
                }

                return ServiceResult<bool>.Success(true, "بيانات العملة صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من بيانات العملة: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة كود العملة
        /// Validate Currency Code
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>هل الكود صحيح</returns>
        public bool IsValidCurrencyCode(string currencyCode)
        {
            if (string.IsNullOrWhiteSpace(currencyCode))
                return false;

            // كود العملة يجب أن يكون 3 أحرف إنجليزية
            return currencyCode.Length == 3 && Regex.IsMatch(currencyCode, @"^[A-Z]{3}$");
        }

        #endregion

        #region Personal Information Validation

        /// <summary>
        /// التحقق من صحة المعلومات الشخصية
        /// Validate Personal Information
        /// </summary>
        /// <param name="personalInfo">المعلومات الشخصية</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidatePersonalInformation(PersonalInformation personalInfo)
        {
            try
            {
                if (personalInfo == null)
                {
                    return ServiceResult<bool>.Failure("المعلومات الشخصية مطلوبة");
                }

                // التحقق من الاسم بالعربية
                if (string.IsNullOrWhiteSpace(personalInfo.FullNameAr))
                {
                    return ServiceResult<bool>.Failure("الاسم الكامل بالعربية مطلوب");
                }

                // التحقق من الاسم بالإنجليزية
                if (string.IsNullOrWhiteSpace(personalInfo.FullNameEn))
                {
                    return ServiceResult<bool>.Failure("الاسم الكامل بالإنجليزية مطلوب");
                }

                // التحقق من رقم الهوية الوطنية إذا كان محدداً
                if (!string.IsNullOrEmpty(personalInfo.NationalId))
                {
                    if (!IsValidNationalId(personalInfo.NationalId))
                    {
                        return ServiceResult<bool>.Failure("رقم الهوية الوطنية غير صحيح");
                    }
                }

                // التحقق من البريد الإلكتروني إذا كان محدداً
                if (!string.IsNullOrEmpty(personalInfo.Email))
                {
                    if (!IsValidEmail(personalInfo.Email))
                    {
                        return ServiceResult<bool>.Failure("البريد الإلكتروني غير صحيح");
                    }
                }

                // التحقق من رقم الهاتف إذا كان محدداً
                if (!string.IsNullOrEmpty(personalInfo.Phone1))
                {
                    if (!IsValidPhoneNumber(personalInfo.Phone1))
                    {
                        return ServiceResult<bool>.Failure("رقم الهاتف الأول غير صحيح");
                    }
                }

                return ServiceResult<bool>.Success(true, "المعلومات الشخصية صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من المعلومات الشخصية: {ex.Message}");
            }
        }

        #endregion

        #region Search Model Validation

        /// <summary>
        /// التحقق من صحة نموذج البحث
        /// Validate Search Model
        /// </summary>
        /// <param name="searchModel">نموذج البحث</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateSearchModel(AccountSearchModel searchModel)
        {
            try
            {
                if (searchModel == null)
                {
                    return ServiceResult<bool>.Failure("نموذج البحث مطلوب");
                }

                // التحقق من حجم الصفحة
                if (searchModel.PageSize <= 0 || searchModel.PageSize > 1000)
                {
                    return ServiceResult<bool>.Failure("حجم الصفحة يجب أن يكون بين 1 و 1000");
                }

                // التحقق من رقم الصفحة
                if (searchModel.PageNumber <= 0)
                {
                    return ServiceResult<bool>.Failure("رقم الصفحة يجب أن يكون أكبر من الصفر");
                }

                return ServiceResult<bool>.Success(true, "نموذج البحث صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من نموذج البحث: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التحقق من صحة رقم الهوية الوطنية
        /// Validate National ID
        /// </summary>
        /// <param name="nationalId">رقم الهوية الوطنية</param>
        /// <returns>هل الرقم صحيح</returns>
        public bool IsValidNationalId(string nationalId)
        {
            if (string.IsNullOrWhiteSpace(nationalId))
                return false;

            // التحقق من أن الرقم يحتوي على أرقام فقط وطوله مناسب
            return Regex.IsMatch(nationalId, @"^\d{10,20}$");
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// Validate Email Address
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>هل البريد صحيح</returns>
        public bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
                return Regex.IsMatch(email, emailPattern, RegexOptions.IgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف
        /// Validate Phone Number
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <returns>هل الرقم صحيح</returns>
        public bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // التحقق من رقم الهاتف (يمكن أن يحتوي على أرقام ورموز + و - و مسافات)
            var phonePattern = @"^[\+]?[0-9\-\s\(\)]{7,20}$";
            return Regex.IsMatch(phoneNumber, phonePattern);
        }

        #endregion
    }
}
