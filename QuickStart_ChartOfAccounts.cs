using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Awqaf_Managment.UI.Forms.Accounting;

namespace Awqaf_Managment
{
    /// <summary>
    /// تشغيل سريع لنظام إدارة دليل الحسابات
    /// Quick Start for Chart of Accounts Management System
    /// </summary>
    public static class QuickStart_ChartOfAccounts
    {
        /// <summary>
        /// نقطة الدخول الرئيسية
        /// Main Entry Point
        /// </summary>
        [STAThread]
        public static void Main()
        {
            try
            {
                // إعداد التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // عرض رسالة ترحيب
                ShowWelcomeMessage();

                // تشغيل نموذج إدارة دليل الحسابات
                var chartOfAccountsForm = new ChartOfAccountsManagementForm();
                Application.Run(chartOfAccountsForm);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تشغيل النظام:\n{ex.Message}",
                    "خطأ في النظام",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// عرض رسالة ترحيب
        /// Show Welcome Message
        /// </summary>
        private static void ShowWelcomeMessage()
        {
            var message = @"🎉 مرحباً بك في نظام إدارة دليل الحسابات الجديد!

✨ المميزات الجديدة:
• تصميم عصري ومهني
• دعم كامل للغة العربية
• واجهة سهلة الاستخدام
• توليد أكواد تلقائي
• بحث وتصفية متقدمة
• هيكل هرمي للحسابات

🚀 النظام جاهز للاستخدام!";

            MessageBox.Show(
                message,
                "نظام إدارة دليل الحسابات - إصدار 1.0",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
        }
    }
}
