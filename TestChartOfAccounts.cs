using System;
using System.Windows.Forms;
using Awqaf_Managment.UI.Forms.Accounting;

namespace Awqaf_Managment
{
    /// <summary>
    /// اختبار سريع لنظام إدارة دليل الحسابات
    /// Quick Test for Chart of Accounts Management System
    /// </summary>
    public static class TestChartOfAccounts
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للاختبار
        /// Main Entry Point for Testing
        /// </summary>
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("🧪 بدء اختبار نظام إدارة دليل الحسابات...");
                Console.WriteLine("=" * 50);

                // إعداد التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // عرض رسالة ترحيب
                ShowWelcomeMessage();

                // تشغيل نموذج إدارة دليل الحسابات
                Console.WriteLine("🚀 فتح نموذج إدارة دليل الحسابات...");
                var chartOfAccountsForm = new ChartOfAccountsManagementForm();
                
                Console.WriteLine("✅ تم إنشاء النموذج بنجاح!");
                Console.WriteLine("🎯 النموذج جاهز للاستخدام!");
                
                Application.Run(chartOfAccountsForm);
                
                Console.WriteLine("👋 تم إغلاق النموذج بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تشغيل النظام: {ex.Message}");
                Console.WriteLine($"📍 تفاصيل الخطأ: {ex.StackTrace}");
                
                MessageBox.Show(
                    $"خطأ في تشغيل النظام:\n\n{ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                    "خطأ في النظام",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// عرض رسالة ترحيب
        /// Show Welcome Message
        /// </summary>
        private static void ShowWelcomeMessage()
        {
            var message = @"🎉 مرحباً بك في نظام إدارة دليل الحسابات الجديد!

✨ المميزات الجديدة:
• تصميم عصري ومهني خالٍ من الأخطاء
• دعم كامل للغة العربية مع RTL
• واجهة سهلة الاستخدام مع شجرة هرمية
• توليد أكواد الحسابات التلقائي
• بحث وتصفية متقدمة
• هيكل هرمي للحسابات (أب-فرع)
• دعم عملات متعددة
• أرصدة افتتاحية وحالية
• اختصارات لوحة المفاتيح

🚀 النظام جاهز للاستخدام!

📍 الملف: UI/Forms/Accounting/ChartOfAccountsManagementForm.cs
🔧 الخدمات: Services/Accounting/ChartOfAccountsService.cs
🗄️ البيانات: DataAccess/Accounting/ChartOfAccountsDataAccess.cs";

            Console.WriteLine(message);
            Console.WriteLine("=" * 50);

            MessageBox.Show(
                message,
                "نظام إدارة دليل الحسابات - إصدار 1.0 ✅",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// اختبار سريع للنظام بدون واجهة
        /// Quick System Test without UI
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                Console.WriteLine("⚡ اختبار سريع للنظام...");

                // اختبار إنشاء النموذج
                var form = new ChartOfAccountsManagementForm();
                Console.WriteLine("✅ تم إنشاء النموذج بنجاح");

                // اختبار العناصر الأساسية
                if (form.Controls.Count > 0)
                    Console.WriteLine("✅ تم تحميل عناصر التحكم");

                form.Dispose();
                Console.WriteLine("✅ تم تنظيف الموارد");

                Console.WriteLine("🎯 الاختبار السريع نجح - النظام يعمل بشكل صحيح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل الاختبار السريع: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// طباعة معلومات النظام
        /// Print System Information
        /// </summary>
        public static void PrintSystemInfo()
        {
            Console.WriteLine("📋 معلومات النظام:");
            Console.WriteLine("=" * 40);
            Console.WriteLine($"📅 تاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"🏗️ المطور: نظام إدارة الأوقاف");
            Console.WriteLine($"📦 الوحدة: إدارة دليل الحسابات");
            Console.WriteLine($"🔢 الإصدار: 1.0.0");
            Console.WriteLine($"🎯 الحالة: جاهز للاستخدام ✅");
            Console.WriteLine($"📁 المسار: UI/Forms/Accounting/ChartOfAccountsManagementForm");
            Console.WriteLine($"🔧 الخدمات: Services/Accounting/ChartOfAccountsService");
            Console.WriteLine($"🗄️ البيانات: DataAccess/Accounting/ChartOfAccountsDataAccess");
            Console.WriteLine("=" * 40);
        }
    }
}
