<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3E68ABF8-FEE6-49F3-9219-502553DE9AB4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Awqaf_Managment</RootNamespace>
    <AssemblyName>Awqaf_Managment</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|Any CPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|Any CPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FontAwesome.Sharp, Version=6.6.0.0, Culture=neutral, PublicKeyToken=d16d1e4e568ec10f, processorArchitecture=MSIL">
      <HintPath>packages\FontAwesome.Sharp.6.6.0\lib\net48\FontAwesome.Sharp.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SqlClient, Version=4.6.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SqlClient.4.9.0\lib\net462\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="UI\Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\DashboardForm.Designer.cs">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\Security\UserManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\Security\UserManagementForm.Designer.cs">
      <DependentUpon>UserManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\Security\AddEditUserForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\Security\AddEditUserForm.Designer.cs">
      <DependentUpon>AddEditUserForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\Security\UserDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\Security\UserDetailsForm.Designer.cs">
      <DependentUpon>UserDetailsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\Accounting\CurrencyManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\Accounting\CurrencyManagementForm.Designer.cs">
      <DependentUpon>CurrencyManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Helpers\ComboBoxItem.cs" />
    <Compile Include="UI\Helpers\UIHelper.cs" />
    <Compile Include="Common\Constants.cs" />
    <Compile Include="Common\Helpers\UIHelper.cs" />
    <Compile Include="Models\Security\User.cs" />
    <Compile Include="Models\Currency.cs" />

    <Compile Include="Models\ChartOfAccount.cs" />
    <Compile Include="Models\PersonalInformation.cs" />
    <Compile Include="Models\AccountType.cs" />
    <Compile Include="Models\AccountGroup.cs" />


    <Compile Include="DataAccess\DatabaseConnection.cs" />
    <Compile Include="DataAccess\Security\UserDataAccess.cs" />
    <Compile Include="DataAccess\Security\RoleDataAccess.cs" />
    <Compile Include="DataAccess\Base\BaseDataAccess.cs" />
    <Compile Include="DataAccess\CurrencyDataAccess.cs" />
    <Compile Include="DataAccess\ChartOfAccountsDataAccess.cs" />
    <Compile Include="DataAccess\PersonalInformationDataAccess.cs" />
    <Compile Include="DataAccess\AccountTypeDataAccess.cs" />
    <Compile Include="DataAccess\AccountGroupDataAccess.cs" />
    <Compile Include="DataAccess\CurrencyDataAccess.cs" />
    <Compile Include="DataAccess\AuditLogDataAccess.cs" />
    <Compile Include="DataAccess\Accounting\EnhancedChartOfAccountsDataAccess.cs" />
    <Compile Include="DataAccess\Accounting\PersonalInformationDataAccess.cs" />

    <Compile Include="Services\Security\AuthenticationService.cs" />
    <Compile Include="Services\Accounting\CostCenterService.cs" />
    <Compile Include="Services\ChartOfAccountsService.cs" />
    <Compile Include="Services\ValidationService.cs" />
    <Compile Include="Services\BusinessRulesService.cs" />
    <Compile Include="Services\AuditService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\JournalEntryService.cs" />
    <Compile Include="Services\ServiceResult.cs" />


    <Compile Include="UI\Controls\EnhancedAccountsTreeView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Controls\EnhancedAccountsTreeView.Designer.cs">
      <DependentUpon>EnhancedAccountsTreeView.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Controls\AccountSelectedEventArgs.cs" />
    <Compile Include="UI\Forms\Accounting\ChartOfAccountsManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Forms\Accounting\ChartOfAccountsManagementForm.Designer.cs">
      <DependentUpon>ChartOfAccountsManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Forms\TestMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TestSimpleRun.cs" />
    <Compile Include="DatabaseConfig.cs" />
    <Compile Include="TestDatabaseConnection.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\DashboardForm.resx">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\Accounting\CurrencyManagementForm.resx">
      <DependentUpon>CurrencyManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\Security\UserManagementForm.resx">
      <DependentUpon>UserManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\Security\AddEditUserForm.resx">
      <DependentUpon>AddEditUserForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\Security\UserDetailsForm.resx">
      <DependentUpon>UserDetailsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Controls\EnhancedAccountsTreeView.resx">
      <DependentUpon>EnhancedAccountsTreeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Forms\Accounting\ChartOfAccountsManagementForm.resx">
      <DependentUpon>ChartOfAccountsManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>