-- =============================================
-- إدراج البيانات الأساسية لدليل الحسابات
-- Insert Basic Chart of Accounts Data
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. إدراج أنواع الحسابات الأساسية
-- Insert Basic Account Types
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountTypes WHERE TypeCode = 'ASSETS')
BEGIN
    INSERT INTO [dbo].[AccountTypes] ([TypeCode], [TypeNameAr], [TypeNameEn], [Nature], [DisplayOrder])
    VALUES 
        ('ASSETS', N'الأصول', 'Assets', 'Debit', 1),
        ('LIAB', N'الخصوم', 'Liabilities', 'Credit', 2),
        ('EQUITY', N'حقوق الملكية', 'Equity', 'Credit', 3),
        ('REV', N'الإيرادات', 'Revenue', 'Credit', 4),
        ('EXP', N'المصروفات', 'Expenses', 'Debit', 5);
    
    PRINT N'تم إدراج أنواع الحسابات الأساسية';
END
ELSE
BEGIN
    PRINT N'أنواع الحسابات موجودة مسبقاً';
END
GO

-- =============================================
-- 2. إدراج مجموعات الحسابات الأساسية
-- Insert Basic Account Groups
-- =============================================
IF NOT EXISTS (SELECT 1 FROM AccountGroups WHERE GroupCode = 'CA')
BEGIN
    INSERT INTO [dbo].[AccountGroups] ([AccountTypeId], [GroupCode], [GroupNameAr], [GroupNameEn], [DisplayOrder])
    VALUES 
        (1, 'CA', N'الأصول المتداولة', 'Current Assets', 1),
        (1, 'FA', N'الأصول الثابتة', 'Fixed Assets', 2),
        (2, 'CL', N'الخصوم المتداولة', 'Current Liabilities', 1),
        (2, 'LTL', N'الخصوم طويلة الأجل', 'Long-term Liabilities', 2),
        (3, 'CE', N'رأس المال', 'Capital Equity', 1),
        (4, 'OR', N'الإيرادات التشغيلية', 'Operating Revenue', 1),
        (4, 'NOR', N'الإيرادات غير التشغيلية', 'Non-operating Revenue', 2),
        (5, 'OE', N'المصروفات التشغيلية', 'Operating Expenses', 1),
        (5, 'NOE', N'المصروفات غير التشغيلية', 'Non-operating Expenses', 2);
    
    PRINT N'تم إدراج مجموعات الحسابات الأساسية';
END
ELSE
BEGIN
    PRINT N'مجموعات الحسابات موجودة مسبقاً';
END
GO

-- =============================================
-- 3. إدراج الحسابات الرئيسية الأساسية
-- Insert Basic Main Accounts
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = '1.01.001')
BEGIN
    INSERT INTO [dbo].[ChartOfAccounts] 
    ([AccountCode], [AccountNameAr], [AccountNameEn], [AccountTypeId], [AccountGroupId], [AccountLevel], [IsParent], [AllowPosting])
    VALUES 
        -- الأصول المتداولة
        ('1.01.001', N'النقدية والنقدية المعادلة', 'Cash and Cash Equivalents', 1, 1, 1, 1, 0),
        ('1.02.001', N'الذمم المدينة', 'Accounts Receivable', 1, 1, 1, 1, 0),
        ('1.03.001', N'المخزون', 'Inventory', 1, 1, 1, 1, 0),
        
        -- الأصول الثابتة
        ('2.01.001', N'الأراضي والمباني', 'Land and Buildings', 1, 2, 1, 1, 0),
        ('2.02.001', N'المعدات والأثاث', 'Equipment and Furniture', 1, 2, 1, 1, 0),
        ('2.03.001', N'وسائل النقل', 'Vehicles', 1, 2, 1, 1, 0),
        
        -- الخصوم المتداولة
        ('3.01.001', N'الذمم الدائنة', 'Accounts Payable', 2, 3, 1, 1, 0),
        ('3.02.001', N'المصروفات المستحقة', 'Accrued Expenses', 2, 3, 1, 1, 0),
        
        -- حقوق الملكية
        ('4.01.001', N'رأس المال', 'Capital', 3, 5, 1, 1, 0),
        ('4.02.001', N'الأرباح المحتجزة', 'Retained Earnings', 3, 5, 1, 1, 0),
        
        -- الإيرادات
        ('5.01.001', N'إيرادات الأوقاف', 'Waqf Revenue', 4, 6, 1, 1, 0),
        ('5.02.001', N'إيرادات الاستثمار', 'Investment Revenue', 4, 6, 1, 1, 0),
        
        -- المصروفات
        ('6.01.001', N'مصروفات التشغيل', 'Operating Expenses', 5, 8, 1, 1, 0),
        ('6.02.001', N'مصروفات الإدارة', 'Administrative Expenses', 5, 8, 1, 1, 0);
    
    PRINT N'تم إدراج الحسابات الرئيسية الأساسية';
END
ELSE
BEGIN
    PRINT N'الحسابات الرئيسية موجودة مسبقاً';
END
GO

-- =============================================
-- 4. إدراج بعض الحسابات الفرعية كأمثلة
-- Insert Some Sub-accounts as Examples
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = '1.01.002')
BEGIN
    INSERT INTO [dbo].[ChartOfAccounts] 
    ([AccountCode], [AccountNameAr], [AccountNameEn], [AccountTypeId], [AccountGroupId], [ParentAccountId], [AccountLevel], [IsParent], [AllowPosting])
    VALUES 
        -- حسابات فرعية للنقدية
        ('1.01.002', N'الصندوق', 'Cash on Hand', 1, 1, 1, 2, 0, 1),
        ('1.01.003', N'البنك الأهلي', 'National Bank', 1, 1, 1, 2, 0, 1),
        ('1.01.004', N'بنك الراجحي', 'Al Rajhi Bank', 1, 1, 1, 2, 0, 1),
        
        -- حسابات فرعية للذمم المدينة
        ('1.02.002', N'ذمم المستأجرين', 'Tenants Receivables', 1, 1, 2, 2, 0, 1),
        ('1.02.003', N'ذمم أخرى', 'Other Receivables', 1, 1, 2, 2, 0, 1),
        
        -- حسابات فرعية للمصروفات
        ('6.01.002', N'مصروفات الصيانة', 'Maintenance Expenses', 5, 8, 13, 2, 0, 1),
        ('6.01.003', N'مصروفات الكهرباء', 'Electricity Expenses', 5, 8, 13, 2, 0, 1),
        ('6.01.004', N'مصروفات المياه', 'Water Expenses', 5, 8, 13, 2, 0, 1);
    
    PRINT N'تم إدراج الحسابات الفرعية كأمثلة';
END
ELSE
BEGIN
    PRINT N'الحسابات الفرعية موجودة مسبقاً';
END
GO

PRINT N'تم إكمال إدراج البيانات الأساسية لدليل الحسابات بنجاح';
PRINT N'Basic Chart of Accounts data insertion completed successfully';
