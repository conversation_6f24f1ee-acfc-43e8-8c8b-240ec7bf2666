-- =============================================
-- نظام إدارة الأوقاف - قاعدة البيانات المتقدمة للدليل المحاسبي
-- Awqaf Management System - Advanced Chart of Accounts Database
-- =============================================
-- التاريخ: 2025-07-05
-- الإصدار: 2.0 المتقدم
-- =============================================

USE AwqafManagement;
GO

-- =============================================
-- 1. جدول أنواع الحسابات المحسن
-- Enhanced Account Types Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountTypes')
BEGIN
    CREATE TABLE AccountTypes (
        AccountTypeId INT IDENTITY(1,1) PRIMARY KEY,
        TypeCode NVARCHAR(10) NOT NULL UNIQUE,
        TypeNameAr NVARCHAR(100) NOT NULL,
        TypeNameEn NVARCHAR(100),
        Description NVARCHAR(500),
        NormalBalance NVARCHAR(10) DEFAULT 'مدين', -- مدين أو دائن
        IsActive BIT DEFAULT 1,
        SortOrder INT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT
    );
    
    CREATE INDEX IX_AccountTypes_Code ON AccountTypes(TypeCode);
    CREATE INDEX IX_AccountTypes_NameAr ON AccountTypes(TypeNameAr);
    
    PRINT 'تم إنشاء جدول أنواع الحسابات المحسن بنجاح';
END

-- =============================================
-- 2. جدول مجموعات الحسابات المحسن
-- Enhanced Account Groups Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountGroups')
BEGIN
    CREATE TABLE AccountGroups (
        AccountGroupId INT IDENTITY(1,1) PRIMARY KEY,
        GroupCode NVARCHAR(10) NOT NULL UNIQUE,
        GroupNameAr NVARCHAR(100) NOT NULL,
        GroupNameEn NVARCHAR(100),
        AccountTypeId INT NOT NULL,
        Description NVARCHAR(500),
        IsActive BIT DEFAULT 1,
        SortOrder INT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT,
        
        CONSTRAINT FK_AccountGroups_AccountType FOREIGN KEY (AccountTypeId) 
            REFERENCES AccountTypes(AccountTypeId)
    );
    
    CREATE INDEX IX_AccountGroups_Code ON AccountGroups(GroupCode);
    CREATE INDEX IX_AccountGroups_NameAr ON AccountGroups(GroupNameAr);
    CREATE INDEX IX_AccountGroups_Type ON AccountGroups(AccountTypeId);
    
    PRINT 'تم إنشاء جدول مجموعات الحسابات المحسن بنجاح';
END

-- =============================================
-- 3. جدول العملات المحسن
-- Enhanced Currencies Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Currencies')
BEGIN
    CREATE TABLE Currencies (
        CurrencyId INT IDENTITY(1,1) PRIMARY KEY,
        CurrencyCode NVARCHAR(3) NOT NULL UNIQUE,
        CurrencyNameAr NVARCHAR(100) NOT NULL,
        CurrencyNameEn NVARCHAR(100),
        Symbol NVARCHAR(10),
        ExchangeRate DECIMAL(18,6) DEFAULT 1.0,
        IsBaseCurrency BIT DEFAULT 0,
        IsActive BIT DEFAULT 1,
        LastUpdated DATETIME2 DEFAULT GETDATE(),
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT
    );
    
    CREATE INDEX IX_Currencies_Code ON Currencies(CurrencyCode);
    CREATE INDEX IX_Currencies_NameAr ON Currencies(CurrencyNameAr);
    
    PRINT 'تم إنشاء جدول العملات المحسن بنجاح';
END

-- =============================================
-- 4. جدول البيانات الشخصية المتقدم
-- Advanced Personal Information Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PersonalInformation')
BEGIN
    CREATE TABLE PersonalInformation (
        PersonalInfoId INT IDENTITY(1,1) PRIMARY KEY,
        
        -- الأسماء
        FullNameAr NVARCHAR(200) NOT NULL,
        FullNameEn NVARCHAR(200),
        FirstNameAr NVARCHAR(100),
        LastNameAr NVARCHAR(100),
        FatherNameAr NVARCHAR(100),
        GrandFatherNameAr NVARCHAR(100),
        
        -- معلومات الهوية
        NationalId NVARCHAR(20),
        PassportNumber NVARCHAR(20),
        IdType NVARCHAR(20) DEFAULT 'هوية وطنية',
        IdExpiryDate DATE,
        
        -- معلومات الاتصال
        Email NVARCHAR(100),
        Phone NVARCHAR(20),
        Mobile NVARCHAR(20),
        Fax NVARCHAR(20),
        Website NVARCHAR(200),
        
        -- العنوان
        AddressAr NVARCHAR(500),
        AddressEn NVARCHAR(500),
        City NVARCHAR(100),
        Region NVARCHAR(100),
        Country NVARCHAR(100) DEFAULT 'المملكة العربية السعودية',
        PostalCode NVARCHAR(10),
        POBox NVARCHAR(20),
        
        -- معلومات مالية
        TaxNumber NVARCHAR(50),
        CommercialRegister NVARCHAR(50),
        CommercialRegisterDate DATE,
        CommercialRegisterExpiry DATE,
        
        -- معلومات بنكية
        BankName NVARCHAR(100),
        BankAccountNumber NVARCHAR(50),
        IBAN NVARCHAR(34),
        SwiftCode NVARCHAR(20),
        
        -- معلومات إضافية
        PersonType NVARCHAR(20) DEFAULT 'فرد',
        Gender NVARCHAR(10),
        BirthDate DATE,
        Nationality NVARCHAR(50),
        MaritalStatus NVARCHAR(20),
        
        -- ملاحظات
        Notes NVARCHAR(2000),
        SpecialInstructions NVARCHAR(1000),
        
        -- حالة السجل
        IsActive BIT DEFAULT 1,
        IsVerified BIT DEFAULT 0,
        
        -- معلومات التدقيق
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT
    );
    
    -- إنشاء الفهارس
    CREATE INDEX IX_PersonalInfo_FullNameAr ON PersonalInformation(FullNameAr);
    CREATE INDEX IX_PersonalInfo_NationalId ON PersonalInformation(NationalId);
    CREATE INDEX IX_PersonalInfo_Email ON PersonalInformation(Email);
    CREATE INDEX IX_PersonalInfo_Phone ON PersonalInformation(Phone);
    CREATE INDEX IX_PersonalInfo_TaxNumber ON PersonalInformation(TaxNumber);
    CREATE INDEX IX_PersonalInfo_Active ON PersonalInformation(IsActive);
    
    PRINT 'تم إنشاء جدول البيانات الشخصية المتقدم بنجاح';
END

-- =============================================
-- 5. جدول دليل الحسابات المتقدم والشامل
-- Advanced and Comprehensive Chart of Accounts Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ChartOfAccounts')
BEGIN
    CREATE TABLE ChartOfAccounts (
        -- المعرفات الأساسية
        AccountId INT IDENTITY(1,1) PRIMARY KEY,
        AccountCode NVARCHAR(20) NOT NULL UNIQUE,

        -- أسماء الحساب
        AccountNameAr NVARCHAR(200) NOT NULL,
        AccountNameEn NVARCHAR(200),

        -- التصنيفات
        AccountTypeId INT NOT NULL,
        AccountGroupId INT NOT NULL,
        ParentAccountId INT NULL,
        PersonalInfoId INT NULL,

        -- الهيكل الهرمي
        AccountLevel INT DEFAULT 1,
        AccountPath NVARCHAR(500), -- المسار الهرمي الكامل مثل: 1.01.001
        IsParent BIT DEFAULT 0,

        -- حالة الحساب
        IsActive BIT DEFAULT 1,
        AllowPosting BIT DEFAULT 1,
        AllowDirectEntry BIT DEFAULT 1, -- السماح بالإدخال المباشر

        -- الوصف والملاحظات
        Description NVARCHAR(1000),
        Notes NVARCHAR(2000), -- ملاحظات إضافية

        -- العملة والأرصدة
        CurrencyId INT DEFAULT 1,
        CurrencyCode NVARCHAR(3) DEFAULT 'SAR',
        OpeningBalance DECIMAL(18,4) DEFAULT 0,
        CurrentBalance DECIMAL(18,4) DEFAULT 0,
        DebitBalance DECIMAL(18,4) DEFAULT 0,
        CreditBalance DECIMAL(18,4) DEFAULT 0,

        -- طبيعة الحساب
        Nature NVARCHAR(10) DEFAULT 'مدين', -- مدين أو دائن
        BalanceType NVARCHAR(10) DEFAULT 'مدين', -- نوع الرصيد

        -- معلومات إضافية
        TaxNumber NVARCHAR(50), -- الرقم الضريبي للحساب
        CommercialRegister NVARCHAR(50), -- السجل التجاري
        BankAccount NVARCHAR(50), -- رقم الحساب البنكي
        IBAN NVARCHAR(34), -- رقم الآيبان

        -- إعدادات متقدمة
        RequiresCostCenter BIT DEFAULT 0, -- يتطلب مركز تكلفة
        RequiresProject BIT DEFAULT 0, -- يتطلب مشروع
        AutoGenerateCode BIT DEFAULT 1, -- توليد الرمز تلقائياً
        SortOrder INT DEFAULT 0, -- ترتيب العرض

        -- معلومات التدقيق
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT,
        LastTransactionDate DATETIME2,

        -- قيود الفهارس والعلاقات
        CONSTRAINT FK_ChartOfAccounts_AccountType FOREIGN KEY (AccountTypeId)
            REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_ChartOfAccounts_AccountGroup FOREIGN KEY (AccountGroupId)
            REFERENCES AccountGroups(AccountGroupId),
        CONSTRAINT FK_ChartOfAccounts_Parent FOREIGN KEY (ParentAccountId)
            REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_ChartOfAccounts_PersonalInfo FOREIGN KEY (PersonalInfoId)
            REFERENCES PersonalInformation(PersonalInfoId),
        CONSTRAINT FK_ChartOfAccounts_Currency FOREIGN KEY (CurrencyId)
            REFERENCES Currencies(CurrencyId)
    );

    -- إنشاء الفهارس لتحسين الأداء
    CREATE INDEX IX_ChartOfAccounts_AccountCode ON ChartOfAccounts(AccountCode);
    CREATE INDEX IX_ChartOfAccounts_AccountNameAr ON ChartOfAccounts(AccountNameAr);
    CREATE INDEX IX_ChartOfAccounts_AccountType ON ChartOfAccounts(AccountTypeId);
    CREATE INDEX IX_ChartOfAccounts_AccountGroup ON ChartOfAccounts(AccountGroupId);
    CREATE INDEX IX_ChartOfAccounts_Parent ON ChartOfAccounts(ParentAccountId);
    CREATE INDEX IX_ChartOfAccounts_Level ON ChartOfAccounts(AccountLevel);
    CREATE INDEX IX_ChartOfAccounts_Active ON ChartOfAccounts(IsActive);
    CREATE INDEX IX_ChartOfAccounts_Path ON ChartOfAccounts(AccountPath);
    CREATE INDEX IX_ChartOfAccounts_PersonalInfo ON ChartOfAccounts(PersonalInfoId);
    CREATE INDEX IX_ChartOfAccounts_Currency ON ChartOfAccounts(CurrencyId);

    PRINT 'تم إنشاء جدول دليل الحسابات المتقدم بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول دليل الحسابات موجود مسبقاً';
END

-- =============================================
-- 6. جدول سجل التدقيق المتقدم
-- Advanced Audit Log Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountAuditLog')
BEGIN
    CREATE TABLE AccountAuditLog (
        AuditId INT IDENTITY(1,1) PRIMARY KEY,
        AccountId INT NOT NULL,

        -- نوع العملية
        OperationType NVARCHAR(20) NOT NULL, -- إنشاء، تعديل، حذف، تفعيل، إلغاء تفعيل

        -- تفاصيل التغيير
        FieldName NVARCHAR(100), -- اسم الحقل المتغير
        OldValue NVARCHAR(MAX), -- القيمة القديمة
        NewValue NVARCHAR(MAX), -- القيمة الجديدة

        -- معلومات العملية
        OperationDate DATETIME2 DEFAULT GETDATE(),
        UserId INT NOT NULL,
        UserName NVARCHAR(100),
        IPAddress NVARCHAR(50),
        UserAgent NVARCHAR(500),

        -- سبب التغيير
        ChangeReason NVARCHAR(500),
        Comments NVARCHAR(1000),

        -- معلومات إضافية
        SessionId NVARCHAR(100),
        TransactionId NVARCHAR(100),

        CONSTRAINT FK_AccountAuditLog_Account FOREIGN KEY (AccountId)
            REFERENCES ChartOfAccounts(AccountId) ON DELETE CASCADE
    );

    -- إنشاء الفهارس
    CREATE INDEX IX_AccountAuditLog_AccountId ON AccountAuditLog(AccountId);
    CREATE INDEX IX_AccountAuditLog_OperationType ON AccountAuditLog(OperationType);
    CREATE INDEX IX_AccountAuditLog_OperationDate ON AccountAuditLog(OperationDate);
    CREATE INDEX IX_AccountAuditLog_UserId ON AccountAuditLog(UserId);

    PRINT 'تم إنشاء جدول سجل التدقيق المتقدم بنجاح';
END

-- =============================================
-- 7. جدول قوالب الحسابات
-- Account Templates Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AccountTemplates')
BEGIN
    CREATE TABLE AccountTemplates (
        TemplateId INT IDENTITY(1,1) PRIMARY KEY,
        TemplateName NVARCHAR(200) NOT NULL,
        Description NVARCHAR(1000),
        IndustryType NVARCHAR(100), -- نوع الصناعة أو القطاع

        -- بيانات القالب (JSON)
        TemplateData NVARCHAR(MAX), -- بيانات الحسابات بصيغة JSON

        -- حالة القالب
        IsActive BIT DEFAULT 1,
        IsDefault BIT DEFAULT 0,

        -- معلومات التدقيق
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME2,
        ModifiedBy INT
    );

    CREATE INDEX IX_AccountTemplates_Name ON AccountTemplates(TemplateName);
    CREATE INDEX IX_AccountTemplates_Industry ON AccountTemplates(IndustryType);

    PRINT 'تم إنشاء جدول قوالب الحسابات بنجاح';
END

GO
