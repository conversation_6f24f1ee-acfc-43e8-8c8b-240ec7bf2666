using System;
using System.Collections.Generic;
using System.Linq;
using Awqaf_Managment.DataAccess;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.Services
{
    /// <summary>
    /// خدمة منطق الأعمال لدليل الحسابات
    /// Chart of Accounts Business Logic Service
    /// </summary>
    public class ChartOfAccountsService
    {
        #region Private Fields

        private readonly ChartOfAccountsDataAccess _chartOfAccountsDataAccess;
        private readonly AccountTypeDataAccess _accountTypeDataAccess;
        private readonly AccountGroupDataAccess _accountGroupDataAccess;
        private readonly ValidationService _validationService;
        private readonly AuditService _auditService;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ خدمة دليل الحسابات
        /// Chart of Accounts Service Constructor
        /// </summary>
        public ChartOfAccountsService()
        {
            _chartOfAccountsDataAccess = new ChartOfAccountsDataAccess();
            _accountTypeDataAccess = new AccountTypeDataAccess();
            _accountGroupDataAccess = new AccountGroupDataAccess();
            _validationService = new ValidationService();
            _auditService = new AuditService();
        }

        #endregion

        #region Account Management

        /// <summary>
        /// حفظ حساب جديد أو تحديث موجود مع التحقق من صحة البيانات
        /// Save New Account or Update Existing with Validation
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <param name="currentUserId">معرف المستخدم الحالي</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<int> SaveAccount(ChartOfAccount account, string currentUserId)
        {
            try
            {
                // التحقق من صحة البيانات الأساسية
                var validationResult = _validationService.ValidateAccount(account);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<int>.Failure(validationResult.ErrorMessage);
                }

                // التحقق من قواعد الأعمال
                var businessRulesResult = ValidateBusinessRules(account);
                if (!businessRulesResult.IsSuccess)
                {
                    return ServiceResult<int>.Failure(businessRulesResult.ErrorMessage);
                }

                // الحصول على الحساب القديم للمقارنة (في حالة التحديث)
                ChartOfAccount oldAccount = null;
                if (account.AccountId > 0)
                {
                    oldAccount = _chartOfAccountsDataAccess.GetAccountById(account.AccountId);
                    if (oldAccount == null)
                    {
                        return ServiceResult<int>.Failure("الحساب المطلوب تحديثه غير موجود");
                    }
                }

                // توليد كود الحساب إذا لم يكن موجوداً
                if (string.IsNullOrEmpty(account.AccountCode))
                {
                    account.AccountCode = GenerateAccountCode(account.AccountTypeId, account.AccountGroupId, account.ParentAccountId);
                }

                // تحديث معلومات الإنشاء/التعديل
                if (account.AccountId == 0)
                {
                    account.CreatedBy = currentUserId;
                    account.CreatedDate = DateTime.Now;
                }
                else
                {
                    account.ModifiedBy = currentUserId;
                    account.ModifiedDate = DateTime.Now;
                }

                // حفظ الحساب
                int accountId = _chartOfAccountsDataAccess.SaveAccount(account);

                // تسجيل التعديلات في سجل التدقيق
                if (oldAccount != null)
                {
                    _auditService.LogAccountChanges(oldAccount, account, currentUserId);
                }
                else
                {
                    _auditService.LogAccountCreation(account, currentUserId);
                }

                return ServiceResult<int>.Success(accountId, "تم حفظ الحساب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في حفظ الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف حساب مع التحقق من قواعد الأعمال
        /// Delete Account with Business Rules Validation
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="currentUserId">معرف المستخدم الحالي</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<bool> DeleteAccount(int accountId, string currentUserId)
        {
            try
            {
                // التحقق من وجود الحساب
                var account = _chartOfAccountsDataAccess.GetAccountById(accountId);
                if (account == null)
                {
                    return ServiceResult<bool>.Failure("الحساب المطلوب حذفه غير موجود");
                }

                // التحقق من إمكانية الحذف
                var canDeleteResult = CanDeleteAccount(accountId);
                if (!canDeleteResult.IsSuccess)
                {
                    return ServiceResult<bool>.Failure(canDeleteResult.ErrorMessage);
                }

                // حذف الحساب
                bool deleted = _chartOfAccountsDataAccess.DeleteAccount(accountId, currentUserId);

                if (deleted)
                {
                    // تسجيل عملية الحذف في سجل التدقيق
                    _auditService.LogAccountDeletion(account, currentUserId);
                    return ServiceResult<bool>.Success(true, "تم حذف الحساب بنجاح");
                }
                else
                {
                    return ServiceResult<bool>.Failure("فشل في حذف الحساب");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في حذف الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// Get Account by ID
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>بيانات الحساب</returns>
        public ServiceResult<ChartOfAccount> GetAccountById(int accountId)
        {
            try
            {
                var account = _chartOfAccountsDataAccess.GetAccountById(accountId);
                if (account == null)
                {
                    return ServiceResult<ChartOfAccount>.Failure("الحساب غير موجود");
                }

                return ServiceResult<ChartOfAccount>.Success(account);
            }
            catch (Exception ex)
            {
                return ServiceResult<ChartOfAccount>.Failure($"خطأ في الحصول على الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث في الحسابات مع الفلترة
        /// Search Accounts with Filtering
        /// </summary>
        /// <param name="searchModel">نموذج البحث</param>
        /// <returns>نتائج البحث</returns>
        public ServiceResult<AccountSearchResult> SearchAccounts(AccountSearchModel searchModel)
        {
            try
            {
                // التحقق من صحة نموذج البحث
                var validationResult = _validationService.ValidateSearchModel(searchModel);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<AccountSearchResult>.Failure(validationResult.ErrorMessage);
                }

                var searchResult = _chartOfAccountsDataAccess.SearchAccounts(searchModel);
                return ServiceResult<AccountSearchResult>.Success(searchResult);
            }
            catch (Exception ex)
            {
                return ServiceResult<AccountSearchResult>.Failure($"خطأ في البحث: {ex.Message}");
            }
        }

        #endregion

        #region Account Hierarchy Management

        /// <summary>
        /// الحصول على الهيكل الهرمي للحسابات
        /// Get Accounts Hierarchy
        /// </summary>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>الهيكل الهرمي</returns>
        public ServiceResult<List<AccountTreeNode>> GetAccountsHierarchy(int? parentAccountId = null)
        {
            try
            {
                var hierarchy = _chartOfAccountsDataAccess.GetAccountsHierarchy(parentAccountId);
                return ServiceResult<List<AccountTreeNode>>.Success(hierarchy);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<AccountTreeNode>>.Failure($"خطأ في الحصول على الهيكل الهرمي: {ex.Message}");
            }
        }

        /// <summary>
        /// نقل حساب إلى حساب أب جديد
        /// Move Account to New Parent
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="newParentId">معرف الحساب الأب الجديد</param>
        /// <param name="currentUserId">معرف المستخدم الحالي</param>
        /// <returns>نتيجة العملية</returns>
        public ServiceResult<bool> MoveAccount(int accountId, int? newParentId, string currentUserId)
        {
            try
            {
                // التحقق من صحة العملية
                var validationResult = ValidateAccountMove(accountId, newParentId);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<bool>.Failure(validationResult.ErrorMessage);
                }

                // الحصول على الحساب الحالي
                var account = _chartOfAccountsDataAccess.GetAccountById(accountId);
                var oldParentId = account.ParentAccountId;

                // تحديث الحساب الأب
                account.ParentAccountId = newParentId;
                account.ModifiedBy = currentUserId;
                account.ModifiedDate = DateTime.Now;

                // حفظ التغييرات
                _chartOfAccountsDataAccess.SaveAccount(account);

                // تسجيل التغيير في سجل التدقيق
                _auditService.LogAccountMove(accountId, oldParentId, newParentId, currentUserId);

                return ServiceResult<bool>.Success(true, "تم نقل الحساب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في نقل الحساب: {ex.Message}");
            }
        }

        #endregion

        #region Account Code Generation

        /// <summary>
        /// توليد كود حساب جديد
        /// Generate New Account Code
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="accountGroupId">معرف مجموعة الحساب</param>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>كود الحساب المولد</returns>
        public string GenerateAccountCode(int accountTypeId, int accountGroupId, int? parentAccountId = null)
        {
            try
            {
                return _chartOfAccountsDataAccess.GenerateAccountCode(accountTypeId, accountGroupId, parentAccountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في توليد كود الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة كود الحساب
        /// Validate Account Code
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <param name="excludeAccountId">معرف الحساب المستثنى من التحقق</param>
        /// <returns>نتيجة التحقق</returns>
        public ServiceResult<bool> ValidateAccountCode(string accountCode, int? excludeAccountId = null)
        {
            try
            {
                // التحقق من تنسيق الكود
                if (!_validationService.IsValidAccountCodeFormat(accountCode))
                {
                    return ServiceResult<bool>.Failure("تنسيق كود الحساب غير صحيح");
                }

                // التحقق من عدم تكرار الكود
                var existingAccount = _chartOfAccountsDataAccess.GetAccountByCode(accountCode);
                if (existingAccount != null && existingAccount.AccountId != excludeAccountId)
                {
                    return ServiceResult<bool>.Failure("كود الحساب موجود مسبقاً");
                }

                return ServiceResult<bool>.Success(true, "كود الحساب صحيح");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من كود الحساب: {ex.Message}");
            }
        }

        #endregion

        #region Business Rules Validation

        /// <summary>
        /// التحقق من قواعد الأعمال للحساب
        /// Validate Account Business Rules
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private ServiceResult<bool> ValidateBusinessRules(ChartOfAccount account)
        {
            try
            {
                // التحقق من صحة نوع الحساب
                var accountType = _accountTypeDataAccess.GetAccountTypeById(account.AccountTypeId);
                if (accountType == null || !accountType.IsActive)
                {
                    return ServiceResult<bool>.Failure("نوع الحساب غير صحيح أو غير نشط");
                }

                // التحقق من صحة مجموعة الحساب
                var accountGroup = _accountGroupDataAccess.GetAccountGroupById(account.AccountGroupId);
                if (accountGroup == null || !accountGroup.IsActive)
                {
                    return ServiceResult<bool>.Failure("مجموعة الحساب غير صحيحة أو غير نشطة");
                }

                // التحقق من تطابق نوع الحساب مع المجموعة
                if (accountGroup.AccountTypeId != account.AccountTypeId)
                {
                    return ServiceResult<bool>.Failure("مجموعة الحساب لا تتطابق مع نوع الحساب المحدد");
                }

                // التحقق من الحساب الأب إذا كان محدداً
                if (account.ParentAccountId.HasValue)
                {
                    var parentAccount = _chartOfAccountsDataAccess.GetAccountById(account.ParentAccountId.Value);
                    if (parentAccount == null || !parentAccount.IsActive)
                    {
                        return ServiceResult<bool>.Failure("الحساب الأب غير صحيح أو غير نشط");
                    }

                    // التحقق من أن الحساب الأب يسمح بوجود حسابات فرعية
                    if (!parentAccount.IsParent)
                    {
                        return ServiceResult<bool>.Failure("الحساب الأب المحدد لا يسمح بوجود حسابات فرعية");
                    }

                    // التحقق من عدم إنشاء حلقة مفرغة في الهيكل الهرمي
                    if (account.AccountId > 0 && IsCircularReference(account.AccountId, account.ParentAccountId.Value))
                    {
                        return ServiceResult<bool>.Failure("لا يمكن إنشاء مرجع دائري في الهيكل الهرمي");
                    }
                }

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من قواعد الأعمال: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف الحساب
        /// Check if Account Can Be Deleted
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private ServiceResult<bool> CanDeleteAccount(int accountId)
        {
            try
            {
                // التحقق من وجود حسابات فرعية
                var childAccounts = _chartOfAccountsDataAccess.GetChildAccounts(accountId);
                if (childAccounts.Any())
                {
                    return ServiceResult<bool>.Failure("لا يمكن حذف الحساب لوجود حسابات فرعية");
                }

                // يمكن إضافة المزيد من التحققات هنا مثل:
                // - وجود قيود محاسبية
                // - وجود أرصدة
                // - وجود معاملات مالية

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من إمكانية الحذف: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة نقل الحساب
        /// Validate Account Move
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="newParentId">معرف الحساب الأب الجديد</param>
        /// <returns>نتيجة التحقق</returns>
        private ServiceResult<bool> ValidateAccountMove(int accountId, int? newParentId)
        {
            try
            {
                if (newParentId.HasValue)
                {
                    // التحقق من عدم نقل الحساب إلى نفسه
                    if (accountId == newParentId.Value)
                    {
                        return ServiceResult<bool>.Failure("لا يمكن نقل الحساب إلى نفسه");
                    }

                    // التحقق من عدم إنشاء حلقة مفرغة
                    if (IsCircularReference(accountId, newParentId.Value))
                    {
                        return ServiceResult<bool>.Failure("لا يمكن إنشاء مرجع دائري في الهيكل الهرمي");
                    }
                }

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من صحة النقل: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود مرجع دائري في الهيكل الهرمي
        /// Check for Circular Reference in Hierarchy
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="parentId">معرف الحساب الأب</param>
        /// <returns>هل يوجد مرجع دائري</returns>
        private bool IsCircularReference(int accountId, int parentId)
        {
            var visitedIds = new HashSet<int>();
            var currentId = parentId;

            while (currentId > 0)
            {
                if (currentId == accountId || visitedIds.Contains(currentId))
                {
                    return true;
                }

                visitedIds.Add(currentId);
                var parentAccount = _chartOfAccountsDataAccess.GetAccountById(currentId);
                currentId = parentAccount?.ParentAccountId ?? 0;
            }

            return false;
        }

        #endregion
    }
}
