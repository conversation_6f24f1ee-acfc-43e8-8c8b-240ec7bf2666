using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Awqaf_Managment.Models;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.DataAccess.Accounting;
using Awqaf_Managment.Services;

namespace Awqaf_Managment.Services.Accounting
{
    /// <summary>
    /// خدمة دليل الحسابات المحسنة والشاملة
    /// Enhanced and Comprehensive Chart of Accounts Service
    /// </summary>
    public class EnhancedChartOfAccountsService
    {
        #region الحقول الخاصة - Private Fields
        
        private readonly EnhancedChartOfAccountsDataAccess _dataAccess;
        private readonly ValidationService _validationService;
        private readonly BusinessRulesService _businessRulesService;
        private readonly AuditService _auditService;

        #endregion

        #region المنشئ - Constructor
        
        /// <summary>
        /// منشئ خدمة دليل الحسابات
        /// Chart of Accounts Service Constructor
        /// </summary>
        public EnhancedChartOfAccountsService()
        {
            _dataAccess = new EnhancedChartOfAccountsDataAccess();
            _validationService = new ValidationService();
            _businessRulesService = new BusinessRulesService();
            _auditService = new AuditService();
        }

        #endregion

        #region عمليات الحسابات الأساسية - Basic Account Operations

        /// <summary>
        /// الحصول على جميع الحسابات مع التفاصيل الكاملة
        /// Get All Accounts with Full Details
        /// </summary>
        /// <param name="includePersonalInfo">تضمين البيانات الشخصية</param>
        /// <param name="includeInactive">تضمين الحسابات غير النشطة</param>
        /// <param name="accountTypeId">فلترة حسب نوع الحساب</param>
        /// <param name="accountGroupId">فلترة حسب مجموعة الحساب</param>
        /// <param name="parentAccountId">فلترة حسب الحساب الأب</param>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>نتيجة العملية مع قائمة الحسابات</returns>
        public async Task<ServiceResult<List<ChartOfAccount>>> GetAllAccountsAsync(
            bool includePersonalInfo = false,
            bool includeInactive = false,
            int? accountTypeId = null,
            int? accountGroupId = null,
            int? parentAccountId = null,
            string searchTerm = null)
        {
            try
            {
                var accounts = await _dataAccess.GetAllAccountsWithDetailsAsync(
                    includePersonalInfo, includeInactive, accountTypeId, 
                    accountGroupId, parentAccountId, searchTerm);

                // ترتيب الحسابات حسب الهيكل الهرمي
                var sortedAccounts = SortAccountsHierarchically(accounts);

                return ServiceResult<List<ChartOfAccount>>.Success(
                    sortedAccounts, 
                    $"تم الحصول على {sortedAccounts.Count} حساب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<List<ChartOfAccount>>.Failure(
                    $"خطأ في الحصول على الحسابات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// Get Account by ID
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>نتيجة العملية مع بيانات الحساب</returns>
        public async Task<ServiceResult<ChartOfAccount>> GetAccountByIdAsync(int accountId)
        {
            try
            {
                if (accountId <= 0)
                {
                    return ServiceResult<ChartOfAccount>.Failure("معرف الحساب غير صحيح");
                }

                var account = await _dataAccess.GetAccountByIdAsync(accountId);
                
                if (account == null)
                {
                    return ServiceResult<ChartOfAccount>.Failure("الحساب غير موجود");
                }

                return ServiceResult<ChartOfAccount>.Success(account, "تم الحصول على الحساب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<ChartOfAccount>.Failure(
                    $"خطأ في الحصول على الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ حساب (إضافة أو تحديث) مع التحقق من القواعد المحاسبية
        /// Save Account (Add or Update) with Accounting Rules Validation
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نتيجة العملية مع معرف الحساب المحفوظ</returns>
        public async Task<ServiceResult<int>> SaveAccountAsync(ChartOfAccount account, int userId)
        {
            try
            {
                // التحقق من صحة البيانات الأساسية
                var validationResult = await ValidateAccountDataAsync(account);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<int>.Failure(validationResult.Message);
                }

                // التحقق من القواعد المحاسبية
                var businessRulesResult = await _businessRulesService.ValidateAccountBusinessRulesAsync(account);
                if (!businessRulesResult.IsSuccess)
                {
                    return ServiceResult<int>.Failure(businessRulesResult.Message);
                }

                // حفظ الحساب القديم للمقارنة (في حالة التحديث)
                ChartOfAccount oldAccount = null;
                if (account.AccountId > 0)
                {
                    var oldAccountResult = await GetAccountByIdAsync(account.AccountId);
                    if (oldAccountResult.IsSuccess)
                    {
                        oldAccount = oldAccountResult.Data;
                    }
                }

                // توليد رمز الحساب إذا لم يكن موجوداً
                if (string.IsNullOrEmpty(account.AccountCode) || account.AutoGenerateCode)
                {
                    account.AccountCode = await _dataAccess.GenerateAccountCodeAsync(
                        account.ParentAccountId, account.AccountTypeId);
                }

                // حساب المستوى والمسار الهرمي
                await CalculateAccountHierarchyAsync(account);

                // حفظ الحساب
                var accountId = await _dataAccess.SaveAccountAsync(account, userId);

                // تسجيل في سجل التدقيق
                if (oldAccount != null)
                {
                    await _auditService.LogAccountChangesAsync(oldAccount, account, userId);
                }
                else
                {
                    await _auditService.LogAccountCreationAsync(account, userId);
                }

                var message = account.AccountId == 0 ? "تم إضافة الحساب بنجاح" : "تم تحديث الحساب بنجاح";
                return ServiceResult<int>.Success(accountId, message);
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في حفظ الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف حساب مع التحقق من القيود
        /// Delete Account with Constraint Checking
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="forceDelete">حذف قسري</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ServiceResult<bool>> DeleteAccountAsync(int accountId, int userId, bool forceDelete = false)
        {
            try
            {
                // التحقق من وجود الحساب
                var accountResult = await GetAccountByIdAsync(accountId);
                if (!accountResult.IsSuccess)
                {
                    return ServiceResult<bool>.Failure("الحساب غير موجود");
                }

                var account = accountResult.Data;

                // التحقق من القواعد المحاسبية للحذف
                var canDeleteResult = await _businessRulesService.CanDeleteAccountAsync(account, forceDelete);
                if (!canDeleteResult.IsSuccess)
                {
                    return ServiceResult<bool>.Failure(canDeleteResult.Message);
                }

                // حذف الحساب
                var deleted = await _dataAccess.DeleteAccountAsync(accountId, userId, forceDelete);

                if (deleted)
                {
                    // تسجيل في سجل التدقيق
                    await _auditService.LogAccountDeletionAsync(account, userId);
                    return ServiceResult<bool>.Success(true, "تم حذف الحساب بنجاح");
                }
                else
                {
                    return ServiceResult<bool>.Failure("فشل في حذف الحساب");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في حذف الحساب: {ex.Message}");
            }
        }

        #endregion

        #region البحث والفلترة المتقدمة - Advanced Search and Filtering

        /// <summary>
        /// البحث المتقدم في الحسابات
        /// Advanced Account Search
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>نتيجة العملية مع نتائج البحث</returns>
        public async Task<ServiceResult<List<ChartOfAccount>>> SearchAccountsAsync(AccountSearchCriteria searchCriteria)
        {
            try
            {
                // التحقق من صحة معايير البحث
                if (searchCriteria == null)
                {
                    return ServiceResult<List<ChartOfAccount>>.Failure("معايير البحث مطلوبة");
                }

                var accounts = await _dataAccess.SearchAccountsAsync(searchCriteria);

                return ServiceResult<List<ChartOfAccount>>.Success(
                    accounts, 
                    $"تم العثور على {accounts.Count} حساب من أصل {searchCriteria.TotalRecords}");
            }
            catch (Exception ex)
            {
                return ServiceResult<List<ChartOfAccount>>.Failure(
                    $"خطأ في البحث المتقدم: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على الحسابات للقوائم المنسدلة
        /// Get Accounts for Dropdown Lists
        /// </summary>
        /// <param name="accountTypeId">نوع الحساب</param>
        /// <param name="excludeParents">استبعاد الحسابات الأب</param>
        /// <param name="activeOnly">الحسابات النشطة فقط</param>
        /// <returns>قائمة الحسابات للاختيار</returns>
        public async Task<ServiceResult<List<AccountSelection>>> GetAccountsForSelectionAsync(
            int? accountTypeId = null, 
            bool excludeParents = false, 
            bool activeOnly = true)
        {
            try
            {
                var accounts = await _dataAccess.GetAllAccountsWithDetailsAsync(
                    includePersonalInfo: false,
                    includeInactive: !activeOnly,
                    accountTypeId: accountTypeId);

                var selectionList = accounts
                    .Where(a => !excludeParents || !a.IsParent)
                    .Select(a => new AccountSelection
                    {
                        AccountId = a.AccountId,
                        AccountCode = a.AccountCode,
                        AccountNameAr = a.AccountNameAr,
                        AccountLevel = a.AccountLevel,
                        IsParent = a.IsParent,
                        DisplayText = $"{a.AccountCode} - {a.AccountNameAr}"
                    })
                    .OrderBy(a => a.AccountCode)
                    .ToList();

                return ServiceResult<List<AccountSelection>>.Success(
                    selectionList, 
                    $"تم الحصول على {selectionList.Count} حساب للاختيار");
            }
            catch (Exception ex)
            {
                return ServiceResult<List<AccountSelection>>.Failure(
                    $"خطأ في الحصول على الحسابات للاختيار: {ex.Message}");
            }
        }

        #endregion

        #region طرق مساعدة - Helper Methods

        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// Validate Account Data
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        private async Task<ServiceResult<bool>> ValidateAccountDataAsync(ChartOfAccount account)
        {
            try
            {
                var errors = account.ValidateData();
                
                // التحقق من عدم تكرار رمز الحساب
                if (!string.IsNullOrEmpty(account.AccountCode))
                {
                    var existingAccount = await _dataAccess.GetAllAccountsWithDetailsAsync(
                        searchTerm: account.AccountCode);
                    
                    var duplicate = existingAccount.FirstOrDefault(a => 
                        a.AccountCode == account.AccountCode && a.AccountId != account.AccountId);
                    
                    if (duplicate != null)
                    {
                        errors.Add("رمز الحساب موجود مسبقاً");
                    }
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.Failure(string.Join(", ", errors));
                }

                return ServiceResult<bool>.Success(true, "البيانات صحيحة");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"خطأ في التحقق من البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// حساب الهيكل الهرمي للحساب
        /// Calculate Account Hierarchy
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        private async Task CalculateAccountHierarchyAsync(ChartOfAccount account)
        {
            if (account.ParentAccountId.HasValue)
            {
                var parentAccount = await _dataAccess.GetAccountByIdAsync(account.ParentAccountId.Value);
                if (parentAccount != null)
                {
                    account.AccountLevel = parentAccount.AccountLevel + 1;
                    account.AccountPath = $"{parentAccount.AccountPath}.{account.AccountCode}";
                }
            }
            else
            {
                account.AccountLevel = 1;
                account.AccountPath = account.AccountCode;
            }
        }

        /// <summary>
        /// ترتيب الحسابات حسب الهيكل الهرمي
        /// Sort Accounts Hierarchically
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>قائمة مرتبة</returns>
        private List<ChartOfAccount> SortAccountsHierarchically(List<ChartOfAccount> accounts)
        {
            return accounts
                .OrderBy(a => a.AccountLevel)
                .ThenBy(a => a.AccountPath)
                .ThenBy(a => a.SortOrder)
                .ThenBy(a => a.AccountCode)
                .ToList();
        }

        #endregion
    }

    /// <summary>
    /// نموذج اختيار الحساب
    /// Account Selection Model
    /// </summary>
    public class AccountSelection
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; }
        public string AccountNameAr { get; set; }
        public int AccountLevel { get; set; }
        public bool IsParent { get; set; }
        public string DisplayText { get; set; }
        public string IndentedDisplayText => new string(' ', (AccountLevel - 1) * 4) + DisplayText;
    }
}
