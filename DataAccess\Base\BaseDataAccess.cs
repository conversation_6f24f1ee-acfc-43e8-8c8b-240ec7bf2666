using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace Awqaf_Managment.DataAccess.Base
{
    /// <summary>
    /// الفئة الأساسية لطبقة الوصول للبيانات
    /// Base Data Access Layer Class
    /// </summary>
    public abstract class BaseDataAccess
    {
        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// Database Connection String
        /// </summary>
        protected string ConnectionString => DatabaseConnection.ConnectionString;

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// Create New Database Connection
        /// </summary>
        protected DatabaseConnection CreateConnection()
        {
            return new DatabaseConnection();
        }

        /// <summary>
        /// تنفيذ إجراء مخزن وإرجاع عدد الصفوف المتأثرة
        /// Execute Stored Procedure and Return Affected Rows Count
        /// </summary>
        /// <param name="procedureName">اسم الإجراء المخزن</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        protected int ExecuteStoredProcedure(string procedureName, params SqlParameter[] parameters)
        {
            using (var db = CreateConnection())
            {
                using (var command = db.CreateCommand(procedureName, CommandType.StoredProcedure))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// تنفيذ إجراء مخزن وإرجاع قيمة واحدة
        /// Execute Stored Procedure and Return Single Value
        /// </summary>
        /// <param name="procedureName">اسم الإجراء المخزن</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        protected object ExecuteStoredProcedureScalar(string procedureName, params SqlParameter[] parameters)
        {
            using (var db = CreateConnection())
            {
                using (var command = db.CreateCommand(procedureName, CommandType.StoredProcedure))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteScalar();
                }
            }
        }

        /// <summary>
        /// تنفيذ إجراء مخزن وإرجاع قارئ البيانات
        /// Execute Stored Procedure and Return Data Reader
        /// </summary>
        /// <param name="procedureName">اسم الإجراء المخزن</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قارئ البيانات</returns>
        protected SqlDataReader ExecuteStoredProcedureReader(string procedureName, params SqlParameter[] parameters)
        {
            var db = CreateConnection();
            var command = db.CreateCommand(procedureName, CommandType.StoredProcedure);
            
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }
            
            return command.ExecuteReader(CommandBehavior.CloseConnection);
        }

        /// <summary>
        /// تنفيذ إجراء مخزن مع معاملة
        /// Execute Stored Procedure with Transaction
        /// </summary>
        /// <param name="procedureName">اسم الإجراء المخزن</param>
        /// <param name="parameters">المعاملات</param>
        /// <param name="db">اتصال قاعدة البيانات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        protected int ExecuteStoredProcedureWithTransaction(string procedureName, SqlParameter[] parameters, DatabaseConnection db)
        {
            using (var command = db.CreateCommand(procedureName, CommandType.StoredProcedure))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                return command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// إنشاء معامل SQL
        /// Create SQL Parameter
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <returns>معامل SQL</returns>
        protected SqlParameter CreateParameter(string name, object value)
        {
            return new SqlParameter(name, value ?? DBNull.Value);
        }

        /// <summary>
        /// إنشاء معامل SQL مع نوع البيانات
        /// Create SQL Parameter with Data Type
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <param name="sqlDbType">نوع البيانات</param>
        /// <returns>معامل SQL</returns>
        protected SqlParameter CreateParameter(string name, object value, SqlDbType sqlDbType)
        {
            var parameter = new SqlParameter(name, sqlDbType);
            parameter.Value = value ?? DBNull.Value;
            return parameter;
        }

        /// <summary>
        /// إنشاء معامل SQL مع نوع البيانات والحجم
        /// Create SQL Parameter with Data Type and Size
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <param name="sqlDbType">نوع البيانات</param>
        /// <param name="size">حجم البيانات</param>
        /// <returns>معامل SQL</returns>
        protected SqlParameter CreateParameter(string name, object value, SqlDbType sqlDbType, int size)
        {
            var parameter = new SqlParameter(name, sqlDbType, size);
            parameter.Value = value ?? DBNull.Value;
            return parameter;
        }

        /// <summary>
        /// إنشاء معامل إخراج
        /// Create Output Parameter
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="sqlDbType">نوع البيانات</param>
        /// <param name="size">حجم البيانات</param>
        /// <returns>معامل الإخراج</returns>
        protected SqlParameter CreateOutputParameter(string name, SqlDbType sqlDbType, int size = 0)
        {
            var parameter = size > 0 ? new SqlParameter(name, sqlDbType, size) : new SqlParameter(name, sqlDbType);
            parameter.Direction = ParameterDirection.Output;
            return parameter;
        }

        /// <summary>
        /// تحويل قيمة قاعدة البيانات إلى نوع محدد
        /// Convert Database Value to Specific Type
        /// </summary>
        /// <typeparam name="T">نوع البيانات المطلوب</typeparam>
        /// <param name="value">القيمة من قاعدة البيانات</param>
        /// <returns>القيمة المحولة</returns>
        protected T ConvertFromDb<T>(object value)
        {
            if (value == null || value == DBNull.Value)
                return default(T);

            if (typeof(T) == typeof(string))
                return (T)(object)(value?.ToString() ?? string.Empty);

            return (T)Convert.ChangeType(value, typeof(T));
        }

        /// <summary>
        /// تحويل قيمة قاعدة البيانات إلى نوع محدد مع قيمة افتراضية
        /// Convert Database Value to Specific Type with Default Value
        /// </summary>
        /// <typeparam name="T">نوع البيانات المطلوب</typeparam>
        /// <param name="value">القيمة من قاعدة البيانات</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة المحولة أو الافتراضية</returns>
        protected T ConvertFromDb<T>(object value, T defaultValue)
        {
            if (value == null || value == DBNull.Value)
                return defaultValue;

            try
            {
                if (typeof(T) == typeof(string))
                    return (T)(object)(value?.ToString() ?? defaultValue?.ToString() ?? string.Empty);

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// تحويل قيمة إلى قيمة قاعدة البيانات
        /// Convert Value to Database Value
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <returns>قيمة قاعدة البيانات</returns>
        protected object ConvertToDb(object value)
        {
            return value ?? DBNull.Value;
        }

        /// <summary>
        /// معالجة الأخطاء وإرجاع رسالة مناسبة
        /// Handle Errors and Return Appropriate Message
        /// </summary>
        /// <param name="ex">الاستثناء</param>
        /// <param name="operation">اسم العملية</param>
        /// <returns>رسالة الخطأ</returns>
        protected string HandleException(Exception ex, string operation)
        {
            if (ex is SqlException sqlEx)
            {
                return $"خطأ في قاعدة البيانات أثناء {operation}: {sqlEx.Message}";
            }
            
            return $"خطأ أثناء {operation}: {ex.Message}";
        }

        /// <summary>
        /// تسجيل الخطأ
        /// Log Error
        /// </summary>
        /// <param name="ex">الاستثناء</param>
        /// <param name="operation">اسم العملية</param>
        protected virtual void LogError(Exception ex, string operation)
        {
            // يمكن تطوير نظام تسجيل الأخطاء هنا
            // Error logging system can be developed here
            System.Diagnostics.Debug.WriteLine($"Error in {operation}: {ex.Message}");
        }
    }
}
