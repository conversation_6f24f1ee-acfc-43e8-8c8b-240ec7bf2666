using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Awqaf_Managment.Models;
using Awqaf_Managment.DataAccess.Base;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات المعلومات الشخصية
    /// Personal Information Data Access Layer
    /// </summary>
    public class PersonalInformationDataAccess : BaseDataAccess
    {
        #region المنشئ - Constructor
        
        /// <summary>
        /// منشئ طبقة الوصول للبيانات
        /// Data Access Layer Constructor
        /// </summary>
        public PersonalInformationDataAccess() : base()
        {
        }

        #endregion

        #region عمليات CRUD الأساسية - Basic CRUD Operations

        /// <summary>
        /// الحصول على جميع البيانات الشخصية
        /// Get All Personal Information
        /// </summary>
        /// <param name="includeInactive">تضمين السجلات غير النشطة</param>
        /// <returns>قائمة البيانات الشخصية</returns>
        public async Task<List<PersonalInformation>> GetAllPersonalInformationAsync(bool includeInactive = false)
        {
            var personalInfoList = new List<PersonalInformation>();

            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    var query = @"
                        SELECT 
                            PersonalInfoId, FullNameAr, FullNameEn, FirstNameAr, LastNameAr,
                            FatherNameAr, GrandFatherNameAr, NationalId, PassportNumber,
                            IdType, IdExpiryDate, Email, Phone, Mobile, Fax, Website,
                            AddressAr, AddressEn, City, Region, Country, PostalCode, POBox,
                            TaxNumber, CommercialRegister, CommercialRegisterDate,
                            CommercialRegisterExpiry, BankName, BankAccountNumber, IBAN,
                            SwiftCode, PersonType, Gender, BirthDate, Nationality,
                            MaritalStatus, Notes, SpecialInstructions, IsActive, IsVerified,
                            CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM PersonalInformation
                        WHERE (@IncludeInactive = 1 OR IsActive = 1)
                        ORDER BY FullNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@IncludeInactive", includeInactive);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                personalInfoList.Add(MapReaderToPersonalInformation(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في الحصول على البيانات الشخصية: {ex.Message}");
                throw new Exception($"خطأ في الحصول على البيانات الشخصية: {ex.Message}", ex);
            }

            return personalInfoList;
        }

        /// <summary>
        /// الحصول على بيانات شخصية بالمعرف
        /// Get Personal Information by ID
        /// </summary>
        /// <param name="personalInfoId">معرف البيانات الشخصية</param>
        /// <returns>البيانات الشخصية</returns>
        public async Task<PersonalInformation> GetPersonalInformationByIdAsync(int personalInfoId)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    var query = @"
                        SELECT 
                            PersonalInfoId, FullNameAr, FullNameEn, FirstNameAr, LastNameAr,
                            FatherNameAr, GrandFatherNameAr, NationalId, PassportNumber,
                            IdType, IdExpiryDate, Email, Phone, Mobile, Fax, Website,
                            AddressAr, AddressEn, City, Region, Country, PostalCode, POBox,
                            TaxNumber, CommercialRegister, CommercialRegisterDate,
                            CommercialRegisterExpiry, BankName, BankAccountNumber, IBAN,
                            SwiftCode, PersonType, Gender, BirthDate, Nationality,
                            MaritalStatus, Notes, SpecialInstructions, IsActive, IsVerified,
                            CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM PersonalInformation
                        WHERE PersonalInfoId = @PersonalInfoId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@PersonalInfoId", personalInfoId);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return MapReaderToPersonalInformation(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في الحصول على البيانات الشخصية {personalInfoId}: {ex.Message}");
                throw new Exception($"خطأ في الحصول على البيانات الشخصية: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// حفظ بيانات شخصية (إضافة أو تحديث)
        /// Save Personal Information (Add or Update)
        /// </summary>
        /// <param name="personalInfo">البيانات الشخصية</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>معرف البيانات الشخصية المحفوظة</returns>
        public async Task<int> SavePersonalInformationAsync(PersonalInformation personalInfo, int userId)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            string query;
                            if (personalInfo.PersonalInfoId == 0)
                            {
                                // إضافة جديدة
                                query = @"
                                    INSERT INTO PersonalInformation (
                                        FullNameAr, FullNameEn, FirstNameAr, LastNameAr, FatherNameAr,
                                        GrandFatherNameAr, NationalId, PassportNumber, IdType, IdExpiryDate,
                                        Email, Phone, Mobile, Fax, Website, AddressAr, AddressEn,
                                        City, Region, Country, PostalCode, POBox, TaxNumber,
                                        CommercialRegister, CommercialRegisterDate, CommercialRegisterExpiry,
                                        BankName, BankAccountNumber, IBAN, SwiftCode, PersonType,
                                        Gender, BirthDate, Nationality, MaritalStatus, Notes,
                                        SpecialInstructions, IsActive, IsVerified, CreatedDate, CreatedBy
                                    )
                                    VALUES (
                                        @FullNameAr, @FullNameEn, @FirstNameAr, @LastNameAr, @FatherNameAr,
                                        @GrandFatherNameAr, @NationalId, @PassportNumber, @IdType, @IdExpiryDate,
                                        @Email, @Phone, @Mobile, @Fax, @Website, @AddressAr, @AddressEn,
                                        @City, @Region, @Country, @PostalCode, @POBox, @TaxNumber,
                                        @CommercialRegister, @CommercialRegisterDate, @CommercialRegisterExpiry,
                                        @BankName, @BankAccountNumber, @IBAN, @SwiftCode, @PersonType,
                                        @Gender, @BirthDate, @Nationality, @MaritalStatus, @Notes,
                                        @SpecialInstructions, @IsActive, @IsVerified, GETDATE(), @UserId
                                    );
                                    SELECT SCOPE_IDENTITY();";
                            }
                            else
                            {
                                // تحديث موجود
                                query = @"
                                    UPDATE PersonalInformation SET
                                        FullNameAr = @FullNameAr, FullNameEn = @FullNameEn,
                                        FirstNameAr = @FirstNameAr, LastNameAr = @LastNameAr,
                                        FatherNameAr = @FatherNameAr, GrandFatherNameAr = @GrandFatherNameAr,
                                        NationalId = @NationalId, PassportNumber = @PassportNumber,
                                        IdType = @IdType, IdExpiryDate = @IdExpiryDate,
                                        Email = @Email, Phone = @Phone, Mobile = @Mobile,
                                        Fax = @Fax, Website = @Website, AddressAr = @AddressAr,
                                        AddressEn = @AddressEn, City = @City, Region = @Region,
                                        Country = @Country, PostalCode = @PostalCode, POBox = @POBox,
                                        TaxNumber = @TaxNumber, CommercialRegister = @CommercialRegister,
                                        CommercialRegisterDate = @CommercialRegisterDate,
                                        CommercialRegisterExpiry = @CommercialRegisterExpiry,
                                        BankName = @BankName, BankAccountNumber = @BankAccountNumber,
                                        IBAN = @IBAN, SwiftCode = @SwiftCode, PersonType = @PersonType,
                                        Gender = @Gender, BirthDate = @BirthDate, Nationality = @Nationality,
                                        MaritalStatus = @MaritalStatus, Notes = @Notes,
                                        SpecialInstructions = @SpecialInstructions, IsActive = @IsActive,
                                        IsVerified = @IsVerified, ModifiedDate = GETDATE(), ModifiedBy = @UserId
                                    WHERE PersonalInfoId = @PersonalInfoId;
                                    SELECT @PersonalInfoId;";
                            }

                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                AddPersonalInformationParameters(command, personalInfo, userId);
                                
                                var result = await command.ExecuteScalarAsync();
                                var personalInfoId = Convert.ToInt32(result);
                                
                                transaction.Commit();
                                return personalInfoId;
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في حفظ البيانات الشخصية: {ex.Message}");
                throw new Exception($"خطأ في حفظ البيانات الشخصية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف بيانات شخصية
        /// Delete Personal Information
        /// </summary>
        /// <param name="personalInfoId">معرف البيانات الشخصية</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<bool> DeletePersonalInformationAsync(int personalInfoId)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();
                    
                    // التحقق من عدم ارتباط البيانات بحسابات
                    var checkQuery = "SELECT COUNT(*) FROM ChartOfAccounts WHERE PersonalInfoId = @PersonalInfoId";
                    using (var checkCommand = new SqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@PersonalInfoId", personalInfoId);
                        var count = (int)await checkCommand.ExecuteScalarAsync();
                        
                        if (count > 0)
                        {
                            throw new Exception("لا يمكن حذف البيانات الشخصية لأنها مرتبطة بحسابات محاسبية");
                        }
                    }
                    
                    var deleteQuery = "DELETE FROM PersonalInformation WHERE PersonalInfoId = @PersonalInfoId";
                    using (var command = new SqlCommand(deleteQuery, connection))
                    {
                        command.Parameters.AddWithValue("@PersonalInfoId", personalInfoId);
                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في حذف البيانات الشخصية {personalInfoId}: {ex.Message}");
                throw new Exception($"خطأ في حذف البيانات الشخصية: {ex.Message}", ex);
            }
        }

        #endregion

        #region طرق مساعدة - Helper Methods

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن البيانات الشخصية
        /// Map Data Reader to Personal Information Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن البيانات الشخصية</returns>
        private PersonalInformation MapReaderToPersonalInformation(SqlDataReader reader)
        {
            return new PersonalInformation
            {
                PersonalInfoId = reader.GetInt32("PersonalInfoId"),
                FullNameAr = reader.GetString("FullNameAr"),
                FullNameEn = reader.IsDBNull("FullNameEn") ? null : reader.GetString("FullNameEn"),
                FirstNameAr = reader.IsDBNull("FirstNameAr") ? null : reader.GetString("FirstNameAr"),
                LastNameAr = reader.IsDBNull("LastNameAr") ? null : reader.GetString("LastNameAr"),
                FatherNameAr = reader.IsDBNull("FatherNameAr") ? null : reader.GetString("FatherNameAr"),
                GrandFatherNameAr = reader.IsDBNull("GrandFatherNameAr") ? null : reader.GetString("GrandFatherNameAr"),
                NationalId = reader.IsDBNull("NationalId") ? null : reader.GetString("NationalId"),
                PassportNumber = reader.IsDBNull("PassportNumber") ? null : reader.GetString("PassportNumber"),
                IdType = reader.IsDBNull("IdType") ? null : reader.GetString("IdType"),
                IdExpiryDate = reader.IsDBNull("IdExpiryDate") ? (DateTime?)null : reader.GetDateTime("IdExpiryDate"),
                Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                Phone = reader.IsDBNull("Phone") ? null : reader.GetString("Phone"),
                Mobile = reader.IsDBNull("Mobile") ? null : reader.GetString("Mobile"),
                Fax = reader.IsDBNull("Fax") ? null : reader.GetString("Fax"),
                Website = reader.IsDBNull("Website") ? null : reader.GetString("Website"),
                AddressAr = reader.IsDBNull("AddressAr") ? null : reader.GetString("AddressAr"),
                AddressEn = reader.IsDBNull("AddressEn") ? null : reader.GetString("AddressEn"),
                City = reader.IsDBNull("City") ? null : reader.GetString("City"),
                Region = reader.IsDBNull("Region") ? null : reader.GetString("Region"),
                Country = reader.IsDBNull("Country") ? null : reader.GetString("Country"),
                PostalCode = reader.IsDBNull("PostalCode") ? null : reader.GetString("PostalCode"),
                POBox = reader.IsDBNull("POBox") ? null : reader.GetString("POBox"),
                TaxNumber = reader.IsDBNull("TaxNumber") ? null : reader.GetString("TaxNumber"),
                CommercialRegister = reader.IsDBNull("CommercialRegister") ? null : reader.GetString("CommercialRegister"),
                CommercialRegisterDate = reader.IsDBNull("CommercialRegisterDate") ? (DateTime?)null : reader.GetDateTime("CommercialRegisterDate"),
                CommercialRegisterExpiry = reader.IsDBNull("CommercialRegisterExpiry") ? (DateTime?)null : reader.GetDateTime("CommercialRegisterExpiry"),
                BankName = reader.IsDBNull("BankName") ? null : reader.GetString("BankName"),
                BankAccountNumber = reader.IsDBNull("BankAccountNumber") ? null : reader.GetString("BankAccountNumber"),
                IBAN = reader.IsDBNull("IBAN") ? null : reader.GetString("IBAN"),
                SwiftCode = reader.IsDBNull("SwiftCode") ? null : reader.GetString("SwiftCode"),
                PersonType = reader.IsDBNull("PersonType") ? null : reader.GetString("PersonType"),
                Gender = reader.IsDBNull("Gender") ? null : reader.GetString("Gender"),
                BirthDate = reader.IsDBNull("BirthDate") ? (DateTime?)null : reader.GetDateTime("BirthDate"),
                Nationality = reader.IsDBNull("Nationality") ? null : reader.GetString("Nationality"),
                MaritalStatus = reader.IsDBNull("MaritalStatus") ? null : reader.GetString("MaritalStatus"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                SpecialInstructions = reader.IsDBNull("SpecialInstructions") ? null : reader.GetString("SpecialInstructions"),
                IsActive = reader.GetBoolean("IsActive"),
                IsVerified = reader.GetBoolean("IsVerified"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                CreatedBy = reader.IsDBNull("CreatedBy") ? (int?)null : reader.GetInt32("CreatedBy"),
                ModifiedDate = reader.IsDBNull("ModifiedDate") ? (DateTime?)null : reader.GetDateTime("ModifiedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? (int?)null : reader.GetInt32("ModifiedBy")
            };
        }

        /// <summary>
        /// إضافة معاملات البيانات الشخصية للأمر
        /// Add Personal Information Parameters to Command
        /// </summary>
        /// <param name="command">الأمر</param>
        /// <param name="personalInfo">البيانات الشخصية</param>
        /// <param name="userId">معرف المستخدم</param>
        private void AddPersonalInformationParameters(SqlCommand command, PersonalInformation personalInfo, int userId)
        {
            if (personalInfo.PersonalInfoId > 0)
                command.Parameters.AddWithValue("@PersonalInfoId", personalInfo.PersonalInfoId);

            command.Parameters.AddWithValue("@FullNameAr", personalInfo.FullNameAr);
            command.Parameters.AddWithValue("@FullNameEn", (object)personalInfo.FullNameEn ?? DBNull.Value);
            command.Parameters.AddWithValue("@FirstNameAr", (object)personalInfo.FirstNameAr ?? DBNull.Value);
            command.Parameters.AddWithValue("@LastNameAr", (object)personalInfo.LastNameAr ?? DBNull.Value);
            command.Parameters.AddWithValue("@FatherNameAr", (object)personalInfo.FatherNameAr ?? DBNull.Value);
            command.Parameters.AddWithValue("@GrandFatherNameAr", (object)personalInfo.GrandFatherNameAr ?? DBNull.Value);
            command.Parameters.AddWithValue("@NationalId", (object)personalInfo.NationalId ?? DBNull.Value);
            command.Parameters.AddWithValue("@PassportNumber", (object)personalInfo.PassportNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@IdType", (object)personalInfo.IdType ?? DBNull.Value);
            command.Parameters.AddWithValue("@IdExpiryDate", (object)personalInfo.IdExpiryDate ?? DBNull.Value);
            command.Parameters.AddWithValue("@Email", (object)personalInfo.Email ?? DBNull.Value);
            command.Parameters.AddWithValue("@Phone", (object)personalInfo.Phone ?? DBNull.Value);
            command.Parameters.AddWithValue("@Mobile", (object)personalInfo.Mobile ?? DBNull.Value);
            command.Parameters.AddWithValue("@Fax", (object)personalInfo.Fax ?? DBNull.Value);
            command.Parameters.AddWithValue("@Website", (object)personalInfo.Website ?? DBNull.Value);
            command.Parameters.AddWithValue("@AddressAr", (object)personalInfo.AddressAr ?? DBNull.Value);
            command.Parameters.AddWithValue("@AddressEn", (object)personalInfo.AddressEn ?? DBNull.Value);
            command.Parameters.AddWithValue("@City", (object)personalInfo.City ?? DBNull.Value);
            command.Parameters.AddWithValue("@Region", (object)personalInfo.Region ?? DBNull.Value);
            command.Parameters.AddWithValue("@Country", (object)personalInfo.Country ?? DBNull.Value);
            command.Parameters.AddWithValue("@PostalCode", (object)personalInfo.PostalCode ?? DBNull.Value);
            command.Parameters.AddWithValue("@POBox", (object)personalInfo.POBox ?? DBNull.Value);
            command.Parameters.AddWithValue("@TaxNumber", (object)personalInfo.TaxNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@CommercialRegister", (object)personalInfo.CommercialRegister ?? DBNull.Value);
            command.Parameters.AddWithValue("@CommercialRegisterDate", (object)personalInfo.CommercialRegisterDate ?? DBNull.Value);
            command.Parameters.AddWithValue("@CommercialRegisterExpiry", (object)personalInfo.CommercialRegisterExpiry ?? DBNull.Value);
            command.Parameters.AddWithValue("@BankName", (object)personalInfo.BankName ?? DBNull.Value);
            command.Parameters.AddWithValue("@BankAccountNumber", (object)personalInfo.BankAccountNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@IBAN", (object)personalInfo.IBAN ?? DBNull.Value);
            command.Parameters.AddWithValue("@SwiftCode", (object)personalInfo.SwiftCode ?? DBNull.Value);
            command.Parameters.AddWithValue("@PersonType", (object)personalInfo.PersonType ?? DBNull.Value);
            command.Parameters.AddWithValue("@Gender", (object)personalInfo.Gender ?? DBNull.Value);
            command.Parameters.AddWithValue("@BirthDate", (object)personalInfo.BirthDate ?? DBNull.Value);
            command.Parameters.AddWithValue("@Nationality", (object)personalInfo.Nationality ?? DBNull.Value);
            command.Parameters.AddWithValue("@MaritalStatus", (object)personalInfo.MaritalStatus ?? DBNull.Value);
            command.Parameters.AddWithValue("@Notes", (object)personalInfo.Notes ?? DBNull.Value);
            command.Parameters.AddWithValue("@SpecialInstructions", (object)personalInfo.SpecialInstructions ?? DBNull.Value);
            command.Parameters.AddWithValue("@IsActive", personalInfo.IsActive);
            command.Parameters.AddWithValue("@IsVerified", personalInfo.IsVerified);
            command.Parameters.AddWithValue("@UserId", userId);
        }

        /// <summary>
        /// تسجيل الأخطاء
        /// Log Errors
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        private void LogError(string message)
        {
            // يمكن تحسين هذا لاحقاً لاستخدام نظام تسجيل متقدم
            System.Diagnostics.Debug.WriteLine($"[PersonalInformationDataAccess] {DateTime.Now}: {message}");
        }

        #endregion

        #region البحث والفلترة - Search and Filtering

        /// <summary>
        /// البحث في البيانات الشخصية
        /// Search Personal Information
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="personType">نوع الشخص</param>
        /// <param name="isActive">حالة النشاط</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<PersonalInformation>> SearchPersonalInformationAsync(
            string searchTerm = null,
            string personType = null,
            bool? isActive = null)
        {
            var personalInfoList = new List<PersonalInformation>();

            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT
                            PersonalInfoId, FullNameAr, FullNameEn, FirstNameAr, LastNameAr,
                            FatherNameAr, GrandFatherNameAr, NationalId, PassportNumber,
                            IdType, IdExpiryDate, Email, Phone, Mobile, Fax, Website,
                            AddressAr, AddressEn, City, Region, Country, PostalCode, POBox,
                            TaxNumber, CommercialRegister, CommercialRegisterDate,
                            CommercialRegisterExpiry, BankName, BankAccountNumber, IBAN,
                            SwiftCode, PersonType, Gender, BirthDate, Nationality,
                            MaritalStatus, Notes, SpecialInstructions, IsActive, IsVerified,
                            CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM PersonalInformation
                        WHERE 1=1
                            AND (@SearchTerm IS NULL OR
                                 FullNameAr LIKE '%' + @SearchTerm + '%' OR
                                 FullNameEn LIKE '%' + @SearchTerm + '%' OR
                                 NationalId LIKE '%' + @SearchTerm + '%' OR
                                 Email LIKE '%' + @SearchTerm + '%' OR
                                 Phone LIKE '%' + @SearchTerm + '%' OR
                                 Mobile LIKE '%' + @SearchTerm + '%')
                            AND (@PersonType IS NULL OR PersonType = @PersonType)
                            AND (@IsActive IS NULL OR IsActive = @IsActive)
                        ORDER BY FullNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", (object)searchTerm ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PersonType", (object)personType ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", (object)isActive ?? DBNull.Value);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                personalInfoList.Add(MapReaderToPersonalInformation(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في البحث في البيانات الشخصية: {ex.Message}");
                throw new Exception($"خطأ في البحث في البيانات الشخصية: {ex.Message}", ex);
            }

            return personalInfoList;
        }

        /// <summary>
        /// الحصول على البيانات الشخصية للاختيار (للقوائم المنسدلة)
        /// Get Personal Information for Selection (for dropdowns)
        /// </summary>
        /// <returns>قائمة مبسطة للاختيار</returns>
        public async Task<List<PersonalInformationSelection>> GetPersonalInformationForSelectionAsync()
        {
            var selectionList = new List<PersonalInformationSelection>();

            try
            {
                using (var connection = GetConnection())
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT PersonalInfoId, FullNameAr, NationalId, Email, Phone, Mobile
                        FROM PersonalInformation
                        WHERE IsActive = 1
                        ORDER BY FullNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                selectionList.Add(new PersonalInformationSelection
                                {
                                    PersonalInfoId = reader.GetInt32("PersonalInfoId"),
                                    FullNameAr = reader.GetString("FullNameAr"),
                                    NationalId = reader.IsDBNull("NationalId") ? null : reader.GetString("NationalId"),
                                    Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                                    Phone = reader.IsDBNull("Phone") ? null : reader.GetString("Phone"),
                                    Mobile = reader.IsDBNull("Mobile") ? null : reader.GetString("Mobile")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في الحصول على البيانات الشخصية للاختيار: {ex.Message}");
                throw new Exception($"خطأ في الحصول على البيانات الشخصية للاختيار: {ex.Message}", ex);
            }

            return selectionList;
        }

        #endregion
    }

    /// <summary>
    /// نموذج مبسط للبيانات الشخصية للاختيار
    /// Simplified Personal Information Selection Model
    /// </summary>
    public class PersonalInformationSelection
    {
        public int PersonalInfoId { get; set; }
        public string FullNameAr { get; set; }
        public string NationalId { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }

        public string DisplayText => $"{FullNameAr} - {NationalId ?? Email ?? Phone ?? Mobile ?? "غير محدد"}";
    }
}
