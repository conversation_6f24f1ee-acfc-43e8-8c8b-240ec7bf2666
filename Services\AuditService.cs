using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using Awqaf_Managment.DataAccess;
using Awqaf_Managment.Models;

namespace Awqaf_Managment.Services
{
    /// <summary>
    /// خدمة تدقيق وتتبع التغييرات
    /// Audit and Change Tracking Service
    /// </summary>
    public class AuditService
    {
        #region Private Fields

        private readonly AuditLogDataAccess _auditLogDataAccess;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ خدمة التدقيق
        /// Audit Service Constructor
        /// </summary>
        public AuditService()
        {
            _auditLogDataAccess = new AuditLogDataAccess();
        }

        #endregion

        #region Account Audit Operations

        /// <summary>
        /// تسجيل إنشاء حساب جديد
        /// Log Account Creation
        /// </summary>
        /// <param name="account">بيانات الحساب الجديد</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="userName">اسم المستخدم</param>
        /// <param name="changeReason">سبب التغيير</param>
        public void LogAccountCreation(ChartOfAccount account, string userId, string userName = null, string changeReason = null)
        {
            try
            {
                var auditLog = new AccountAuditLog
                {
                    AccountId = account.AccountId,
                    Operation = "INSERT",
                    FieldName = "جميع الحقول",
                    OldValue = null,
                    NewValue = $"تم إنشاء حساب جديد: {account.AccountNameAr} ({account.AccountCode})",
                    ChangeReason = changeReason ?? "إنشاء حساب جديد",
                    UserId = userId,
                    UserName = userName ?? userId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetUserAgent()
                };

                _auditLogDataAccess.AddAuditLog(auditLog);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في نظام السجلات
                LogError($"خطأ في تسجيل إنشاء الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل تعديل الحساب
        /// Log Account Changes
        /// </summary>
        /// <param name="oldAccount">بيانات الحساب القديمة</param>
        /// <param name="newAccount">بيانات الحساب الجديدة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="userName">اسم المستخدم</param>
        /// <param name="changeReason">سبب التغيير</param>
        public void LogAccountChanges(ChartOfAccount oldAccount, ChartOfAccount newAccount, string userId, string userName = null, string changeReason = null)
        {
            try
            {
                var changes = GetAccountChanges(oldAccount, newAccount);
                
                foreach (var change in changes)
                {
                    var auditLog = new AccountAuditLog
                    {
                        AccountId = newAccount.AccountId,
                        Operation = "UPDATE",
                        FieldName = change.FieldName,
                        OldValue = change.OldValue,
                        NewValue = change.NewValue,
                        ChangeReason = changeReason ?? "تعديل بيانات الحساب",
                        UserId = userId,
                        UserName = userName ?? userId,
                        IPAddress = GetClientIPAddress(),
                        UserAgent = GetUserAgent()
                    };

                    _auditLogDataAccess.AddAuditLog(auditLog);
                }
            }
            catch (Exception ex)
            {
                LogError($"خطأ في تسجيل تعديل الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل حذف الحساب
        /// Log Account Deletion
        /// </summary>
        /// <param name="account">بيانات الحساب المحذوف</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="userName">اسم المستخدم</param>
        /// <param name="changeReason">سبب الحذف</param>
        public void LogAccountDeletion(ChartOfAccount account, string userId, string userName = null, string changeReason = null)
        {
            try
            {
                var auditLog = new AccountAuditLog
                {
                    AccountId = account.AccountId,
                    Operation = "DELETE",
                    FieldName = "جميع الحقول",
                    OldValue = $"حساب محذوف: {account.AccountNameAr} ({account.AccountCode})",
                    NewValue = null,
                    ChangeReason = changeReason ?? "حذف الحساب",
                    UserId = userId,
                    UserName = userName ?? userId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetUserAgent()
                };

                _auditLogDataAccess.AddAuditLog(auditLog);
            }
            catch (Exception ex)
            {
                LogError($"خطأ في تسجيل حذف الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل نقل الحساب
        /// Log Account Move
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="oldParentId">معرف الحساب الأب القديم</param>
        /// <param name="newParentId">معرف الحساب الأب الجديد</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="userName">اسم المستخدم</param>
        /// <param name="changeReason">سبب النقل</param>
        public void LogAccountMove(int accountId, int? oldParentId, int? newParentId, string userId, string userName = null, string changeReason = null)
        {
            try
            {
                var auditLog = new AccountAuditLog
                {
                    AccountId = accountId,
                    Operation = "UPDATE",
                    FieldName = "الحساب الأب",
                    OldValue = oldParentId?.ToString() ?? "لا يوجد",
                    NewValue = newParentId?.ToString() ?? "لا يوجد",
                    ChangeReason = changeReason ?? "نقل الحساب",
                    UserId = userId,
                    UserName = userName ?? userId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetUserAgent()
                };

                _auditLogDataAccess.AddAuditLog(auditLog);
            }
            catch (Exception ex)
            {
                LogError($"خطأ في تسجيل نقل الحساب: {ex.Message}");
            }
        }

        #endregion

        #region Audit Retrieval Operations

        /// <summary>
        /// الحصول على سجلات التدقيق للحساب
        /// Get Audit Logs for Account
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <returns>سجلات التدقيق</returns>
        public ServiceResult<List<AccountAuditLog>> GetAccountAuditLogs(int accountId, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                var auditLogs = _auditLogDataAccess.GetAuditLogsByAccount(accountId, pageNumber, pageSize);
                return ServiceResult<List<AccountAuditLog>>.Success(auditLogs);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<AccountAuditLog>>.Failure($"خطأ في الحصول على سجلات التدقيق: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث في سجلات التدقيق
        /// Search Audit Logs
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <param name="operation">نوع العملية</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <returns>نتائج البحث</returns>
        public ServiceResult<List<AccountAuditLog>> SearchAuditLogs(string searchText = null, string operation = null, 
            DateTime? fromDate = null, DateTime? toDate = null, string userId = null, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                var auditLogs = _auditLogDataAccess.SearchAuditLogs(searchText, operation, fromDate, toDate, userId, pageNumber, pageSize);
                return ServiceResult<List<AccountAuditLog>>.Success(auditLogs);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<AccountAuditLog>>.Failure($"خطأ في البحث في سجلات التدقيق: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على إحصائيات التدقيق
        /// Get Audit Statistics
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>إحصائيات التدقيق</returns>
        public ServiceResult<AuditLogStatistics> GetAuditStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var statistics = _auditLogDataAccess.GetAuditLogStatistics(fromDate, toDate);
                return ServiceResult<AuditLogStatistics>.Success(statistics);
            }
            catch (Exception ex)
            {
                return ServiceResult<AuditLogStatistics>.Failure($"خطأ في الحصول على إحصائيات التدقيق: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// الحصول على التغييرات بين الحساب القديم والجديد
        /// Get Changes Between Old and New Account
        /// </summary>
        /// <param name="oldAccount">الحساب القديم</param>
        /// <param name="newAccount">الحساب الجديد</param>
        /// <returns>قائمة التغييرات</returns>
        private List<FieldChange> GetAccountChanges(ChartOfAccount oldAccount, ChartOfAccount newAccount)
        {
            var changes = new List<FieldChange>();

            // مقارنة الحقول
            if (oldAccount.AccountCode != newAccount.AccountCode)
                changes.Add(new FieldChange("كود الحساب", oldAccount.AccountCode, newAccount.AccountCode));

            if (oldAccount.AccountName != newAccount.AccountName)
                changes.Add(new FieldChange("اسم الحساب (إنجليزي)", oldAccount.AccountName, newAccount.AccountName));

            if (oldAccount.AccountNameAr != newAccount.AccountNameAr)
                changes.Add(new FieldChange("اسم الحساب (عربي)", oldAccount.AccountNameAr, newAccount.AccountNameAr));

            if (oldAccount.AccountTypeId != newAccount.AccountTypeId)
                changes.Add(new FieldChange("نوع الحساب", oldAccount.AccountTypeId.ToString(), newAccount.AccountTypeId.ToString()));

            if (oldAccount.AccountGroupId != newAccount.AccountGroupId)
                changes.Add(new FieldChange("مجموعة الحساب", oldAccount.AccountGroupId.ToString(), newAccount.AccountGroupId.ToString()));

            if (oldAccount.ParentAccountId != newAccount.ParentAccountId)
                changes.Add(new FieldChange("الحساب الأب", oldAccount.ParentAccountId?.ToString() ?? "لا يوجد", newAccount.ParentAccountId?.ToString() ?? "لا يوجد"));

            if (oldAccount.IsParent != newAccount.IsParent)
                changes.Add(new FieldChange("حساب أب", oldAccount.IsParent ? "نعم" : "لا", newAccount.IsParent ? "نعم" : "لا"));

            if (oldAccount.IsActive != newAccount.IsActive)
                changes.Add(new FieldChange("نشط", oldAccount.IsActive ? "نعم" : "لا", newAccount.IsActive ? "نعم" : "لا"));

            if (oldAccount.AllowPosting != newAccount.AllowPosting)
                changes.Add(new FieldChange("يسمح بالترحيل", oldAccount.AllowPosting ? "نعم" : "لا", newAccount.AllowPosting ? "نعم" : "لا"));

            if (oldAccount.Description != newAccount.Description)
                changes.Add(new FieldChange("الوصف", oldAccount.Description ?? "", newAccount.Description ?? ""));

            if (oldAccount.CurrencyCode != newAccount.CurrencyCode)
                changes.Add(new FieldChange("كود العملة", oldAccount.CurrencyCode, newAccount.CurrencyCode));

            if (oldAccount.OpeningBalance != newAccount.OpeningBalance)
                changes.Add(new FieldChange("الرصيد الافتتاحي", oldAccount.OpeningBalance.ToString("N2"), newAccount.OpeningBalance.ToString("N2")));

            return changes;
        }

        /// <summary>
        /// الحصول على عنوان IP للعميل
        /// Get Client IP Address
        /// </summary>
        /// <returns>عنوان IP</returns>
        private string GetClientIPAddress()
        {
            try
            {
                // في تطبيق WinForms، يمكن الحصول على IP المحلي
                string hostName = Dns.GetHostName();
                IPHostEntry hostEntry = Dns.GetHostEntry(hostName);
                foreach (IPAddress ip in hostEntry.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "غير معروف";
            }
        }

        /// <summary>
        /// الحصول على معلومات المتصفح/التطبيق
        /// Get User Agent Information
        /// </summary>
        /// <returns>معلومات المتصفح</returns>
        private string GetUserAgent()
        {
            try
            {
                return $"Awqaf Management System - Windows Application v1.0";
            }
            catch
            {
                return "غير معروف";
            }
        }

        /// <summary>
        /// تسجيل الأخطاء
        /// Log Errors
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        private void LogError(string message)
        {
            try
            {
                // يمكن تطوير نظام تسجيل الأخطاء هنا
                // مثل الكتابة في ملف أو قاعدة بيانات منفصلة
                System.Diagnostics.Debug.WriteLine($"[Audit Service Error] {DateTime.Now}: {message}");
            }
            catch
            {
                // تجاهل أخطاء تسجيل الأخطاء لتجنب الحلقة المفرغة
            }
        }

        #endregion
    }

    /// <summary>
    /// نموذج تغيير الحقل
    /// Field Change Model
    /// </summary>
    public class FieldChange
    {
        public string FieldName { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }

        public FieldChange(string fieldName, string oldValue, string newValue)
        {
            FieldName = fieldName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }
}
