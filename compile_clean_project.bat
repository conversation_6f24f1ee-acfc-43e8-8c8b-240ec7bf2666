@echo off
echo ========================================
echo تجميع المشروع النظيف (بدون النظام المحاسبي)
echo Compiling Clean Project (Without Accounting System)
echo ========================================

cd /d "%~dp0"

echo.
echo 1. تنظيف المشروع...
echo 1. Cleaning project...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo.
echo 2. إنشاء مجلدات البناء...
echo 2. Creating build directories...
if not exist "bin\Debug" mkdir "bin\Debug"

echo.
echo 3. تجميع المشروع...
echo 3. Compiling project...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Data.dll ^
    /reference:System.ComponentModel.DataAnnotations.dll ^
    /reference:System.Data.SqlClient.dll ^
    /out:bin\Debug\Awqaf_Managment.exe ^
    /win32icon:Resources\app.ico ^
    "Program.cs" ^
    "UI\Forms\MainForm.cs" ^
    "UI\Forms\MainForm.Designer.cs" ^
    "UI\Forms\DashboardForm.cs" ^
    "UI\Forms\DashboardForm.Designer.cs" ^
    "UI\Forms\Security\LoginForm.cs" ^
    "UI\Forms\Security\LoginForm.Designer.cs" ^
    "UI\Forms\Security\UserManagementForm.cs" ^
    "UI\Forms\Security\UserManagementForm.Designer.cs" ^

    "UI\Forms\Accounting\CurrencyManagementForm.cs" ^
    "UI\Forms\Accounting\CurrencyManagementForm.Designer.cs" ^
    "UI\Helpers\ComboBoxItem.cs" ^
    "UI\Helpers\UIHelper.cs" ^
    "Common\Constants.cs" ^
    "Common\Helpers\UIHelper.cs" ^

    "Models\Security\User.cs" ^
    "Models\Accounting\Currency.cs" ^
    "Models\Accounting\CostCenter.cs" ^
    "DataAccess\DatabaseConnection.cs" ^
    "DataAccess\Security\UserDataAccess.cs" ^
    "DataAccess\Security\RoleDataAccess.cs" ^
    "DataAccess\Base\BaseDataAccess.cs" ^
    "DataAccess\Accounting\CurrencyDataAccess.cs" ^
    "Services\Security\AuthenticationService.cs" ^
    "Services\Accounting\CostCenterService.cs" ^
    "TestDatabaseConnection.cs" ^
    "Properties\AssemblyInfo.cs"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم التجميع بنجاح!
    echo ✅ Compilation successful!
    echo.
    echo الملفات المُجمعة:
    echo Compiled files:
    echo - MainForm (النموذج الرئيسي)
    echo - DashboardForm (لوحة التحكم)
    echo - LoginForm (نموذج تسجيل الدخول)
    echo - UserManagementForm (إدارة المستخدمين)

    echo - CurrencyManagementForm (إدارة العملات)
    echo - Security Services (خدمات الأمان)
    echo - Database Connection (اتصال قاعدة البيانات)
    echo.
    echo تشغيل التطبيق...
    echo Running application...
    start "" "bin\Debug\Awqaf_Managment.exe"
) else (
    echo.
    echo ❌ فشل في التجميع
    echo ❌ Compilation failed
    echo.
    echo تحقق من الأخطاء أعلاه
    echo Check errors above
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
