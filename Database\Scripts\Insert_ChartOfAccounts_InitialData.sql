-- ========================================
-- البيانات الأولية للدليل المحاسبي المتطور
-- Advanced Chart of Accounts Initial Data
-- ========================================

USE AwqafManagement;
GO

PRINT '========================================';
PRINT 'بدء إدراج البيانات الأولية للدليل المحاسبي';
PRINT 'Starting Chart of Accounts Initial Data Insertion';
PRINT '========================================';

-- ========================================
-- 1. إدراج أنواع الحسابات
-- ========================================

IF NOT EXISTS (SELECT 1 FROM AccountTypes WHERE TypeCode = 'ASSETS')
BEGIN
    INSERT INTO AccountTypes (TypeCode, TypeName, TypeNameAr, TypeDescription, TypeDescriptionAr, NormalBalance, DisplayOrder)
    VALUES 
    ('ASSETS', 'Assets', 'الأصول', 'Assets and resources owned by the organization', 'الأصول والموارد المملوكة للمنظمة', 'Debit', 1),
    ('LIAB', 'Liabilities', 'الخصوم', 'Debts and obligations owed by the organization', 'الديون والالتزامات المستحقة على المنظمة', 'Credit', 2),
    ('EQUITY', 'Equity', 'حقوق الملكية', 'Owner equity and retained earnings', 'حقوق الملكية والأرباح المحتجزة', 'Credit', 3),
    ('REVENUE', 'Revenue', 'الإيرادات', 'Income and revenue accounts', 'حسابات الدخل والإيرادات', 'Credit', 4),
    ('EXPENSE', 'Expenses', 'المصروفات', 'Expense and cost accounts', 'حسابات المصروفات والتكاليف', 'Debit', 5),
    ('COST', 'Cost of Goods Sold', 'تكلفة البضاعة المباعة', 'Direct costs of goods sold', 'التكاليف المباشرة للبضاعة المباعة', 'Debit', 6);
    
    PRINT '✅ تم إدراج أنواع الحسابات';
END

-- ========================================
-- 2. إدراج مجموعات الحسابات
-- ========================================

DECLARE @AssetsTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = 'ASSETS');
DECLARE @LiabTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = 'LIAB');
DECLARE @EquityTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = 'EQUITY');
DECLARE @RevenueTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = 'REVENUE');
DECLARE @ExpenseTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE TypeCode = 'EXPENSE');

IF NOT EXISTS (SELECT 1 FROM AccountGroups WHERE GroupCode = 'CA')
BEGIN
    INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, GroupDescription, GroupDescriptionAr, AccountTypeId, DisplayOrder)
    VALUES 
    -- مجموعات الأصول
    ('CA', 'Current Assets', 'الأصول المتداولة', 'Assets that can be converted to cash within one year', 'الأصول التي يمكن تحويلها إلى نقد خلال سنة', @AssetsTypeId, 1),
    ('FA', 'Fixed Assets', 'الأصول الثابتة', 'Long-term assets used in operations', 'الأصول طويلة الأجل المستخدمة في العمليات', @AssetsTypeId, 2),
    ('IA', 'Intangible Assets', 'الأصول غير الملموسة', 'Non-physical assets with value', 'الأصول غير المادية ذات القيمة', @AssetsTypeId, 3),
    
    -- مجموعات الخصوم
    ('CL', 'Current Liabilities', 'الخصوم المتداولة', 'Debts due within one year', 'الديون المستحقة خلال سنة', @LiabTypeId, 1),
    ('LTL', 'Long-term Liabilities', 'الخصوم طويلة الأجل', 'Debts due after one year', 'الديون المستحقة بعد سنة', @LiabTypeId, 2),
    
    -- مجموعات حقوق الملكية
    ('CE', 'Capital Equity', 'رأس المال', 'Owner capital and investments', 'رأس مال المالك والاستثمارات', @EquityTypeId, 1),
    ('RE', 'Retained Earnings', 'الأرباح المحتجزة', 'Accumulated profits and losses', 'الأرباح والخسائر المتراكمة', @EquityTypeId, 2),
    
    -- مجموعات الإيرادات
    ('OR', 'Operating Revenue', 'الإيرادات التشغيلية', 'Revenue from main business operations', 'الإيرادات من العمليات التجارية الرئيسية', @RevenueTypeId, 1),
    ('NOR', 'Non-Operating Revenue', 'الإيرادات غير التشغيلية', 'Revenue from other sources', 'الإيرادات من مصادر أخرى', @RevenueTypeId, 2),
    
    -- مجموعات المصروفات
    ('OE', 'Operating Expenses', 'المصروفات التشغيلية', 'Expenses from main business operations', 'المصروفات من العمليات التجارية الرئيسية', @ExpenseTypeId, 1),
    ('AE', 'Administrative Expenses', 'المصروفات الإدارية', 'General and administrative expenses', 'المصروفات العامة والإدارية', @ExpenseTypeId, 2),
    ('SE', 'Selling Expenses', 'مصروفات البيع', 'Sales and marketing expenses', 'مصروفات البيع والتسويق', @ExpenseTypeId, 3);
    
    PRINT '✅ تم إدراج مجموعات الحسابات';
END

-- ========================================
-- 3. إدراج العملات الأساسية
-- ========================================

IF NOT EXISTS (SELECT 1 FROM Currencies WHERE CurrencyCode = 'SAR')
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyName, CurrencyNameAr, CurrencySymbol, ExchangeRate, IsBaseCurrency, DecimalPlaces)
    VALUES 
    ('SAR', 'Saudi Riyal', 'الريال السعودي', 'ر.س', 1.0, 1, 2),
    ('USD', 'US Dollar', 'الدولار الأمريكي', '$', 3.75, 0, 2),
    ('EUR', 'Euro', 'اليورو', '€', 4.10, 0, 2),
    ('GBP', 'British Pound', 'الجنيه الإسترليني', '£', 4.65, 0, 2),
    ('AED', 'UAE Dirham', 'الدرهم الإماراتي', 'د.إ', 1.02, 0, 2),
    ('KWD', 'Kuwaiti Dinar', 'الدينار الكويتي', 'د.ك', 12.25, 0, 3),
    ('QAR', 'Qatari Riyal', 'الريال القطري', 'ر.ق', 1.03, 0, 2),
    ('BHD', 'Bahraini Dinar', 'الدينار البحريني', 'د.ب', 9.95, 0, 3),
    ('OMR', 'Omani Rial', 'الريال العماني', 'ر.ع', 9.75, 0, 3),
    ('JOD', 'Jordanian Dinar', 'الدينار الأردني', 'د.أ', 5.30, 0, 3);
    
    PRINT '✅ تم إدراج العملات الأساسية';
END

-- ========================================
-- 4. إدراج الحسابات الرئيسية الأساسية
-- ========================================

DECLARE @SARCurrencyId INT = (SELECT CurrencyId FROM Currencies WHERE CurrencyCode = 'SAR');
DECLARE @CAGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'CA');
DECLARE @FAGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'FA');
DECLARE @CLGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'CL');
DECLARE @CEGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'CE');
DECLARE @ORGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'OR');
DECLARE @OEGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'OE');

IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = '01.00.000')
BEGIN
    INSERT INTO ChartOfAccounts (
        AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, 
        CurrencyId, AccountLevel, IsParent, IsActive, AllowPosting, 
        Description, DescriptionAr, IsSystemAccount, SortOrder, CreatedBy
    )
    VALUES 
    -- الأصول المتداولة
    ('01.00.000', 'Current Assets', 'الأصول المتداولة', @AssetsTypeId, @CAGroupId, @SARCurrencyId, 1, 1, 1, 0, 'Current Assets Group', 'مجموعة الأصول المتداولة', 1, 1, 'SYSTEM'),
    ('01.01.000', 'Cash and Cash Equivalents', 'النقدية وما في حكمها', @AssetsTypeId, @CAGroupId, @SARCurrencyId, 2, 1, 1, 0, 'Cash and equivalents', 'النقدية وما في حكمها', 1, 1, 'SYSTEM'),
    ('01.01.001', 'Cash in Hand', 'النقدية في الصندوق', @AssetsTypeId, @CAGroupId, @SARCurrencyId, 3, 0, 1, 1, 'Physical cash in office', 'النقدية الموجودة في المكتب', 1, 1, 'SYSTEM'),
    ('01.01.002', 'Bank Account - Main', 'الحساب البنكي الرئيسي', @AssetsTypeId, @CAGroupId, @SARCurrencyId, 3, 0, 1, 1, 'Main bank account', 'الحساب البنكي الرئيسي', 1, 2, 'SYSTEM'),
    
    -- الأصول الثابتة
    ('02.00.000', 'Fixed Assets', 'الأصول الثابتة', @AssetsTypeId, @FAGroupId, @SARCurrencyId, 1, 1, 1, 0, 'Fixed Assets Group', 'مجموعة الأصول الثابتة', 1, 2, 'SYSTEM'),
    ('02.01.000', 'Property, Plant & Equipment', 'الممتلكات والمصانع والمعدات', @AssetsTypeId, @FAGroupId, @SARCurrencyId, 2, 1, 1, 0, 'PP&E', 'الممتلكات والمصانع والمعدات', 1, 1, 'SYSTEM'),
    
    -- الخصوم المتداولة
    ('03.00.000', 'Current Liabilities', 'الخصوم المتداولة', @LiabTypeId, @CLGroupId, @SARCurrencyId, 1, 1, 1, 0, 'Current Liabilities Group', 'مجموعة الخصوم المتداولة', 1, 3, 'SYSTEM'),
    ('03.01.000', 'Accounts Payable', 'الحسابات الدائنة', @LiabTypeId, @CLGroupId, @SARCurrencyId, 2, 1, 1, 0, 'Accounts payable', 'الحسابات الدائنة', 1, 1, 'SYSTEM'),
    
    -- حقوق الملكية
    ('04.00.000', 'Equity', 'حقوق الملكية', @EquityTypeId, @CEGroupId, @SARCurrencyId, 1, 1, 1, 0, 'Equity Group', 'مجموعة حقوق الملكية', 1, 4, 'SYSTEM'),
    ('04.01.000', 'Capital', 'رأس المال', @EquityTypeId, @CEGroupId, @SARCurrencyId, 2, 1, 1, 0, 'Capital accounts', 'حسابات رأس المال', 1, 1, 'SYSTEM'),
    
    -- الإيرادات
    ('05.00.000', 'Revenue', 'الإيرادات', @RevenueTypeId, @ORGroupId, @SARCurrencyId, 1, 1, 1, 0, 'Revenue Group', 'مجموعة الإيرادات', 1, 5, 'SYSTEM'),
    ('05.01.000', 'Operating Revenue', 'الإيرادات التشغيلية', @RevenueTypeId, @ORGroupId, @SARCurrencyId, 2, 1, 1, 0, 'Operating revenue', 'الإيرادات التشغيلية', 1, 1, 'SYSTEM'),
    
    -- المصروفات
    ('06.00.000', 'Expenses', 'المصروفات', @ExpenseTypeId, @OEGroupId, @SARCurrencyId, 1, 1, 1, 0, 'Expenses Group', 'مجموعة المصروفات', 1, 6, 'SYSTEM'),
    ('06.01.000', 'Operating Expenses', 'المصروفات التشغيلية', @ExpenseTypeId, @OEGroupId, @SARCurrencyId, 2, 1, 1, 0, 'Operating expenses', 'المصروفات التشغيلية', 1, 1, 'SYSTEM');
    
    PRINT '✅ تم إدراج الحسابات الرئيسية الأساسية';
END

-- ========================================
-- 5. تحديث المسارات الهرمية
-- ========================================

-- تحديث ParentAccountId للحسابات الفرعية
UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '01.00.000')
WHERE AccountCode IN ('01.01.000');

UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '01.01.000')
WHERE AccountCode IN ('01.01.001', '01.01.002');

UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '02.00.000')
WHERE AccountCode IN ('02.01.000');

UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '03.00.000')
WHERE AccountCode IN ('03.01.000');

UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '04.00.000')
WHERE AccountCode IN ('04.01.000');

UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '05.00.000')
WHERE AccountCode IN ('05.01.000');

UPDATE ChartOfAccounts 
SET ParentAccountId = (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '06.00.000')
WHERE AccountCode IN ('06.01.000');

PRINT '✅ تم تحديث العلاقات الهرمية للحسابات';

PRINT '========================================';
PRINT '✅ تم إدراج جميع البيانات الأولية بنجاح';
PRINT '✅ All initial data inserted successfully';
PRINT '========================================';

GO
