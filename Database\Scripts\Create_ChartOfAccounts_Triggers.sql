-- ========================================
-- المشغلات للدليل المحاسبي المتطور
-- Advanced Chart of Accounts Triggers
-- ========================================

USE AwqafManagement;
GO

PRINT '========================================';
PRINT 'بدء إنشاء المشغلات للدليل المحاسبي';
PRINT 'Starting Chart of Accounts Triggers Creation';
PRINT '========================================';

-- ========================================
-- 1. مشغل سجل التعديلات للحسابات
-- ========================================

IF OBJECT_ID('dbo.TR_ChartOfAccounts_Audit', 'TR') IS NOT NULL
    DROP TRIGGER dbo.TR_ChartOfAccounts_Audit;
GO

CREATE TRIGGER [dbo].[TR_ChartOfAccounts_Audit]
ON [dbo].[ChartOfAccounts]
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Operation NVARCHAR(20);
    DECLARE @UserId NVARCHAR(100) = SYSTEM_USER;
    
    -- تحديد نوع العملية
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
        SET @Operation = 'UPDATE';
    ELSE IF EXISTS (SELECT * FROM inserted)
        SET @Operation = 'INSERT';
    ELSE
        SET @Operation = 'DELETE';
    
    -- تسجيل عمليات الإدراج
    IF @Operation = 'INSERT'
    BEGIN
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, NewValue, ChangedBy)
        SELECT 
            AccountId,
            'INSERT',
            'NEW_RECORD',
            'AccountCode: ' + AccountCode + ', AccountName: ' + AccountName + ', AccountNameAr: ' + AccountNameAr,
            @UserId
        FROM inserted;
    END
    
    -- تسجيل عمليات الحذف
    IF @Operation = 'DELETE'
    BEGIN
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, ChangedBy)
        SELECT 
            AccountId,
            'DELETE',
            'DELETED_RECORD',
            'AccountCode: ' + AccountCode + ', AccountName: ' + AccountName + ', AccountNameAr: ' + AccountNameAr,
            @UserId
        FROM deleted;
    END
    
    -- تسجيل عمليات التعديل
    IF @Operation = 'UPDATE'
    BEGIN
        -- تسجيل تغيير رمز الحساب
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'AccountCode',
            d.AccountCode,
            i.AccountCode,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.AccountCode != d.AccountCode;
        
        -- تسجيل تغيير اسم الحساب
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'AccountName',
            d.AccountName,
            i.AccountName,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.AccountName != d.AccountName;
        
        -- تسجيل تغيير الاسم العربي
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'AccountNameAr',
            d.AccountNameAr,
            i.AccountNameAr,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.AccountNameAr != d.AccountNameAr;
        
        -- تسجيل تغيير نوع الحساب
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'AccountTypeId',
            CAST(d.AccountTypeId AS NVARCHAR(10)),
            CAST(i.AccountTypeId AS NVARCHAR(10)),
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.AccountTypeId != d.AccountTypeId;
        
        -- تسجيل تغيير الحساب الأب
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'ParentAccountId',
            ISNULL(CAST(d.ParentAccountId AS NVARCHAR(10)), 'NULL'),
            ISNULL(CAST(i.ParentAccountId AS NVARCHAR(10)), 'NULL'),
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE ISNULL(i.ParentAccountId, -1) != ISNULL(d.ParentAccountId, -1);
        
        -- تسجيل تغيير حالة النشاط
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'IsActive',
            CASE WHEN d.IsActive = 1 THEN 'نشط' ELSE 'غير نشط' END,
            CASE WHEN i.IsActive = 1 THEN 'نشط' ELSE 'غير نشط' END,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.IsActive != d.IsActive;
        
        -- تسجيل تغيير إمكانية الترحيل
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'AllowPosting',
            CASE WHEN d.AllowPosting = 1 THEN 'مسموح' ELSE 'غير مسموح' END,
            CASE WHEN i.AllowPosting = 1 THEN 'مسموح' ELSE 'غير مسموح' END,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.AllowPosting != d.AllowPosting;
        
        -- تسجيل تغيير الرصيد الافتتاحي
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT 
            i.AccountId,
            'UPDATE',
            'OpeningBalance',
            CAST(d.OpeningBalance AS NVARCHAR(20)),
            CAST(i.OpeningBalance AS NVARCHAR(20)),
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE i.OpeningBalance != d.OpeningBalance;
    END
END
GO

PRINT '✅ تم إنشاء مشغل سجل التعديلات (TR_ChartOfAccounts_Audit)';

-- ========================================
-- 2. مشغل تحديث المسار الهرمي
-- ========================================

IF OBJECT_ID('dbo.TR_ChartOfAccounts_UpdatePath', 'TR') IS NOT NULL
    DROP TRIGGER dbo.TR_ChartOfAccounts_UpdatePath;
GO

CREATE TRIGGER [dbo].[TR_ChartOfAccounts_UpdatePath]
ON [dbo].[ChartOfAccounts]
AFTER INSERT, UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- تحديث المسار الهرمي للحسابات المضافة أو المعدلة
    UPDATE c
    SET AccountPath = CASE 
        WHEN c.ParentAccountId IS NULL THEN ''
        ELSE ISNULL(p.AccountPath, '') + '/' + CAST(c.ParentAccountId AS NVARCHAR(10))
    END,
    AccountLevel = CASE 
        WHEN c.ParentAccountId IS NULL THEN 1
        ELSE ISNULL(p.AccountLevel, 0) + 1
    END
    FROM ChartOfAccounts c
    LEFT JOIN ChartOfAccounts p ON c.ParentAccountId = p.AccountId
    WHERE c.AccountId IN (SELECT AccountId FROM inserted);
    
    -- تحديث حالة الحساب الأب
    UPDATE p
    SET IsParent = CASE 
        WHEN EXISTS (SELECT 1 FROM ChartOfAccounts WHERE ParentAccountId = p.AccountId) THEN 1
        ELSE 0
    END
    FROM ChartOfAccounts p
    WHERE p.AccountId IN (
        SELECT DISTINCT ParentAccountId 
        FROM inserted 
        WHERE ParentAccountId IS NOT NULL
    );
END
GO

PRINT '✅ تم إنشاء مشغل تحديث المسار الهرمي (TR_ChartOfAccounts_UpdatePath)';

-- ========================================
-- 3. مشغل منع حذف الحسابات التي لها حسابات فرعية
-- ========================================

IF OBJECT_ID('dbo.TR_ChartOfAccounts_PreventDelete', 'TR') IS NOT NULL
    DROP TRIGGER dbo.TR_ChartOfAccounts_PreventDelete;
GO

CREATE TRIGGER [dbo].[TR_ChartOfAccounts_PreventDelete]
ON [dbo].[ChartOfAccounts]
INSTEAD OF DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @AccountsWithChildren TABLE (AccountId INT, AccountName NVARCHAR(200));
    DECLARE @SystemAccounts TABLE (AccountId INT, AccountName NVARCHAR(200));
    
    -- التحقق من وجود حسابات فرعية
    INSERT INTO @AccountsWithChildren (AccountId, AccountName)
    SELECT d.AccountId, d.AccountName
    FROM deleted d
    WHERE EXISTS (SELECT 1 FROM ChartOfAccounts WHERE ParentAccountId = d.AccountId);
    
    -- التحقق من الحسابات النظامية
    INSERT INTO @SystemAccounts (AccountId, AccountName)
    SELECT d.AccountId, d.AccountName
    FROM deleted d
    WHERE d.IsSystemAccount = 1;
    
    -- رفع خطأ إذا كانت هناك حسابات لها فروع
    IF EXISTS (SELECT 1 FROM @AccountsWithChildren)
    BEGIN
        DECLARE @ErrorMsg NVARCHAR(500);
        SELECT TOP 1 @ErrorMsg = 'لا يمكن حذف الحساب "' + AccountName + '" لأنه يحتوي على حسابات فرعية'
        FROM @AccountsWithChildren;
        
        RAISERROR(@ErrorMsg, 16, 1);
        RETURN;
    END
    
    -- رفع خطأ إذا كانت هناك حسابات نظامية
    IF EXISTS (SELECT 1 FROM @SystemAccounts)
    BEGIN
        SELECT TOP 1 @ErrorMsg = 'لا يمكن حذف الحساب النظامي "' + AccountName + '"'
        FROM @SystemAccounts;
        
        RAISERROR(@ErrorMsg, 16, 1);
        RETURN;
    END
    
    -- حذف الحسابات المسموح بحذفها
    DELETE FROM ChartOfAccounts 
    WHERE AccountId IN (SELECT AccountId FROM deleted)
    AND AccountId NOT IN (SELECT AccountId FROM @AccountsWithChildren)
    AND AccountId NOT IN (SELECT AccountId FROM @SystemAccounts);
END
GO

PRINT '✅ تم إنشاء مشغل منع الحذف (TR_ChartOfAccounts_PreventDelete)';

PRINT '========================================';
PRINT '✅ تم إنشاء جميع المشغلات بنجاح';
PRINT '✅ All triggers created successfully';
PRINT '========================================';

GO
