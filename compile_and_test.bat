@echo off
echo ========================================
echo تجميع واختبار مشروع إدارة الأوقاف
echo Compiling and Testing Awqaf Management Project
echo ========================================

cd /d "%~dp0"

echo.
echo 1. تنظيف المشروع...
echo 1. Cleaning project...
if exist "bin\Debug" rmdir /s /q "bin\Debug"
if exist "obj\Debug" rmdir /s /q "obj\Debug"

echo.
echo 2. استعادة الحزم...
echo 2. Restoring packages...
nuget restore Awqaf_Managment.sln

echo.
echo 3. تجميع المشروع...
echo 3. Building project...

REM محاولة استخدام MSBuild من Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
    goto :check_result
)

REM محاولة استخدام MSBuild من Visual Studio 2019 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
    goto :check_result
)

REM محاولة استخدام MSBuild من .NET Framework
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
    goto :check_result
)

echo خطأ: لم يتم العثور على MSBuild
echo Error: MSBuild not found
goto :end

:check_result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم التجميع بنجاح!
    echo ✅ Compilation successful!
    echo.
    echo 4. تشغيل التطبيق...
    echo 4. Running application...
    start "" "bin\Debug\Awqaf_Managment.exe"
) else (
    echo.
    echo ❌ فشل في التجميع
    echo ❌ Compilation failed
    echo.
    echo تحقق من الأخطاء أعلاه
    echo Check errors above
)

:end
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
