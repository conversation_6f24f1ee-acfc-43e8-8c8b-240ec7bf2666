using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models
{
    /// <summary>
    /// نموذج العملات
    /// Currency Model
    /// </summary>
    public class Currency
    {
        /// <summary>
        /// معرف العملة
        /// Currency ID
        /// </summary>
        public int CurrencyId { get; set; }

        /// <summary>
        /// رمز العملة (ISO 4217)
        /// Currency Code (ISO 4217)
        /// </summary>
        [Required(ErrorMessage = "رمز العملة مطلوب")]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "رمز العملة يجب أن يكون 3 أحرف")]
        [RegularExpression("^[A-Z]{3}$", ErrorMessage = "رمز العملة يجب أن يكون 3 أحرف كبيرة")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// اسم العملة بالإنجليزية
        /// Currency Name in English
        /// </summary>
        [Required(ErrorMessage = "اسم العملة بالإنجليزية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العملة يجب ألا يزيد عن 100 حرف")]
        public string CurrencyName { get; set; }

        /// <summary>
        /// اسم العملة بالعربية
        /// Currency Name in Arabic
        /// </summary>
        [Required(ErrorMessage = "اسم العملة بالعربية مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العملة يجب ألا يزيد عن 100 حرف")]
        public string CurrencyNameAr { get; set; }

        /// <summary>
        /// رمز العملة للعرض
        /// Currency Symbol for Display
        /// </summary>
        [Required(ErrorMessage = "رمز العملة للعرض مطلوب")]
        [StringLength(10, ErrorMessage = "رمز العملة للعرض يجب ألا يزيد عن 10 أحرف")]
        public string CurrencySymbol { get; set; }

        /// <summary>
        /// سعر الصرف مقابل العملة الأساسية
        /// Exchange Rate against Base Currency
        /// </summary>
        [Required(ErrorMessage = "سعر الصرف مطلوب")]
        [Range(0.000001, 999999.999999, ErrorMessage = "سعر الصرف يجب أن يكون أكبر من صفر")]
        public decimal ExchangeRate { get; set; } = 1.0m;

        /// <summary>
        /// هل هي العملة الأساسية
        /// Is Base Currency
        /// </summary>
        public bool IsBaseCurrency { get; set; } = false;

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// عدد المنازل العشرية
        /// Decimal Places
        /// </summary>
        [Range(0, 6, ErrorMessage = "عدد المنازل العشرية يجب أن يكون بين 0 و 6")]
        public int DecimalPlaces { get; set; } = 2;

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// منشئ السجل
        /// Created By
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل السجل
        /// Modified By
        /// </summary>
        [StringLength(100)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// الحصول على اسم العملة حسب اللغة
        /// Get currency name based on language
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>اسم العملة</returns>
        public string GetDisplayName(bool isArabic = true)
        {
            return isArabic ? CurrencyNameAr : CurrencyName;
        }

        /// <summary>
        /// الحصول على النص الكامل للعملة
        /// Get full currency text
        /// </summary>
        /// <param name="isArabic">هل العرض بالعربية</param>
        /// <returns>النص الكامل للعملة</returns>
        public string GetFullDisplayText(bool isArabic = true)
        {
            var name = GetDisplayName(isArabic);
            return $"{CurrencyCode} - {name} ({CurrencySymbol})";
        }

        /// <summary>
        /// تنسيق المبلغ بالعملة
        /// Format amount with currency
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="showSymbol">عرض رمز العملة</param>
        /// <returns>المبلغ المنسق</returns>
        public string FormatAmount(decimal amount, bool showSymbol = true)
        {
            var formatString = $"N{DecimalPlaces}";
            var formattedAmount = amount.ToString(formatString);
            
            if (showSymbol)
            {
                return $"{formattedAmount} {CurrencySymbol}";
            }
            
            return formattedAmount;
        }

        /// <summary>
        /// تحويل مبلغ من هذه العملة إلى العملة الأساسية
        /// Convert amount from this currency to base currency
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ بالعملة الأساسية</returns>
        public decimal ConvertToBaseCurrency(decimal amount)
        {
            if (IsBaseCurrency)
                return amount;
                
            return amount / ExchangeRate;
        }

        /// <summary>
        /// تحويل مبلغ من العملة الأساسية إلى هذه العملة
        /// Convert amount from base currency to this currency
        /// </summary>
        /// <param name="baseAmount">المبلغ بالعملة الأساسية</param>
        /// <returns>المبلغ بهذه العملة</returns>
        public decimal ConvertFromBaseCurrency(decimal baseAmount)
        {
            if (IsBaseCurrency)
                return baseAmount;
                
            return baseAmount * ExchangeRate;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public string Validate()
        {
            if (string.IsNullOrWhiteSpace(CurrencyCode))
                return "رمز العملة مطلوب";

            if (CurrencyCode.Length != 3)
                return "رمز العملة يجب أن يكون 3 أحرف";

            if (!System.Text.RegularExpressions.Regex.IsMatch(CurrencyCode, "^[A-Z]{3}$"))
                return "رمز العملة يجب أن يكون 3 أحرف كبيرة";

            if (string.IsNullOrWhiteSpace(CurrencyName))
                return "اسم العملة بالإنجليزية مطلوب";

            if (string.IsNullOrWhiteSpace(CurrencyNameAr))
                return "اسم العملة بالعربية مطلوب";

            if (string.IsNullOrWhiteSpace(CurrencySymbol))
                return "رمز العملة للعرض مطلوب";

            if (ExchangeRate <= 0)
                return "سعر الصرف يجب أن يكون أكبر من صفر";

            if (DecimalPlaces < 0 || DecimalPlaces > 6)
                return "عدد المنازل العشرية يجب أن يكون بين 0 و 6";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// نسخ البيانات من نموذج آخر
        /// Copy data from another model
        /// </summary>
        /// <param name="source">النموذج المصدر</param>
        public void CopyFrom(Currency source)
        {
            if (source == null) return;

            CurrencyCode = source.CurrencyCode;
            CurrencyName = source.CurrencyName;
            CurrencyNameAr = source.CurrencyNameAr;
            CurrencySymbol = source.CurrencySymbol;
            ExchangeRate = source.ExchangeRate;
            IsBaseCurrency = source.IsBaseCurrency;
            IsActive = source.IsActive;
            DecimalPlaces = source.DecimalPlaces;
        }

        /// <summary>
        /// تمثيل نصي للكائن
        /// String representation of the object
        /// </summary>
        /// <returns>النص التمثيلي</returns>
        public override string ToString()
        {
            return $"{CurrencyCode} - {CurrencyNameAr} ({CurrencySymbol})";
        }
    }
}
