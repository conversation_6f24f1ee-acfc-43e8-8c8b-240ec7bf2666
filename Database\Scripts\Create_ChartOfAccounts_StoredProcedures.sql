-- ========================================
-- الإجراءات المخزنة للدليل المحاسبي المتطور
-- Advanced Chart of Accounts Stored Procedures
-- ========================================

USE AwqafManagement;
GO

PRINT '========================================';
PRINT 'بدء إنشاء الإجراءات المخزنة للدليل المحاسبي';
PRINT 'Starting Chart of Accounts Stored Procedures Creation';
PRINT '========================================';

-- ========================================
-- 1. إجراء توليد رمز الحساب التلقائي
-- ========================================

IF OBJECT_ID('dbo.SP_GenerateAccountCode', 'P') IS NOT NULL
    DROP PROCEDURE dbo.SP_GenerateAccountCode;
GO

CREATE PROCEDURE [dbo].[SP_GenerateAccountCode]
    @AccountTypeId INT,
    @ParentAccountId INT = NULL,
    @GeneratedCode NVARCHAR(20) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TypeCode NVARCHAR(10);
    DECLARE @ParentCode NVARCHAR(20);
    DECLARE @NextNumber INT;
    DECLARE @NewCode NVARCHAR(20);
    
    BEGIN TRY
        -- الحصول على رمز نوع الحساب
        SELECT @TypeCode = TypeCode 
        FROM AccountTypes 
        WHERE AccountTypeId = @AccountTypeId;
        
        IF @TypeCode IS NULL
        BEGIN
            RAISERROR('نوع الحساب غير موجود', 16, 1);
            RETURN;
        END
        
        -- إذا كان حساب فرعي
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT @ParentCode = AccountCode 
            FROM ChartOfAccounts 
            WHERE AccountId = @ParentAccountId;
            
            IF @ParentCode IS NULL
            BEGIN
                RAISERROR('الحساب الأب غير موجود', 16, 1);
                RETURN;
            END
            
            -- البحث عن أعلى رقم فرعي
            SELECT @NextNumber = ISNULL(MAX(CAST(RIGHT(AccountCode, 3) AS INT)), 0) + 1
            FROM ChartOfAccounts 
            WHERE ParentAccountId = @ParentAccountId
            AND LEN(AccountCode) = LEN(@ParentCode) + 4; -- نفس المستوى
            
            SET @NewCode = @ParentCode + '.' + RIGHT('000' + CAST(@NextNumber AS NVARCHAR(3)), 3);
        END
        ELSE
        BEGIN
            -- حساب رئيسي جديد
            SELECT @NextNumber = ISNULL(MAX(CAST(LEFT(AccountCode, CHARINDEX('.', AccountCode + '.') - 1) AS INT)), 0) + 1
            FROM ChartOfAccounts 
            WHERE AccountTypeId = @AccountTypeId
            AND ParentAccountId IS NULL;
            
            SET @NewCode = RIGHT('00' + CAST(@NextNumber AS NVARCHAR(2)), 2) + '.00.000';
        END
        
        -- التحقق من عدم وجود الرمز مسبقاً
        WHILE EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = @NewCode)
        BEGIN
            SET @NextNumber = @NextNumber + 1;
            IF @ParentAccountId IS NOT NULL
                SET @NewCode = @ParentCode + '.' + RIGHT('000' + CAST(@NextNumber AS NVARCHAR(3)), 3);
            ELSE
                SET @NewCode = RIGHT('00' + CAST(@NextNumber AS NVARCHAR(2)), 2) + '.00.000';
        END
        
        SET @GeneratedCode = @NewCode;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT '✅ تم إنشاء إجراء توليد رمز الحساب (SP_GenerateAccountCode)';

-- ========================================
-- 2. إجراء حفظ الحساب (إضافة/تعديل)
-- ========================================

IF OBJECT_ID('dbo.SP_SaveAccount', 'P') IS NOT NULL
    DROP PROCEDURE dbo.SP_SaveAccount;
GO

CREATE PROCEDURE [dbo].[SP_SaveAccount]
    @AccountId INT = NULL,
    @AccountCode NVARCHAR(20),
    @AccountName NVARCHAR(200),
    @AccountNameAr NVARCHAR(200),
    @AccountTypeId INT,
    @AccountGroupId INT = NULL,
    @ParentAccountId INT = NULL,
    @PersonalInfoId INT = NULL,
    @CurrencyId INT,
    @IsActive BIT = 1,
    @AllowPosting BIT = 1,
    @Description NVARCHAR(1000) = NULL,
    @DescriptionAr NVARCHAR(1000) = NULL,
    @OpeningBalance DECIMAL(18,4) = 0,
    @OpeningBalanceDate DATE = NULL,
    @UserId NVARCHAR(100),
    @ResultAccountId INT OUTPUT,
    @ResultMessage NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @IsUpdate BIT = 0;
    DECLARE @AccountLevel INT = 1;
    DECLARE @IsParent BIT = 0;
    DECLARE @AccountPath NVARCHAR(500);
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من صحة البيانات
        IF LTRIM(RTRIM(@AccountName)) = ''
        BEGIN
            SET @ResultMessage = 'اسم الحساب مطلوب';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF LTRIM(RTRIM(@AccountNameAr)) = ''
        BEGIN
            SET @ResultMessage = 'الاسم العربي للحساب مطلوب';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من وجود رمز الحساب
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = @AccountCode AND (@AccountId IS NULL OR AccountId != @AccountId))
        BEGIN
            SET @ResultMessage = 'رمز الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- حساب المستوى والمسار
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT @AccountLevel = AccountLevel + 1,
                   @AccountPath = ISNULL(AccountPath, '') + '/' + CAST(@ParentAccountId AS NVARCHAR(10))
            FROM ChartOfAccounts 
            WHERE AccountId = @ParentAccountId;
            
            -- تحديث الحساب الأب ليصبح حساب أب
            UPDATE ChartOfAccounts 
            SET IsParent = 1, 
                ModifiedDate = GETDATE(), 
                ModifiedBy = @UserId
            WHERE AccountId = @ParentAccountId;
        END
        ELSE
        BEGIN
            SET @AccountPath = '';
        END
        
        -- تحديد نوع العملية
        IF @AccountId IS NOT NULL AND @AccountId > 0
            SET @IsUpdate = 1;
        
        IF @IsUpdate = 1
        BEGIN
            -- تعديل حساب موجود
            UPDATE ChartOfAccounts 
            SET AccountCode = @AccountCode,
                AccountName = @AccountName,
                AccountNameAr = @AccountNameAr,
                AccountTypeId = @AccountTypeId,
                AccountGroupId = @AccountGroupId,
                ParentAccountId = @ParentAccountId,
                PersonalInfoId = @PersonalInfoId,
                CurrencyId = @CurrencyId,
                AccountLevel = @AccountLevel,
                IsActive = @IsActive,
                AllowPosting = @AllowPosting,
                Description = @Description,
                DescriptionAr = @DescriptionAr,
                OpeningBalance = @OpeningBalance,
                OpeningBalanceDate = @OpeningBalanceDate,
                AccountPath = @AccountPath,
                ModifiedDate = GETDATE(),
                ModifiedBy = @UserId
            WHERE AccountId = @AccountId;
            
            SET @ResultAccountId = @AccountId;
            SET @ResultMessage = 'تم تعديل الحساب بنجاح';
        END
        ELSE
        BEGIN
            -- إضافة حساب جديد
            INSERT INTO ChartOfAccounts (
                AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId,
                ParentAccountId, PersonalInfoId, CurrencyId, AccountLevel, IsParent,
                IsActive, AllowPosting, Description, DescriptionAr, OpeningBalance,
                OpeningBalanceDate, CurrentBalance, AccountPath, CreatedBy
            )
            VALUES (
                @AccountCode, @AccountName, @AccountNameAr, @AccountTypeId, @AccountGroupId,
                @ParentAccountId, @PersonalInfoId, @CurrencyId, @AccountLevel, @IsParent,
                @IsActive, @AllowPosting, @Description, @DescriptionAr, @OpeningBalance,
                @OpeningBalanceDate, @OpeningBalance, @AccountPath, @UserId
            );
            
            SET @ResultAccountId = SCOPE_IDENTITY();
            SET @ResultMessage = 'تم إضافة الحساب بنجاح';
        END
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @ResultMessage = 'خطأ: ' + ERROR_MESSAGE();
        SET @ResultAccountId = -1;
    END CATCH
END
GO

PRINT '✅ تم إنشاء إجراء حفظ الحساب (SP_SaveAccount)';

-- ========================================
-- 3. إجراء البحث في الحسابات
-- ========================================

IF OBJECT_ID('dbo.SP_SearchAccounts', 'P') IS NOT NULL
    DROP PROCEDURE dbo.SP_SearchAccounts;
GO

CREATE PROCEDURE [dbo].[SP_SearchAccounts]
    @SearchText NVARCHAR(200) = NULL,
    @AccountTypeId INT = NULL,
    @AccountGroupId INT = NULL,
    @ParentAccountId INT = NULL,
    @IsActive BIT = NULL,
    @AllowPosting BIT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SortColumn NVARCHAR(50) = 'AccountCode',
    @SortDirection NVARCHAR(4) = 'ASC'
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @WhereClause NVARCHAR(MAX) = '';
    DECLARE @OrderClause NVARCHAR(100);

    -- بناء شرط البحث
    IF @SearchText IS NOT NULL AND LTRIM(RTRIM(@SearchText)) != ''
    BEGIN
        SET @WhereClause = @WhereClause + ' AND (c.AccountCode LIKE ''%' + @SearchText + '%''
                                              OR c.AccountName LIKE ''%' + @SearchText + '%''
                                              OR c.AccountNameAr LIKE ''%' + @SearchText + '%''
                                              OR c.Description LIKE ''%' + @SearchText + '%''
                                              OR c.DescriptionAr LIKE ''%' + @SearchText + '%'')';
    END

    IF @AccountTypeId IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND c.AccountTypeId = ' + CAST(@AccountTypeId AS NVARCHAR(10));

    IF @AccountGroupId IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND c.AccountGroupId = ' + CAST(@AccountGroupId AS NVARCHAR(10));

    IF @ParentAccountId IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND c.ParentAccountId = ' + CAST(@ParentAccountId AS NVARCHAR(10));

    IF @IsActive IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND c.IsActive = ' + CAST(@IsActive AS NVARCHAR(1));

    IF @AllowPosting IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND c.AllowPosting = ' + CAST(@AllowPosting AS NVARCHAR(1));

    -- إزالة AND الأولى
    IF LEN(@WhereClause) > 0
        SET @WhereClause = 'WHERE ' + SUBSTRING(@WhereClause, 6, LEN(@WhereClause));

    -- ترتيب النتائج
    SET @OrderClause = 'ORDER BY ' + @SortColumn + ' ' + @SortDirection;

    -- بناء الاستعلام النهائي
    SET @SQL = '
    SELECT
        c.AccountId,
        c.AccountCode,
        c.AccountName,
        c.AccountNameAr,
        c.AccountTypeId,
        at.TypeName,
        at.TypeNameAr,
        c.AccountGroupId,
        ag.GroupName,
        ag.GroupNameAr,
        c.ParentAccountId,
        pc.AccountName AS ParentAccountName,
        pc.AccountNameAr AS ParentAccountNameAr,
        c.PersonalInfoId,
        pi.FullName,
        pi.FullNameAr,
        c.CurrencyId,
        cur.CurrencyCode,
        cur.CurrencyName,
        cur.CurrencyNameAr,
        c.AccountLevel,
        c.IsParent,
        c.IsActive,
        c.AllowPosting,
        c.Description,
        c.DescriptionAr,
        c.OpeningBalance,
        c.CurrentBalance,
        c.DebitBalance,
        c.CreditBalance,
        c.LastTransactionDate,
        c.CreatedDate,
        c.CreatedBy,
        c.ModifiedDate,
        c.ModifiedBy,
        COUNT(*) OVER() AS TotalRecords
    FROM ChartOfAccounts c
    LEFT JOIN AccountTypes at ON c.AccountTypeId = at.AccountTypeId
    LEFT JOIN AccountGroups ag ON c.AccountGroupId = ag.AccountGroupId
    LEFT JOIN ChartOfAccounts pc ON c.ParentAccountId = pc.AccountId
    LEFT JOIN PersonalInformation pi ON c.PersonalInfoId = pi.PersonalInfoId
    LEFT JOIN Currencies cur ON c.CurrencyId = cur.CurrencyId
    ' + @WhereClause + '
    ' + @OrderClause + '
    OFFSET ' + CAST(@Offset AS NVARCHAR(10)) + ' ROWS
    FETCH NEXT ' + CAST(@PageSize AS NVARCHAR(10)) + ' ROWS ONLY';

    EXEC sp_executesql @SQL;
END
GO

PRINT '✅ تم إنشاء إجراء البحث في الحسابات (SP_SearchAccounts)';

-- ========================================
-- 4. إجراء الحصول على التسلسل الهرمي للحسابات
-- ========================================

IF OBJECT_ID('dbo.SP_GetAccountsHierarchy', 'P') IS NOT NULL
    DROP PROCEDURE dbo.SP_GetAccountsHierarchy;
GO

CREATE PROCEDURE [dbo].[SP_GetAccountsHierarchy]
    @ParentAccountId INT = NULL,
    @IncludeInactive BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    WITH AccountHierarchy AS (
        -- المستوى الأول (الحسابات الرئيسية أو الفرعية للحساب المحدد)
        SELECT
            c.AccountId,
            c.AccountCode,
            c.AccountName,
            c.AccountNameAr,
            c.AccountTypeId,
            at.TypeName,
            at.TypeNameAr,
            c.AccountGroupId,
            ag.GroupName,
            ag.GroupNameAr,
            c.ParentAccountId,
            c.PersonalInfoId,
            pi.FullName,
            pi.FullNameAr,
            c.CurrencyId,
            cur.CurrencyCode,
            cur.CurrencySymbol,
            c.AccountLevel,
            c.IsParent,
            c.IsActive,
            c.AllowPosting,
            c.Description,
            c.DescriptionAr,
            c.OpeningBalance,
            c.CurrentBalance,
            c.DebitBalance,
            c.CreditBalance,
            c.SortOrder,
            CAST(c.AccountCode AS NVARCHAR(MAX)) AS HierarchyPath,
            CAST(c.AccountName AS NVARCHAR(MAX)) AS HierarchyName,
            CAST(c.AccountNameAr AS NVARCHAR(MAX)) AS HierarchyNameAr,
            0 AS HierarchyLevel
        FROM ChartOfAccounts c
        LEFT JOIN AccountTypes at ON c.AccountTypeId = at.AccountTypeId
        LEFT JOIN AccountGroups ag ON c.AccountGroupId = ag.AccountGroupId
        LEFT JOIN PersonalInformation pi ON c.PersonalInfoId = pi.PersonalInfoId
        LEFT JOIN Currencies cur ON c.CurrencyId = cur.CurrencyId
        WHERE c.ParentAccountId = @ParentAccountId
        AND (@IncludeInactive = 1 OR c.IsActive = 1)

        UNION ALL

        -- المستويات التالية (العودية)
        SELECT
            c.AccountId,
            c.AccountCode,
            c.AccountName,
            c.AccountNameAr,
            c.AccountTypeId,
            at.TypeName,
            at.TypeNameAr,
            c.AccountGroupId,
            ag.GroupName,
            ag.GroupNameAr,
            c.ParentAccountId,
            c.PersonalInfoId,
            pi.FullName,
            pi.FullNameAr,
            c.CurrencyId,
            cur.CurrencyCode,
            cur.CurrencySymbol,
            c.AccountLevel,
            c.IsParent,
            c.IsActive,
            c.AllowPosting,
            c.Description,
            c.DescriptionAr,
            c.OpeningBalance,
            c.CurrentBalance,
            c.DebitBalance,
            c.CreditBalance,
            c.SortOrder,
            ah.HierarchyPath + ' > ' + c.AccountCode,
            ah.HierarchyName + ' > ' + c.AccountName,
            ah.HierarchyNameAr + ' > ' + c.AccountNameAr,
            ah.HierarchyLevel + 1
        FROM ChartOfAccounts c
        INNER JOIN AccountHierarchy ah ON c.ParentAccountId = ah.AccountId
        LEFT JOIN AccountTypes at ON c.AccountTypeId = at.AccountTypeId
        LEFT JOIN AccountGroups ag ON c.AccountGroupId = ag.AccountGroupId
        LEFT JOIN PersonalInformation pi ON c.PersonalInfoId = pi.PersonalInfoId
        LEFT JOIN Currencies cur ON c.CurrencyId = cur.CurrencyId
        WHERE (@IncludeInactive = 1 OR c.IsActive = 1)
    )
    SELECT *
    FROM AccountHierarchy
    ORDER BY HierarchyLevel, SortOrder, AccountCode;
END
GO

PRINT '✅ تم إنشاء إجراء التسلسل الهرمي (SP_GetAccountsHierarchy)';

PRINT '========================================';
PRINT '✅ تم إنشاء جميع الإجراءات المخزنة بنجاح';
PRINT '✅ All stored procedures created successfully';
PRINT '========================================';

GO
