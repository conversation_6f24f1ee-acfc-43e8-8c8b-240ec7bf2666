# 📊 نظام إدارة دليل الحسابات - Chart of Accounts Management System

## 🎯 نظرة عامة | Overview

تم إنشاء نظام إدارة دليل الحسابات بالكامل من الصفر بتصميم عصري ومهني خالٍ من الأخطاء. يوفر النظام واجهة شاملة لإدارة الحسابات المحاسبية مع دعم كامل للغة العربية والتخطيط من اليمين إلى اليسار (RTL).

## ✨ المميزات الرئيسية | Key Features

### 🏗️ البنية المعمارية | Architecture
- **طبقات منفصلة**: UI/Services/DataAccess/Models
- **أنماط C# حديثة**: Async/Await, Nullable Reference Types
- **التحقق من البيانات**: Data Annotations & Custom Validation
- **معالجة الأخطاء**: Comprehensive Error Handling

### 🎨 واجهة المستخدم | User Interface
- **تصميم عصري مسطح**: Modern Flat UI Design
- **دعم RTL كامل**: Full Arabic RTL Support
- **خطوط عربية**: Cairo, Tajawal, Noto Kufi
- **تخطيط ذكي**: Split Container with Tree & Tabbed Interface
- **ألوان مهنية**: Professional Color Scheme
- **أزرار ملونة**: Color-coded Action Buttons

### 📊 إدارة الحسابات | Account Management
- **هيكل هرمي**: Hierarchical Account Structure
- **توليد أكواد تلقائي**: Auto-generated Account Codes (X.XX.XXX)
- **أنواع ومجموعات**: Account Types & Groups
- **حسابات أب وفرع**: Parent-Child Relationships
- **عملات متعددة**: Multi-currency Support
- **أرصدة افتتاحية وحالية**: Opening & Current Balances

### 🔍 البحث والتصفية | Search & Filter
- **بحث نصي**: Text Search across Code & Names
- **تصفية متقدمة**: Advanced Filtering Options
- **عرض شجري**: Tree View Navigation
- **عرض جدولي**: Grid View with Sorting

## 📁 هيكل الملفات | File Structure

```
UI/Forms/Accounting/
├── ChartOfAccountsManagementForm.cs              # الكود الخلفي الرئيسي
├── ChartOfAccountsManagementForm.Designer.cs     # تصميم الواجهة

Models/Accounting/
├── AccountType.cs                      # نموذج نوع الحساب
├── AccountGroup.cs                     # نموذج مجموعة الحساب
└── ChartOfAccount.cs                   # نموذج الحساب الرئيسي

Services/Accounting/
└── ChartOfAccountsService.cs           # طبقة الخدمات

DataAccess/Accounting/
└── ChartOfAccountsDataAccess.cs        # طبقة الوصول للبيانات

Database/Scripts/
├── CreateChartOfAccountsTable.sql      # إنشاء الجداول
└── InsertBasicAccountData.sql          # البيانات الأساسية

Tests/
└── ChartOfAccountsTest.cs              # اختبارات النظام
```

## 🗄️ قاعدة البيانات | Database Schema

### جداول النظام | System Tables

#### AccountTypes (أنواع الحسابات)
- `AccountTypeId` (PK)
- `TypeCode`, `TypeNameAr`, `TypeNameEn`
- `Nature` (DEBIT/CREDIT)
- `IsActive`, `CreatedDate`, `ModifiedDate`

#### AccountGroups (مجموعات الحسابات)
- `AccountGroupId` (PK)
- `GroupCode`, `GroupNameAr`, `GroupNameEn`
- `AccountTypeId` (FK)
- `IsActive`, `CreatedDate`, `ModifiedDate`

#### ChartOfAccounts (دليل الحسابات)
- `AccountId` (PK)
- `AccountCode`, `AccountNameAr`, `AccountNameEn`
- `AccountTypeId` (FK), `AccountGroupId` (FK)
- `ParentAccountId` (FK), `AccountLevel`
- `IsParent`, `IsActive`, `AllowPosting`
- `CurrencyCode`, `OpeningBalance`, `CurrentBalance`
- `Description`, `CreatedDate`, `ModifiedDate`

## 🚀 كيفية الاستخدام | How to Use

### 1. تشغيل النظام | Running the System
```csharp
// فتح نموذج إدارة دليل الحسابات
var chartOfAccountsForm = new ChartOfAccountsManagementForm();
chartOfAccountsForm.ShowDialog();
```

### 2. إضافة حساب جديد | Adding New Account
1. انقر على زر "جديد" (F2)
2. اختر نوع الحساب
3. اختر مجموعة الحساب
4. انقر "توليد الرمز" للحصول على رمز تلقائي
5. أدخل اسم الحساب العربي (مطلوب)
6. أدخل باقي البيانات حسب الحاجة
7. انقر "حفظ" (F4)

### 3. تعديل حساب | Editing Account
1. اختر الحساب من الشجرة أو الجدول
2. انقر على زر "تعديل" (F3)
3. عدّل البيانات المطلوبة
4. انقر "حفظ" (F4)

### 4. حذف حساب | Deleting Account
1. اختر الحساب المراد حذفه
2. انقر على زر "حذف" (Delete)
3. أكد عملية الحذف

## ⌨️ اختصارات لوحة المفاتيح | Keyboard Shortcuts

- `F2` - إضافة حساب جديد
- `F3` - تعديل الحساب المحدد
- `F4` - حفظ التغييرات
- `F5` - تحديث البيانات
- `Escape` - إلغاء العملية الحالية
- `Delete` - حذف الحساب المحدد

## 🎨 التخصيص | Customization

### الألوان | Colors
- **أخضر**: أزرار الإضافة والحفظ
- **أزرق**: أزرار التعديل والمعلومات
- **أحمر**: أزرار الحذف
- **رمادي**: أزرار الإلغاء
- **بنفسجي**: أزرار التحديث
- **برتقالي**: أزرار التصدير

### الخطوط | Fonts
- **Cairo**: الخط الأساسي للواجهة
- **Tajawal**: خط بديل
- **Noto Kufi Arabic**: خط احتياطي

## 🧪 الاختبار | Testing

### تشغيل الاختبارات | Running Tests
```csharp
// اختبار شامل
await ChartOfAccountsTest.RunComprehensiveTestAsync();

// اختبار سريع
bool isWorking = await ChartOfAccountsTest.QuickTestAsync();

// اختبار يدوي
ChartOfAccountsTest.ShowFormForManualTesting();
```

## 🔧 متطلبات النظام | System Requirements

- **.NET Framework 4.8+**
- **SQL Server 2016+**
- **Windows 10+**
- **4GB RAM minimum**
- **100MB disk space**

## 📝 ملاحظات مهمة | Important Notes

1. **قاعدة البيانات**: تأكد من تشغيل SQL Server باسم المثيل `NAJEEB`
2. **قاعدة البيانات**: اسم قاعدة البيانات يجب أن يكون `AwqafManagement`
3. **الأذونات**: تأكد من وجود أذونات كافية للوصول لقاعدة البيانات
4. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية قبل أي تعديلات كبيرة

## 🆘 استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

1. **خطأ الاتصال بقاعدة البيانات**
   - تحقق من تشغيل SQL Server
   - تحقق من اسم المثيل والقاعدة
   - تحقق من صحة سلسلة الاتصال

2. **خطأ في تحميل البيانات**
   - تحقق من وجود الجداول
   - تحقق من وجود البيانات الأساسية
   - تحقق من الأذونات

3. **مشاكل في الواجهة**
   - تحقق من دقة الشاشة
   - تحقق من إعدادات DPI
   - أعد تشغيل التطبيق

## 👨‍💻 المطور | Developer

**نظام إدارة الأوقاف**  
تم التطوير بواسطة: Augment Agent  
التاريخ: 2025-07-03  
الإصدار: 1.0.0

---

## 🎉 تم الانتهاء بنجاح!

تم إنشاء نظام إدارة دليل الحسابات بالكامل بتصميم عصري ومهني خالٍ من الأخطاء. النظام جاهز للاستخدام ويتضمن جميع الميزات المطلوبة مع دعم كامل للغة العربية.
