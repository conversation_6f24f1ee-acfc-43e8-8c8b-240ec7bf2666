-- =============================================
-- إنشاء جدول دليل الحسابات الرئيسي
-- Create Main Chart of Accounts Table
-- =============================================

USE AwqafManagement;
GO

-- إنشاء جدول دليل الحسابات الرئيسي
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ChartOfAccounts](
        [AccountId] [int] IDENTITY(1,1) NOT NULL,
        [AccountCode] [nvarchar](20) NOT NULL,
        [AccountNameAr] [nvarchar](200) NOT NULL,
        [AccountNameEn] [nvarchar](200) NULL,
        [AccountTypeId] [int] NOT NULL,
        [AccountGroupId] [int] NOT NULL,
        [ParentAccountId] [int] NULL,
        [AccountLevel] [int] NOT NULL DEFAULT(1),
        [IsParent] [bit] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [AllowPosting] [bit] NOT NULL DEFAULT(1),
        [Description] [nvarchar](500) NULL,
        [CurrencyCode] [nvarchar](10) NOT NULL DEFAULT('SAR'),
        [OpeningBalance] [decimal](18,4) NOT NULL DEFAULT(0),
        [CurrentBalance] [decimal](18,4) NOT NULL DEFAULT(0),
        [CreatedDate] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        
        CONSTRAINT [PK_ChartOfAccounts] PRIMARY KEY CLUSTERED ([AccountId] ASC),
        CONSTRAINT [UK_ChartOfAccounts_AccountCode] UNIQUE ([AccountCode]),
        CONSTRAINT [FK_ChartOfAccounts_AccountTypes] FOREIGN KEY([AccountTypeId]) 
            REFERENCES [dbo].[AccountTypes] ([AccountTypeId]),
        CONSTRAINT [FK_ChartOfAccounts_AccountGroups] FOREIGN KEY([AccountGroupId]) 
            REFERENCES [dbo].[AccountGroups] ([AccountGroupId]),
        CONSTRAINT [FK_ChartOfAccounts_ParentAccount] FOREIGN KEY([ParentAccountId]) 
            REFERENCES [dbo].[ChartOfAccounts] ([AccountId])
    );
    
    PRINT N'تم إنشاء جدول دليل الحسابات';
END
ELSE
BEGIN
    PRINT N'جدول دليل الحسابات موجود مسبقاً';
END
GO

-- إنشاء الفهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountCode')
    CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_AccountCode] 
    ON [dbo].[ChartOfAccounts] ([AccountCode] ASC);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountType')
    CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_AccountType] 
    ON [dbo].[ChartOfAccounts] ([AccountTypeId] ASC);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_AccountGroup')
    CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_AccountGroup] 
    ON [dbo].[ChartOfAccounts] ([AccountGroupId] ASC);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_ParentAccount')
    CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_ParentAccount] 
    ON [dbo].[ChartOfAccounts] ([ParentAccountId] ASC);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_Level')
    CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_Level] 
    ON [dbo].[ChartOfAccounts] ([AccountLevel] ASC);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChartOfAccounts_Active')
    CREATE NONCLUSTERED INDEX [IX_ChartOfAccounts_Active] 
    ON [dbo].[ChartOfAccounts] ([IsActive] ASC) WHERE [IsActive] = 1;

PRINT N'تم إنشاء الفهارس بنجاح';
GO
