using System;
using System.Drawing;
using System.Windows.Forms;
using Awqaf_Managment.UI.Forms.Accounting;

namespace Awqaf_Managment.UI.Forms
{
    /// <summary>
    /// نموذج اختبار رئيسي
    /// Test Main Form
    /// </summary>
    public partial class TestMainForm : Form
    {
        #region المنشئ - Constructor
        
        /// <summary>
        /// منشئ النموذج
        /// Form Constructor
        /// </summary>
        public TestMainForm()
        {
            InitializeComponent();
            SetupForm();
        }

        #endregion

        #region إعداد النموذج - Form Setup
        
        /// <summary>
        /// إعداد النموذج
        /// Setup Form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج
            this.Text = "نظام إدارة الأوقاف - الاختبار";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(248, 249, 250);

            // إنشاء القائمة الرئيسية
            CreateMainMenu();
            
            // إنشاء شريط الأدوات
            CreateToolBar();
            
            // إنشاء شريط الحالة
            CreateStatusBar();
        }

        /// <summary>
        /// إنشاء القائمة الرئيسية
        /// Create Main Menu
        /// </summary>
        private void CreateMainMenu()
        {
            var menuStrip = new MenuStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // قائمة المحاسبة
            var accountingMenu = new ToolStripMenuItem("المحاسبة");
            accountingMenu.DropDownItems.Add(new ToolStripMenuItem("دليل الحسابات", null, OpenChartOfAccounts));
            accountingMenu.DropDownItems.Add(new ToolStripSeparator());
            accountingMenu.DropDownItems.Add(new ToolStripMenuItem("القيود اليومية", null, (s, e) => MessageBox.Show("قريباً", "معلومات")));
            accountingMenu.DropDownItems.Add(new ToolStripMenuItem("التقارير المالية", null, (s, e) => MessageBox.Show("قريباً", "معلومات")));

            // قائمة النظام
            var systemMenu = new ToolStripMenuItem("النظام");
            systemMenu.DropDownItems.Add(new ToolStripMenuItem("إعدادات النظام", null, (s, e) => MessageBox.Show("قريباً", "معلومات")));
            systemMenu.DropDownItems.Add(new ToolStripMenuItem("إدارة المستخدمين", null, (s, e) => MessageBox.Show("قريباً", "معلومات")));
            systemMenu.DropDownItems.Add(new ToolStripSeparator());
            systemMenu.DropDownItems.Add(new ToolStripMenuItem("خروج", null, (s, e) => this.Close()));

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("حول البرنامج", null, ShowAbout));
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("دليل المستخدم", null, (s, e) => MessageBox.Show("قريباً", "معلومات")));

            menuStrip.Items.Add(accountingMenu);
            menuStrip.Items.Add(systemMenu);
            menuStrip.Items.Add(helpMenu);

            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        /// <summary>
        /// إنشاء شريط الأدوات
        /// Create Tool Bar
        /// </summary>
        private void CreateToolBar()
        {
            var toolStrip = new ToolStrip
            {
                RightToLeft = RightToLeft.Yes,
                ImageScalingSize = new Size(32, 32),
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // زر دليل الحسابات
            var chartOfAccountsButton = new ToolStripButton
            {
                Text = "دليل الحسابات",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageAboveText
            };
            chartOfAccountsButton.Click += OpenChartOfAccounts;

            // زر القيود اليومية
            var journalEntriesButton = new ToolStripButton
            {
                Text = "القيود اليومية",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageAboveText
            };
            journalEntriesButton.Click += (s, e) => MessageBox.Show("قريباً", "معلومات");

            // زر التقارير
            var reportsButton = new ToolStripButton
            {
                Text = "التقارير",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageAboveText
            };
            reportsButton.Click += (s, e) => MessageBox.Show("قريباً", "معلومات");

            toolStrip.Items.Add(chartOfAccountsButton);
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(journalEntriesButton);
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(reportsButton);

            this.Controls.Add(toolStrip);
        }

        /// <summary>
        /// إنشاء شريط الحالة
        /// Create Status Bar
        /// </summary>
        private void CreateStatusBar()
        {
            var statusStrip = new StatusStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            var statusLabel = new ToolStripStatusLabel
            {
                Text = "مرحباً بك في نظام إدارة الأوقاف",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var dateLabel = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd - dddd", new System.Globalization.CultureInfo("ar-SA"))
            };

            statusStrip.Items.Add(statusLabel);
            statusStrip.Items.Add(dateLabel);

            this.Controls.Add(statusStrip);
        }

        #endregion

        #region معالجات الأحداث - Event Handlers
        
        /// <summary>
        /// فتح نموذج دليل الحسابات
        /// Open Chart of Accounts Form
        /// </summary>
        private void OpenChartOfAccounts(object sender, EventArgs e)
        {
            try
            {
                var chartOfAccountsForm = new ChartOfAccountsManagementForm();
                chartOfAccountsForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج دليل الحسابات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error, 
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// عرض معلومات البرنامج
        /// Show About Information
        /// </summary>
        private void ShowAbout(object sender, EventArgs e)
        {
            var aboutMessage = @"نظام إدارة الأوقاف
الإصدار: 1.0.0
تاريخ الإصدار: 2024

نظام شامل لإدارة الأوقاف والمحاسبة
يتضمن:
• إدارة دليل الحسابات الهرمي
• نظام القيود المحاسبية
• التقارير المالية
• إدارة المستخدمين والصلاحيات

تم التطوير باستخدام:
• .NET Framework 4.8
• Windows Forms
• SQL Server
• ADO.NET";

            MessageBox.Show(aboutMessage, "حول البرنامج", 
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        #endregion

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // TestMainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.IsMdiContainer = true;
            this.Name = "TestMainForm";
            this.Text = "نظام إدارة الأوقاف - الاختبار";
            this.ResumeLayout(false);
        }

        #endregion
    }
}
